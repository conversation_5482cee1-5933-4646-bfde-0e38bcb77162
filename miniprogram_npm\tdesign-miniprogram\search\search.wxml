<wxs src="../common/utils.wxs" module="_"/><wxs src="./search.wxs" module="_this"></wxs><view style="{{_._style([style, customStyle])}}" class="class {{classPrefix}} {{prefix}}-class"><view class="{{classPrefix}}__input-box {{prefix}}-{{focus ? 'is-focused' : 'not-focused'}} {{classPrefix}}__input-box--{{center ? 'center' : ''}} {{classPrefix}}__input-box--{{shape}} {{prefix}}-class-input-container"><t-icon wx:if="{{leftIcon}}" name="{{leftIcon}}" class="{{prefix}}-icon {{prefix}}-class-left" aria-hidden="{{true}}"/><slot wx:else name="left-icon"/><input type="{{type}}" name="input" maxlength="{{maxlength}}" disabled="{{disabled || readonly}}" class="{{prefix}}-input__keyword {{prefix}}-class-input {{ disabled ? prefix + '-input--disabled' : ''}}" focus="{{focus}}" value="{{value}}" confirm-type="{{confirmType}}" confirm-hold="{{confirmHold}}" cursor="{{cursor}}" adjust-position="{{adjustPosition}}" always-embed="{{alwaysEmbed}}" selection-start="{{selectionStart}}" selection-end="{{selectionEnd}}" hold-keyboard="{{holdKeyboard}}" cursor-spacing="{{cursorSpacing}}" placeholder="{{placeholder}}" placeholder-style="{{placeholderStyle}}" placeholder-class="{{placeholderClass}} {{classPrefix}}__placeholder {{classPrefix}}__placeholder--{{center ? 'center': 'normal'}}" bind:input="onInput" bind:focus="onFocus" bind:blur="onBlur" bind:confirm="onConfirm"/><view wx:if="{{value !=='' && clearable && showClearIcon}}" class="{{classPrefix}}__clear hotspot-expanded {{prefix}}-class-clear" catch:tap="handleClear" aria-role="button" aria-label="清除"><t-icon name="close-circle-filled" size="inherit" color="inherit"/></view></view><view wx:if="{{action}}" class="{{classPrefix}}__search-action {{prefix}}-class-action" catch:tap="onActionClick" aria-role="button">{{action}}</view><slot wx:else name="action"/></view><view wx:if="{{isShowResultList && !isSelected}}" class="{{classPrefix}}__result-list" aria-role="listbox"><t-cell wx:for="{{resultList}}" wx:key="index" data-index="{{index}}" class="{{classPrefix}}__result-item" hover bind:tap="onSelectResultItem" aria-role="option"><rich-text slot="title" nodes="{{_this.highLight(item, value)}}"></rich-text></t-cell></view>