// test/risk-level-mapping-test.js
// 风险等级类名映射测试

const aiService = require('../utils/ai-service');

/**
 * 测试风险等级类名映射功能
 */
class RiskLevelMappingTest {
  constructor() {
    this.testResults = [];
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🎯 开始风险等级类名映射测试...\n');

    try {
      this.testRiskLevelClassMapping();
      this.testDefaultPersistenceOptionsMapping();
      this.testCSSClassNameValidation();
      
      this.printTestSummary();
    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error);
    }
  }

  /**
   * 测试风险等级类名映射
   */
  testRiskLevelClassMapping() {
    console.log('🔄 测试风险等级类名映射...');
    
    try {
      const testCases = [
        { input: '无风险', expected: 'none' },
        { input: '低风险', expected: 'low' },
        { input: '中风险', expected: 'medium' },
        { input: '高风险', expected: 'high' },
        { input: '未知风险', expected: 'medium' } // 默认值测试
      ];

      let allPassed = true;
      const results = [];

      for (const testCase of testCases) {
        const result = aiService.getRiskLevelClass(testCase.input);
        const passed = result === testCase.expected;
        
        results.push({
          input: testCase.input,
          expected: testCase.expected,
          actual: result,
          passed: passed
        });

        if (!passed) {
          allPassed = false;
        }
      }

      if (allPassed) {
        console.log('✅ 风险等级类名映射测试通过');
        console.log('   映射结果:');
        results.forEach(r => {
          console.log(`   "${r.input}" -> "${r.actual}"`);
        });
        this.addTestResult('风险等级类名映射', true, '所有映射正确');
      } else {
        const failedCases = results.filter(r => !r.passed);
        console.log('❌ 风险等级类名映射测试失败');
        failedCases.forEach(r => {
          console.log(`   "${r.input}": 期望 "${r.expected}", 实际 "${r.actual}"`);
        });
        this.addTestResult('风险等级类名映射', false, `${failedCases.length} 个映射错误`);
      }
    } catch (error) {
      console.log('❌ 风险等级类名映射测试出错:', error.message);
      this.addTestResult('风险等级类名映射', false, error.message);
    }
  }

  /**
   * 测试默认坚持机制选择的类名映射
   */
  testDefaultPersistenceOptionsMapping() {
    console.log('📋 测试默认坚持机制选择的类名映射...');
    
    try {
      const testContext = {
        voteResult: '深入调查',
        playerRole: '管家',
        currentInfluence: 65,
        roleStatus: '中等'
      };

      const options = aiService.getDefaultPersistenceOptions(testContext);
      
      if (!options || !options.persistenceOptions) {
        throw new Error('获取默认选择失败');
      }

      let allHaveRiskClass = true;
      const classResults = [];

      for (const option of options.persistenceOptions) {
        const hasRiskLevel = option.riskLevel !== undefined;
        const hasRiskLevelClass = option.riskLevelClass !== undefined;
        const classIsValid = hasRiskLevelClass && ['none', 'low', 'medium', 'high'].includes(option.riskLevelClass);

        classResults.push({
          name: option.name,
          riskLevel: option.riskLevel,
          riskLevelClass: option.riskLevelClass,
          valid: hasRiskLevel && hasRiskLevelClass && classIsValid
        });

        if (!classIsValid) {
          allHaveRiskClass = false;
        }
      }

      if (allHaveRiskClass) {
        console.log('✅ 默认坚持机制选择类名映射测试通过');
        console.log('   选择类名映射:');
        classResults.forEach(r => {
          console.log(`   "${r.name}": ${r.riskLevel} -> ${r.riskLevelClass}`);
        });
        this.addTestResult('默认选择类名映射', true, '所有选择都有正确的类名');
      } else {
        const invalidOptions = classResults.filter(r => !r.valid);
        console.log('❌ 默认坚持机制选择类名映射测试失败');
        invalidOptions.forEach(r => {
          console.log(`   "${r.name}": 类名无效或缺失`);
        });
        this.addTestResult('默认选择类名映射', false, `${invalidOptions.length} 个选择类名无效`);
      }
    } catch (error) {
      console.log('❌ 默认坚持机制选择类名映射测试出错:', error.message);
      this.addTestResult('默认选择类名映射', false, error.message);
    }
  }

  /**
   * 测试CSS类名有效性
   */
  testCSSClassNameValidation() {
    console.log('🎨 测试CSS类名有效性...');
    
    try {
      const validCSSClasses = ['none', 'low', 'medium', 'high'];
      const expectedCSSRules = [
        '.risk-none',
        '.risk-low', 
        '.risk-medium',
        '.risk-high'
      ];

      // 检查类名是否符合CSS命名规范
      const validationResults = validCSSClasses.map(className => {
        const isValidCSS = /^[a-zA-Z][a-zA-Z0-9-_]*$/.test(className);
        const hasNoChinese = !/[\u4e00-\u9fa5]/.test(className);
        
        return {
          className: className,
          isValidCSS: isValidCSS,
          hasNoChinese: hasNoChinese,
          valid: isValidCSS && hasNoChinese
        };
      });

      const allValid = validationResults.every(r => r.valid);

      if (allValid) {
        console.log('✅ CSS类名有效性测试通过');
        console.log('   有效的CSS类名:');
        expectedCSSRules.forEach(rule => {
          console.log(`   ${rule}`);
        });
        this.addTestResult('CSS类名有效性', true, '所有类名符合CSS规范');
      } else {
        const invalidClasses = validationResults.filter(r => !r.valid);
        console.log('❌ CSS类名有效性测试失败');
        invalidClasses.forEach(r => {
          console.log(`   "${r.className}": 不符合CSS规范`);
        });
        this.addTestResult('CSS类名有效性', false, `${invalidClasses.length} 个类名无效`);
      }
    } catch (error) {
      console.log('❌ CSS类名有效性测试出错:', error.message);
      this.addTestResult('CSS类名有效性', false, error.message);
    }
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, success, message) {
    this.testResults.push({
      name: testName,
      success: success,
      message: message,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 打印测试总结
   */
  printTestSummary() {
    console.log('\n📋 风险等级类名映射测试总结:');
    console.log('=' * 50);
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(result => result.success).length;
    const failedTests = totalTests - passedTests;

    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests} ✅`);
    console.log(`失败: ${failedTests} ❌`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    console.log('\n详细结果:');
    this.testResults.forEach((result, index) => {
      const status = result.success ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.name}: ${result.message}`);
    });

    if (passedTests === totalTests) {
      console.log('\n🎉 所有风险等级类名映射测试通过！WXSS编译错误已修复。');
    } else {
      console.log('\n⚠️  部分测试失败，请检查相关映射逻辑。');
    }

    // 提供修复建议
    console.log('\n💡 最佳实践建议:');
    console.log('1. 始终使用英文作为CSS类名，避免中文字符');
    console.log('2. 使用映射对象将中文值转换为英文CSS类名');
    console.log('3. 在数据生成时就添加类名映射，而不是在模板中处理');
    console.log('4. 定期运行测试验证类名映射的正确性');
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const test = new RiskLevelMappingTest();
  test.runAllTests().catch(console.error);
}

module.exports = RiskLevelMappingTest;
