// test/simple-api-test.js
// 最简单的API测试

/**
 * 简单API测试类
 */
class SimpleApiTest {
  constructor() {
    this.apiKey = 'sk-rFun7AywY7jUUdJAtUBbFD';
    this.baseUrl = 'https://api.moonshot.cn/v1';
    this.model = 'moonshot-v1-8k';
    this.lastRequestTime = 0;
    this.minInterval = 3000; // 最小请求间隔3秒
  }

  /**
   * 频率控制 - 确保请求间隔
   */
  async waitForRateLimit() {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    if (timeSinceLastRequest < this.minInterval) {
      const waitTime = this.minInterval - timeSinceLastRequest;
      console.log(`⏱️ 频率控制等待 ${waitTime}ms...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    this.lastRequestTime = Date.now();
  }

  /**
   * 最基础的API测试
   */
  async testBasicApi() {
    console.log('🧪 开始最基础的API测试...');

    // 应用频率控制
    await this.waitForRateLimit();

    return new Promise((resolve) => {
      const requestData = {
        model: this.model,
        messages: [
          {
            role: 'system',
            content: 'You are Kimi, an AI assistant provided by Moonshot AI. You are proficient in Chinese and English conversations. You provide users with safe, helpful, and accurate answers. You will reject any questions involving terrorism, racism, or explicit content. Moonshot AI is a proper noun and should not be translated.'
          },
          {
            role: 'user',
            content: 'Hello, what is 1+1?'
          }
        ],
        temperature: 0.6,
        max_tokens: 100
      };

      console.log('📤 发送请求数据:', JSON.stringify(requestData, null, 2));
      console.log('🔑 使用API密钥:', this.apiKey);
      console.log('🌐 请求地址:', `${this.baseUrl}/chat/completions`);

      wx.request({
        url: `${this.baseUrl}/chat/completions`,
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        data: requestData,
        timeout: 30000,
        success: (res) => {
          console.log('✅ API请求成功!');
          console.log('📥 响应状态码:', res.statusCode);
          console.log('📥 响应头:', res.header);
          console.log('📥 响应数据:', res.data);
          
          const result = {
            success: true,
            statusCode: res.statusCode,
            data: res.data,
            message: 'API请求成功'
          };

          if (res.statusCode === 200 && res.data && res.data.choices) {
            result.aiResponse = res.data.choices[0]?.message?.content;
            result.message = `API响应正常: ${result.aiResponse}`;
          }

          resolve(result);
        },
        fail: (error) => {
          console.log('❌ API请求失败!');
          console.log('📥 错误信息:', error);
          
          let message = 'API请求失败';
          if (error.errMsg.includes('url not in domain list')) {
            message = '域名未添加到白名单';
          } else if (error.errMsg.includes('网络')) {
            message = '网络连接问题';
          }

          resolve({
            success: false,
            statusCode: 0,
            error: error,
            message: message
          });
        }
      });
    });
  }

  /**
   * 测试不同的请求格式
   */
  async testDifferentFormats() {
    console.log('🔄 测试不同的请求格式...');
    
    const formats = [
      {
        name: '标准格式',
        data: {
          model: this.model,
          messages: [
            { role: 'user', content: 'Hello' }
          ],
          temperature: 0.6
        }
      },
      {
        name: '带系统提示',
        data: {
          model: this.model,
          messages: [
            { 
              role: 'system', 
              content: 'You are Kimi, an AI assistant provided by Moonshot AI.'
            },
            { role: 'user', content: 'Hello' }
          ],
          temperature: 0.6
        }
      },
      {
        name: '最小格式',
        data: {
          model: this.model,
          messages: [{ role: 'user', content: 'Hi' }]
        }
      }
    ];

    const results = [];

    for (let i = 0; i < formats.length; i++) {
      const format = formats[i];
      console.log(`\n🧪 测试${format.name}...`);

      // 如果不是第一个请求，等待3秒避免频率限制
      if (i > 0) {
        console.log('⏱️ 等待3秒避免频率限制...');
        await new Promise(resolve => setTimeout(resolve, 3000));
      }

      const result = await new Promise((resolve) => {
        wx.request({
          url: `${this.baseUrl}/chat/completions`,
          method: 'POST',
          header: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
          },
          data: format.data,
          timeout: 15000,
          success: (res) => {
            console.log(`✅ ${format.name}成功: ${res.statusCode}`);
            resolve({
              format: format.name,
              success: true,
              statusCode: res.statusCode,
              hasResponse: !!(res.data && res.data.choices)
            });
          },
          fail: (error) => {
            console.log(`❌ ${format.name}失败:`, error.errMsg);
            resolve({
              format: format.name,
              success: false,
              error: error.errMsg
            });
          }
        });
      });
      
      results.push(result);
    }

    return results;
  }

  /**
   * 运行完整测试
   */
  async runFullTest() {
    console.log('🚀 开始完整的API测试...\n');
    
    const results = {
      basicTest: await this.testBasicApi(),
      formatTests: await this.testDifferentFormats(),
      timestamp: new Date().toISOString()
    };

    console.log('\n📊 测试结果总结:');
    console.log('================');
    
    // 基础测试结果
    console.log('\n1. 基础API测试:');
    if (results.basicTest.success) {
      console.log('   ✅ 基础测试通过');
      console.log(`   状态码: ${results.basicTest.statusCode}`);
      if (results.basicTest.aiResponse) {
        console.log(`   AI响应: ${results.basicTest.aiResponse.substring(0, 50)}...`);
      }
    } else {
      console.log('   ❌ 基础测试失败');
      console.log(`   错误: ${results.basicTest.message}`);
    }

    // 格式测试结果
    console.log('\n2. 格式测试结果:');
    results.formatTests.forEach(test => {
      const status = test.success ? '✅' : '❌';
      console.log(`   ${status} ${test.format}: ${test.success ? test.statusCode : test.error}`);
    });

    // 生成建议
    console.log('\n💡 诊断建议:');
    if (results.basicTest.success) {
      console.log('   🎉 API配置完全正确！可以正常使用AI功能。');
    } else if (results.basicTest.message.includes('域名')) {
      console.log('   ⚠️  请在微信小程序后台配置域名白名单');
    } else if (results.basicTest.statusCode === 401) {
      console.log('   ⚠️  API密钥认证失败，请检查密钥是否正确');
    } else {
      console.log('   ⚠️  其他问题，请检查网络连接和API服务状态');
    }

    return results;
  }
}

module.exports = SimpleApiTest;
