<!-- pages/cloud-debug/cloud-debug.wxml -->
<view class="container">
  <view class="header">
    <text class="title">🔧 云开发调试工具</text>
    <text class="subtitle">用于诊断和修复云开发问题</text>
  </view>

  <view class="section">
    <view class="section-title">环境信息</view>
    <view class="info-item">
      <text class="label">当前环境:</text>
      <text class="value">{{currentEnv || '未初始化'}}</text>
    </view>
    <view class="info-item">
      <text class="label">可用环境:</text>
      <view class="env-list">
        <text wx:for="{{envList}}" wx:key="*this" class="env-item">{{item}}</text>
      </view>
    </view>
  </view>

  <view class="section">
    <view class="section-title">操作按钮</view>
    <view class="button-group">
      <button 
        class="btn primary" 
        bindtap="manualInit"
        loading="{{isLoading}}"
        disabled="{{isLoading}}"
      >
        {{isLoading ? '测试中...' : '手动初始化云开发'}}
      </button>
      
      <button 
        class="btn secondary" 
        bindtap="createCloudFunction"
        disabled="{{!currentEnv}}"
      >
        创建云函数
      </button>
      
      <button 
        class="btn outline" 
        bindtap="clearResults"
      >
        清除结果
      </button>
    </view>
  </view>

  <view class="section">
    <view class="section-title">测试结果 ({{testResults.length}})</view>
    
    <view wx:if="{{testResults.length === 0}}" class="empty-state">
      <text>暂无测试结果，点击"手动初始化云开发"开始测试</text>
    </view>
    
    <view wx:else class="results-list">
      <view 
        wx:for="{{testResults}}" 
        wx:key="id" 
        class="result-item"
        bindtap="copyResult"
        data-result="{{item}}"
      >
        <view class="result-header">
          <text class="result-title">{{item.title}}</text>
          <text class="result-time">{{item.timestamp}}</text>
        </view>
        <view class="result-message">{{item.message}}</view>
        <view wx:if="{{item.data}}" class="result-data">
          <text>{{item.data}}</text>
        </view>
      </view>
    </view>
  </view>

  <view class="section">
    <view class="section-title">使用说明</view>
    <view class="help-text">
      <text>1. 点击"手动初始化云开发"测试环境连接</text>
      <text>2. 如果测试成功，云开发环境就可以使用了</text>
      <text>3. 点击"创建云函数"获取控制台链接</text>
      <text>4. 点击任意测试结果可复制详细信息</text>
    </view>
  </view>
</view>
