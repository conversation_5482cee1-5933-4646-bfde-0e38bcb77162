# 单人测试模式流程说明

## 完整流程

### 1. 创建房间
- 在主页点击"创建房间"
- 填写房间信息（房间名称、最大人数等）
- 点击"创建房间"按钮
- 系统自动跳转到房间大厅

### 2. 进入房间大厅
- 显示房间信息和玩家座位
- 房主可以看到"开始游戏"按钮（默认禁用）
- 右上角有"更多"菜单按钮

### 3. 打开测试功能面板
- 点击右上角的"更多"按钮（三个点图标）
- 在弹出菜单中选择"🧪 测试功能"
- 打开测试功能面板

### 4. 开启单人测试模式
- 在测试功能面板中找到"单人测试模式"选项
- 切换开关到开启状态
- 系统自动添加AI测试玩家
- 显示"已开启单人测试模式"提示

### 5. 开始测试游戏
- 方式1: 在测试面板中点击"开始测试游戏"按钮
- 方式2: 关闭测试面板，点击大厅中的"开始测试游戏"按钮
- 系统自动生成AI剧本（如果开启了AI剧本生成）
- 跳转到角色分配页面

## 功能特性

### 🧪 测试功能面板
- **单人测试模式**: 允许单人开始游戏，自动添加AI玩家
- **快速开始模式**: 跳过等待，立即开始游戏
- **AI剧本生成**: 使用AI自动生成游戏剧本

### 👤 单人测试模式详情
当开启单人测试模式时：
- ✅ 可以单人开始游戏
- ✅ 自动添加AI测试玩家（艾米丽、詹姆斯、莉莉安、维克多、索菲亚）
- ✅ 跳过准备等待阶段
- ✅ 所有AI玩家自动设为准备状态

### 🤖 AI测试玩家
系统会自动添加以下AI测试玩家：
- AI-艾米丽
- AI-詹姆斯  
- AI-莉莉安
- AI-维克多
- AI-索菲亚

## 技术实现

### 数据结构
```javascript
testMode: {
  singlePlayer: false,  // 单人测试模式
  quickStart: false,    // 快速开始模式
  aiScript: true        // AI剧本生成
}
```

### 核心方法
- `openTestPanel()`: 打开测试功能面板
- `onSinglePlayerModeChange()`: 切换单人测试模式
- `addAITestPlayers()`: 添加AI测试玩家
- `startTestGame()`: 开始测试游戏

### 流程控制
```javascript
// 开始游戏时检查测试模式
startGame() {
  if (this.data.testMode.singlePlayer) {
    this.startTestGame();
    return;
  }
  // 正常游戏流程...
}
```

## 用户界面

### 测试功能面板
- **位置**: 底部弹出面板
- **触发**: 房间菜单 → "🧪 测试功能"
- **内容**: 
  - 单人测试模式开关
  - 快速开始模式开关
  - AI剧本生成开关
  - 重置设置按钮
  - 开始测试游戏按钮

### 视觉反馈
- 开启单人模式时显示成功提示
- 自动添加AI玩家到座位
- 开始游戏按钮文字变为"开始测试游戏"
- 按钮状态根据测试模式动态调整

## 使用场景

### 开发测试
- 快速验证游戏流程
- 测试角色分配功能
- 验证AI剧本生成
- 调试游戏逻辑

### 演示展示
- 单人演示完整游戏流程
- 展示AI功能特性
- 快速体验游戏内容

### 功能验证
- 验证房间创建流程
- 测试玩家管理功能
- 检查页面跳转逻辑
- 确认数据存储正确

## 注意事项

### 限制条件
- 只有房主可以开启测试模式
- 单人测试模式下会自动添加AI玩家
- AI玩家不会真实参与游戏互动

### 数据处理
- AI玩家数据存储在房间管理器中
- 测试模式设置仅在当前会话有效
- 剧本数据正常保存到本地存储

### 兼容性
- 测试模式与正常游戏模式兼容
- 可以随时关闭测试模式
- 不影响正常的多人游戏功能

## 故障排除

### 常见问题

1. **测试面板打不开**
   - 检查是否点击了正确的菜单项
   - 确认页面没有其他弹窗遮挡

2. **单人模式开关无效**
   - 检查是否为房主身份
   - 确认房间状态正常

3. **AI玩家添加失败**
   - 检查房间是否已满
   - 确认房间管理器工作正常

4. **开始游戏失败**
   - 检查测试模式是否正确开启
   - 确认剧本生成功能正常

### 调试方法
- 查看控制台日志输出
- 检查房间数据状态
- 验证本地存储数据
- 测试页面跳转逻辑

## 扩展功能

### 未来改进
- 支持自定义AI玩家数量
- 添加更多测试选项
- 支持测试模式配置保存
- 增加测试数据导出功能

### 配置选项
```javascript
const testConfig = {
  aiPlayerCount: 5,        // AI玩家数量
  autoReady: true,         // 自动准备
  skipWaiting: true,       // 跳过等待
  debugMode: false         // 调试模式
};
```

## 总结

单人测试模式提供了完整的测试流程：

✅ **简单易用**: 三步即可开启测试模式
✅ **功能完整**: 支持完整的游戏流程测试
✅ **智能化**: 自动添加AI玩家和生成剧本
✅ **灵活配置**: 多种测试选项可自由组合
✅ **开发友好**: 便于开发调试和功能验证

这个实现完美解决了单人测试的需求，让开发者和用户都能轻松体验完整的游戏流程。
