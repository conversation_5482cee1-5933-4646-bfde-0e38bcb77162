/* pages/api-test/api-test.wxss */
.container {
  padding: 40rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

/* 状态卡片 */
.status-card {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.status-header {
  margin-bottom: 30rpx;
}

.status-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.status-items {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-size: 28rpx;
  color: #666;
}

.status-value {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.status-value.success {
  background-color: #d4edda;
  color: #155724;
}

.status-value.error {
  background-color: #f8d7da;
  color: #721c24;
}

.status-value.unknown {
  background-color: #e2e3e5;
  color: #6c757d;
}

/* 测试按钮 */
.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.test-btn {
  padding: 30rpx;
  border: none;
  border-radius: 15rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: white;
}

.test-btn.primary {
  background-color: #007bff;
}

.test-btn.primary[disabled] {
  background-color: #6c757d;
}

.test-btn.secondary {
  background-color: #28a745;
}

.test-btn.secondary[disabled] {
  background-color: #6c757d;
}

.test-btn.settings {
  background-color: #17a2b8;
}

/* 测试结果 */
.result-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.result-actions {
  display: flex;
  gap: 10rpx;
}

.action-btn {
  padding: 10rpx 20rpx;
  background-color: #f8f9fa;
  color: #6c757d;
  border: 1rpx solid #dee2e6;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.result-content {
  background-color: #f8f9fa;
  border-radius: 10rpx;
  padding: 30rpx;
  border: 1rpx solid #e9ecef;
}

.result-text {
  font-family: 'Courier New', monospace;
  font-size: 24rpx;
  line-height: 1.6;
  color: #333;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 使用说明 */
.instructions {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.instruction-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.instruction-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.instruction-item {
  display: flex;
  align-items: flex-start;
}

.instruction-number {
  width: 50rpx;
  height: 50rpx;
  background-color: #007bff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  font-weight: bold;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.instruction-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
  padding-top: 8rpx;
}

/* 加载覆盖层 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: white;
  border-radius: 20rpx;
  padding: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #333;
}
