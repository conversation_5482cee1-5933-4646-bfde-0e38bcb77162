// 历史记录页面逻辑
const api = require('../../utils/api');
const common = require('../../utils/common');

Page({
  data: {
    activeTab: 'games',
    showGameDetail: false,
    selectedGame: null,
    gameHistory: [
      {
        id: 1,
        gameName: '🏰 雾夜庄园疑案',
        role: '私人秘书',
        result: 'win',
        influenceChange: '+25',
        timeAgo: '2小时前',
        duration: '45分钟',
        playerCount: 6,
        tags: ['推理正确', 'MVP', '团队合作']
      },
      {
        id: 2,
        gameName: '🏫 校园悬案',
        role: '学生会长',
        result: 'lose',
        influenceChange: '-5',
        timeAgo: '1天前',
        duration: '38分钟',
        playerCount: 5,
        tags: ['推理错误', '被投出局']
      },
      {
        id: 3,
        gameName: '🏛️ 古堡疑云',
        role: '管家',
        result: 'draw',
        influenceChange: '+10',
        timeAgo: '3天前',
        duration: '52分钟',
        playerCount: 8,
        tags: ['时间耗尽', '坚持选择']
      }
    ],
    achievements: [
      {
        id: 1,
        name: '推理大师',
        description: '连续推理正确5次',
        icon: '🕵️',
        unlocked: true,
        progress: 100,
        current: 5,
        target: 5
      },
      {
        id: 2,
        name: '影响力之王',
        description: '达到1000影响力',
        icon: '👑',
        unlocked: false,
        progress: 35,
        current: 350,
        target: 1000
      },
      {
        id: 3,
        name: '团队合作',
        description: '与好友合作获胜10次',
        icon: '🤝',
        unlocked: true,
        progress: 100,
        current: 10,
        target: 10
      },
      {
        id: 4,
        name: '真心话达人',
        description: '获得50次真心话好评',
        icon: '💕',
        unlocked: false,
        progress: 60,
        current: 30,
        target: 50
      }
    ],
    friendInteractions: [
      {
        id: 1,
        friend: {
          id: 'user1',
          nickname: '张三',
          avatar: ''
        },
        type: 'like',
        description: '在"雾夜庄园疑案"中给你点赞',
        timeAgo: '刚刚'
      },
      {
        id: 2,
        friend: {
          id: 'user2',
          nickname: '李四',
          avatar: ''
        },
        type: 'invite',
        description: '邀请你加入新游戏"神秘岛屿"',
        timeAgo: '10分钟前'
      },
      {
        id: 3,
        friend: {
          id: 'user3',
          nickname: '王五',
          avatar: ''
        },
        type: 'comment',
        description: '评论了你的真心话回答',
        timeAgo: '1小时前'
      }
    ]
  },

  onLoad() {
    this.loadHistoryData();
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData();
  },

  onPullDownRefresh() {
    this.loadHistoryData().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载历史数据
  async loadHistoryData() {
    try {
      common.showLoading('加载中...');
      
      // 这里应该调用实际的API
      // const [gameRes, achievementRes, friendRes] = await Promise.all([
      //   api.getUserHistory(),
      //   api.getUserAchievements(),
      //   api.getFriendInteractions()
      // ]);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // this.setData({
      //   gameHistory: gameRes.data,
      //   achievements: achievementRes.data,
      //   friendInteractions: friendRes.data
      // });
      
      common.hideLoading();
    } catch (error) {
      console.error('加载历史数据失败:', error);
      common.hideLoading();
      this.showToast('加载失败，请重试');
    }
  },

  // 刷新数据
  async refreshData() {
    try {
      // 静默刷新，不显示loading
      // const res = await api.getUserHistory();
      // this.setData({ gameHistory: res.data });
    } catch (error) {
      console.error('刷新数据失败:', error);
    }
  },

  // 标签页切换
  onTabChange(e) {
    this.setData({ activeTab: e.detail.value });
    
    // 根据不同标签页加载对应数据
    switch (e.detail.value) {
      case 'games':
        this.loadGameHistory();
        break;
      case 'achievements':
        this.loadAchievements();
        break;
      case 'friends':
        this.loadFriendInteractions();
        break;
    }
  },

  // 加载游戏历史
  async loadGameHistory() {
    try {
      // const res = await api.getUserHistory();
      // this.setData({ gameHistory: res.data });
    } catch (error) {
      console.error('加载游戏历史失败:', error);
    }
  },

  // 加载成就数据
  async loadAchievements() {
    try {
      // const res = await api.getUserAchievements();
      // this.setData({ achievements: res.data });
    } catch (error) {
      console.error('加载成就数据失败:', error);
    }
  },

  // 加载好友互动
  async loadFriendInteractions() {
    try {
      // const res = await api.getFriendInteractions();
      // this.setData({ friendInteractions: res.data });
    } catch (error) {
      console.error('加载好友互动失败:', error);
    }
  },

  // 查看游戏详情
  viewGameDetail(e) {
    const gameId = e.currentTarget.dataset.gameId;
    const game = this.data.gameHistory.find(g => g.id === gameId);
    
    if (game) {
      this.setData({
        selectedGame: game,
        showGameDetail: true
      });
    }
  },

  // 游戏详情弹窗显示状态变化
  onGameDetailVisibleChange(e) {
    this.setData({ showGameDetail: e.detail.visible });
  },

  // 关闭游戏详情
  closeGameDetail() {
    this.setData({ showGameDetail: false });
  },

  // 查看成就详情
  viewAchievementDetail(e) {
    const achievementId = e.currentTarget.dataset.achievementId;
    const achievement = this.data.achievements.find(a => a.id === achievementId);
    
    if (achievement) {
      let content = `${achievement.description}\n\n`;
      if (achievement.unlocked) {
        content += '🎉 恭喜你已获得此成就！';
      } else {
        content += `进度：${achievement.current}/${achievement.target}\n完成度：${achievement.progress}%`;
      }
      
      wx.showModal({
        title: achievement.name,
        content,
        showCancel: false
      });
    }
  },

  // 查看好友资料
  viewFriendProfile(friendId) {
    wx.navigateTo({
      url: `/pages/friend-profile/friend-profile?friendId=${friendId}`
    });
  },

  // 回复好友互动
  replyToFriend(friendId, interactionId) {
    wx.navigateTo({
      url: `/pages/chat/chat?friendId=${friendId}&interactionId=${interactionId}`
    });
  },

  // 分享成就
  shareAchievement(achievement) {
    const shareContent = `我在AI推理游戏中获得了"${achievement.name}"成就！${achievement.description}`;
    
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
    
    // 复制分享内容到剪贴板
    common.copyToClipboard(shareContent);
  },

  // 重新挑战游戏
  replayGame(gameId) {
    wx.showModal({
      title: '重新挑战',
      content: '是否要创建相同设置的新房间？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: `/pages/room-create/room-create?templateGameId=${gameId}`
          });
        }
      }
    });
  },

  // 查看游戏回放
  viewGameReplay(gameId) {
    wx.navigateTo({
      url: `/pages/game-replay/game-replay?gameId=${gameId}`
    });
  },

  // 显示提示
  showToast(message, theme = 'warning') {
    const toast = this.selectComponent('#t-toast');
    toast.showToast({
      theme,
      message,
      duration: 2000
    });
  }
});
