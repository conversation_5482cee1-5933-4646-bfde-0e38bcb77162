// 完整流程测试页面
const roomManager = require('../../utils/room-manager');
const aiService = require('../../utils/ai-service-simple');

Page({
  data: {
    testSteps: [
      { id: 1, name: '创建房间', status: 'pending', desc: '创建测试房间' },
      { id: 2, name: '添加玩家', status: 'pending', desc: '添加AI测试玩家' },
      { id: 3, name: '开启测试模式', status: 'pending', desc: '启用单人测试模式' },
      { id: 4, name: '生成剧本', status: 'pending', desc: 'AI生成游戏剧本' },
      { id: 5, name: '角色分配', status: 'pending', desc: '分配角色给玩家' },
      { id: 6, name: '验证结果', status: 'pending', desc: '验证整个流程' }
    ],
    currentStep: 0,
    testResult: null,
    roomId: '',
    isRunning: false
  },

  onLoad() {
    console.log('🧪 完整流程测试页面加载');
  },

  // 开始完整流程测试
  async startCompleteTest() {
    if (this.data.isRunning) return;

    this.setData({ 
      isRunning: true,
      currentStep: 0
    });

    try {
      // 步骤1: 创建房间
      await this.executeStep(1, async () => {
        const roomResult = roomManager.createRoom({
          gameMode: '经典推理模式',
          maxPlayers: 6,
          timeLimit: 600,
          rounds: 3
        });
        
        this.setData({ roomId: roomResult.roomId });
        return `房间创建成功: ${roomResult.roomId}`;
      });

      // 步骤2: 添加玩家
      await this.executeStep(2, async () => {
        const testPlayers = [
          { id: 'ai_player_2', nickname: 'AI-艾米丽', avatar: '', isReady: true },
          { id: 'ai_player_3', nickname: 'AI-詹姆斯', avatar: '', isReady: true },
          { id: 'ai_player_4', nickname: 'AI-莉莉安', avatar: '', isReady: true },
          { id: 'ai_player_5', nickname: 'AI-维克多', avatar: '', isReady: true },
          { id: 'ai_player_6', nickname: 'AI-索菲亚', avatar: '', isReady: true }
        ];

        for (const player of testPlayers) {
          roomManager.joinRoom(this.data.roomId, player);
        }

        const room = roomManager.getRoomInfo(this.data.roomId);
        return `添加了${room.players.size}个玩家`;
      });

      // 步骤3: 开启测试模式
      await this.executeStep(3, async () => {
        // 模拟开启单人测试模式
        return '单人测试模式已开启';
      });

      // 步骤4: 生成剧本
      await this.executeStep(4, async () => {
        const scriptData = await aiService.generateScript({
          storyType: 'mystery',
          playerCount: 6,
          difficulty: 'medium',
          theme: '神秘庄园'
        });

        // 保存剧本到本地存储
        wx.setStorageSync(`script_${this.data.roomId}`, scriptData);
        
        return `剧本生成成功: ${scriptData.storyInfo?.title || '未知标题'}`;
      });

      // 步骤5: 角色分配
      await this.executeStep(5, async () => {
        const room = roomManager.getRoomInfo(this.data.roomId);
        const playerIds = Array.from(room.players.keys());
        const scriptData = wx.getStorageSync(`script_${this.data.roomId}`);
        
        const assignmentResult = await aiService.generateRoleAssignment(scriptData, playerIds);
        
        // 保存角色分配结果
        wx.setStorageSync(`role_assignment_${this.data.roomId}`, assignmentResult);
        
        return `角色分配成功: ${assignmentResult.assignments.length}个角色`;
      });

      // 步骤6: 验证结果
      await this.executeStep(6, async () => {
        const room = roomManager.getRoomInfo(this.data.roomId);
        const scriptData = wx.getStorageSync(`script_${this.data.roomId}`);
        const assignmentResult = wx.getStorageSync(`role_assignment_${this.data.roomId}`);

        // 调试信息
        console.log('🔍 验证数据:');
        console.log('房间数据:', room);
        console.log('剧本数据:', scriptData);
        console.log('角色分配:', assignmentResult);

        // 验证数据完整性
        const validations = [
          {
            name: '房间数据',
            valid: !!room && room.players.size > 0,
            detail: `房间存在: ${!!room}, 玩家数量: ${room?.players?.size || 0}`
          },
          {
            name: '剧本数据',
            valid: !!scriptData && !!scriptData.storyInfo,
            detail: `剧本存在: ${!!scriptData}, storyInfo存在: ${!!scriptData?.storyInfo}, 标题: ${scriptData?.storyInfo?.title || '无'}`
          },
          {
            name: '角色分配',
            valid: !!assignmentResult && !!assignmentResult.assignments && assignmentResult.assignments.length > 0,
            detail: `分配结果存在: ${!!assignmentResult}, assignments存在: ${!!assignmentResult?.assignments}, 数量: ${assignmentResult?.assignments?.length || 0}`
          },
          {
            name: '数据一致性',
            valid: room && assignmentResult && room.players.size === assignmentResult.assignments?.length,
            detail: `玩家数: ${room?.players?.size || 0}, 角色数: ${assignmentResult?.assignments?.length || 0}`
          }
        ];

        const allValid = validations.every(v => v.valid);
        const invalidItems = validations.filter(v => !v.valid);

        if (!allValid) {
          console.error('❌ 验证失败详情:');
          invalidItems.forEach(item => {
            console.error(`- ${item.name}: ${item.detail}`);
          });
          throw new Error(`验证失败: ${invalidItems.map(v => v.name).join(', ')}`);
        }

        return '所有验证通过，流程完整';
      });

      // 设置测试结果
      this.setData({
        testResult: {
          success: true,
          message: '完整流程测试通过！',
          roomId: this.data.roomId,
          details: {
            roomPlayers: roomManager.getRoomInfo(this.data.roomId).players.size,
            scriptTitle: wx.getStorageSync(`script_${this.data.roomId}`)?.storyInfo?.title,
            roleCount: wx.getStorageSync(`role_assignment_${this.data.roomId}`)?.assignments?.length
          }
        }
      });

    } catch (error) {
      console.error('❌ 完整流程测试失败:', error);
      this.setData({
        testResult: {
          success: false,
          message: `测试失败: ${error.message}`,
          error: error
        }
      });
    } finally {
      this.setData({ isRunning: false });
    }
  },

  // 执行单个测试步骤
  async executeStep(stepId, stepFunction) {
    console.log(`🔄 执行步骤${stepId}...`);
    
    // 更新步骤状态为进行中
    this.updateStepStatus(stepId, 'running');
    this.setData({ currentStep: stepId });

    try {
      // 执行步骤函数
      const result = await stepFunction();
      
      // 更新步骤状态为成功
      this.updateStepStatus(stepId, 'success', result);
      
      // 等待一下让用户看到进度
      await new Promise(resolve => setTimeout(resolve, 500));
      
      console.log(`✅ 步骤${stepId}完成:`, result);
      
    } catch (error) {
      // 更新步骤状态为失败
      this.updateStepStatus(stepId, 'failed', error.message);
      throw error;
    }
  },

  // 更新步骤状态
  updateStepStatus(stepId, status, message = '') {
    const steps = this.data.testSteps.map(step => {
      if (step.id === stepId) {
        return {
          ...step,
          status: status,
          result: message
        };
      }
      return step;
    });

    this.setData({ testSteps: steps });
  },

  // 重置测试
  resetTest() {
    const resetSteps = this.data.testSteps.map(step => ({
      ...step,
      status: 'pending',
      result: ''
    }));

    this.setData({
      testSteps: resetSteps,
      currentStep: 0,
      testResult: null,
      roomId: '',
      isRunning: false
    });
  },

  // 查看测试详情
  viewTestDetails() {
    if (!this.data.testResult) {
      wx.showToast({
        title: '请先运行测试',
        icon: 'none'
      });
      return;
    }

    const result = this.data.testResult;
    let content = `测试结果: ${result.success ? '成功' : '失败'}\n`;
    
    if (result.success && result.details) {
      content += `房间ID: ${result.roomId}\n`;
      content += `玩家数量: ${result.details.roomPlayers}\n`;
      content += `剧本标题: ${result.details.scriptTitle}\n`;
      content += `角色数量: ${result.details.roleCount}`;
    } else if (result.error) {
      content += `错误信息: ${result.message}`;
    }

    wx.showModal({
      title: '测试详情',
      content: content,
      showCancel: false
    });
  },

  // 进入房间大厅
  goToRoomLobby() {
    if (!this.data.roomId) {
      wx.showToast({
        title: '请先运行测试',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/room-lobby/room-lobby?roomId=${this.data.roomId}&testMode=single`
    });
  },

  // 进入角色分配页面
  goToRoleAssignment() {
    if (!this.data.roomId) {
      wx.showToast({
        title: '请先运行测试',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/role-assignment/role-assignment?roomId=${this.data.roomId}`
    });
  },

  // 进入线索收集页面
  goToCluesCollection() {
    if (!this.data.roomId) {
      wx.showToast({
        title: '请先运行测试',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/clues-collection/clues-collection?roomId=${this.data.roomId}`
    });
  }
});
