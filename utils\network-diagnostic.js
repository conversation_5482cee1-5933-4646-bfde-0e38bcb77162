// utils/network-diagnostic.js
// 网络诊断工具

class NetworkDiagnostic {
  constructor() {
    this.diagnosticResults = [];
  }

  /**
   * 运行完整的网络诊断
   * @returns {Promise<Object>} 诊断结果
   */
  async runFullDiagnostic() {
    console.log('🔍 开始网络诊断...');
    
    const results = {
      timestamp: new Date().toISOString(),
      networkStatus: await this.checkNetworkStatus(),
      domainReachability: await this.checkDomainReachability(),
      apiEndpoint: await this.checkApiEndpoint(),
      requestHeaders: this.checkRequestHeaders(),
      recommendations: []
    };

    // 生成建议
    results.recommendations = this.generateRecommendations(results);
    
    console.log('📋 网络诊断完成:', results);
    return results;
  }

  /**
   * 检查网络状态
   * @returns {Promise<Object>} 网络状态信息
   */
  async checkNetworkStatus() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          console.log('📶 网络类型:', res.networkType);
          resolve({
            networkType: res.networkType,
            isConnected: res.networkType !== 'none',
            isWifi: res.networkType === 'wifi',
            isMobile: ['2g', '3g', '4g', '5g'].includes(res.networkType)
          });
        },
        fail: () => {
          resolve({
            networkType: 'unknown',
            isConnected: false,
            error: '无法获取网络状态'
          });
        }
      });
    });
  }

  /**
   * 检查域名可达性
   * @returns {Promise<Object>} 域名检查结果
   */
  async checkDomainReachability() {
    const domains = [
      'https://api.moonshot.cn', // 使用正确的API域名
      'https://www.baidu.com', // 作为对照组
    ];

    const results = {};

    for (const domain of domains) {
      try {
        const result = await this.pingDomain(domain);
        results[domain] = result;
      } catch (error) {
        results[domain] = {
          reachable: false,
          error: error.message,
          responseTime: null
        };
      }
    }

    return results;
  }

  /**
   * Ping域名
   * @param {string} domain - 域名
   * @returns {Promise<Object>} Ping结果
   */
  async pingDomain(domain) {
    const startTime = Date.now();
    
    return new Promise((resolve, reject) => {
      wx.request({
        url: domain,
        method: 'GET',
        timeout: 10000,
        success: (res) => {
          const responseTime = Date.now() - startTime;
          resolve({
            reachable: true,
            statusCode: res.statusCode,
            responseTime: responseTime,
            headers: res.header
          });
        },
        fail: (error) => {
          const responseTime = Date.now() - startTime;
          resolve({
            reachable: false,
            error: error.errMsg,
            responseTime: responseTime
          });
        }
      });
    });
  }

  /**
   * 检查API端点
   * @returns {Promise<Object>} API端点检查结果
   */
  async checkApiEndpoint() {
    const apiUrl = 'https://api.moonshot.cn/v1/chat/completions'; // 使用正确的API地址
    const startTime = Date.now();

    return new Promise((resolve) => {
      wx.request({
        url: apiUrl,
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-key' // 使用测试密钥
        },
        data: {
          model: 'moonshot-v1-8k', // 使用正确的模型名称
          messages: [{ role: 'user', content: 'test' }]
        },
        timeout: 15000,
        success: (res) => {
          const responseTime = Date.now() - startTime;
          resolve({
            reachable: true,
            statusCode: res.statusCode,
            responseTime: responseTime,
            response: res.data,
            error: res.data?.error
          });
        },
        fail: (error) => {
          const responseTime = Date.now() - startTime;
          resolve({
            reachable: false,
            error: error.errMsg,
            responseTime: responseTime
          });
        }
      });
    });
  }

  /**
   * 检查请求头配置
   * @returns {Object} 请求头检查结果
   */
  checkRequestHeaders() {
    const requiredHeaders = [
      'Content-Type',
      'Authorization'
    ];

    const recommendedHeaders = [
      'User-Agent'
    ];

    return {
      required: requiredHeaders,
      recommended: recommendedHeaders,
      current: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer [API_KEY]',
        'User-Agent': 'WeChat-MiniProgram'
      }
    };
  }

  /**
   * 生成建议
   * @param {Object} results - 诊断结果
   * @returns {Array} 建议列表
   */
  generateRecommendations(results) {
    const recommendations = [];

    // 网络状态建议
    if (!results.networkStatus.isConnected) {
      recommendations.push({
        type: 'critical',
        title: '网络连接问题',
        description: '设备未连接到网络，请检查网络设置',
        action: '检查WiFi或移动数据连接'
      });
    } else if (results.networkStatus.networkType === '2g') {
      recommendations.push({
        type: 'warning',
        title: '网络速度较慢',
        description: '当前使用2G网络，可能影响API请求速度',
        action: '建议切换到WiFi或4G/5G网络'
      });
    }

    // 域名可达性建议
    const moonshotReachable = results.domainReachability['https://api.moonshot.cn']?.reachable;
    if (!moonshotReachable) {
      recommendations.push({
        type: 'critical',
        title: 'API域名不可达',
        description: 'Moonshot API域名无法访问',
        action: '检查域名白名单配置或网络防火墙设置'
      });
    }

    // API端点建议
    if (!results.apiEndpoint.reachable) {
      recommendations.push({
        type: 'critical',
        title: 'API端点连接失败',
        description: 'API端点无法连接，可能是网络或配置问题',
        action: '检查API地址、密钥和网络配置'
      });
    } else if (results.apiEndpoint.statusCode === 401) {
      recommendations.push({
        type: 'warning',
        title: 'API认证问题',
        description: 'API密钥可能无效或已过期',
        action: '检查并更新API密钥'
      });
    }

    // 性能建议
    const apiResponseTime = results.apiEndpoint.responseTime;
    if (apiResponseTime && apiResponseTime > 10000) {
      recommendations.push({
        type: 'info',
        title: '响应时间较长',
        description: `API响应时间为${apiResponseTime}ms，较长`,
        action: '考虑增加超时时间或优化请求参数'
      });
    }

    // 通用建议
    recommendations.push({
      type: 'info',
      title: '微信小程序域名配置',
      description: '确保在微信小程序后台配置了合法域名',
      action: '在小程序后台添加 https://api.moonshot.cn 到request合法域名'
    });

    return recommendations;
  }

  /**
   * 打印诊断报告
   * @param {Object} results - 诊断结果
   */
  printDiagnosticReport(results) {
    console.log('\n📊 网络诊断报告');
    console.log('=' * 50);
    
    console.log('\n📶 网络状态:');
    console.log(`  类型: ${results.networkStatus.networkType}`);
    console.log(`  连接状态: ${results.networkStatus.isConnected ? '已连接' : '未连接'}`);
    
    console.log('\n🌐 域名可达性:');
    Object.entries(results.domainReachability).forEach(([domain, result]) => {
      const status = result.reachable ? '✅' : '❌';
      const time = result.responseTime ? `${result.responseTime}ms` : 'N/A';
      console.log(`  ${status} ${domain} (${time})`);
    });
    
    console.log('\n🔌 API端点:');
    const apiStatus = results.apiEndpoint.reachable ? '✅' : '❌';
    const apiTime = results.apiEndpoint.responseTime ? `${results.apiEndpoint.responseTime}ms` : 'N/A';
    console.log(`  ${apiStatus} API连接 (${apiTime})`);
    if (results.apiEndpoint.statusCode) {
      console.log(`  状态码: ${results.apiEndpoint.statusCode}`);
    }
    
    console.log('\n💡 建议:');
    results.recommendations.forEach((rec, index) => {
      const icon = rec.type === 'critical' ? '🚨' : rec.type === 'warning' ? '⚠️' : 'ℹ️';
      console.log(`  ${index + 1}. ${icon} ${rec.title}`);
      console.log(`     ${rec.description}`);
      console.log(`     建议: ${rec.action}`);
    });
  }
}

module.exports = NetworkDiagnostic;
