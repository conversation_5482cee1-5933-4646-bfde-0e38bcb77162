/* 讨论页面样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  position: relative;
  overflow: hidden;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(40rpx);
  border-bottom: 2rpx solid rgba(255, 255, 255, 0.2);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 40rpx;
  padding-top: calc(20rpx + env(safe-area-inset-top));
  height: 88rpx;
}

.nav-left {
  width: 120rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.nav-right {
  width: 140rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.nav-icon {
  color: white;
  font-size: 36rpx;
  font-weight: bold;
}

.nav-title {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  text-shadow: 0 0 20rpx rgba(255, 255, 255, 0.5);
}

.timer {
  color: #333;
  font-size: 28rpx;
  font-weight: 700;
  background: #ffd700;
  padding: 12rpx 20rpx;
  border-radius: 24rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.3);
  min-width: 80rpx;
  text-align: center;
  letter-spacing: 1rpx;
}

.timer.urgent {
  color: white;
  background: #ff4d4f;
  box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.4);
  animation: pulse 1s infinite;
}

/* 页面内容 */
.page-content {
  padding: 240rpx 40rpx 240rpx;
  position: relative;
  z-index: 1;
}

/* 艺术化卡片 */
.artistic-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(50rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 48rpx;
  padding: 48rpx;
  margin-bottom: 40rpx;
  box-shadow:
    0 24rpx 80rpx rgba(0, 0, 0, 0.15),
    0 8rpx 32rpx rgba(0, 0, 0, 0.1),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 章节标题 */
.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.section-icon {
  font-size: 40rpx;
  margin-right: 16rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  text-shadow: 0 0 20rpx rgba(255, 255, 255, 0.6);
}

/* 说明内容 */
.instruction-content {
  padding: 16rpx 0;
}

.instruction-text {
  font-size: 28rpx;
  line-height: 1.8;
  color: rgba(255, 255, 255, 0.9);
  text-align: justify;
}

/* 标签页 */
.tab-header {
  display: flex;
  margin-bottom: 32rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 24rpx;
  padding: 8rpx;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 8rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.tab-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.tab-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

/* 聊天区域 */
.chat-area {
  height: 600rpx;
  display: flex;
  flex-direction: column;
}

.message-list {
  flex: 1;
  padding: 16rpx 0;
}

.message-item {
  display: flex;
  margin-bottom: 24rpx;
}

.message-avatar {
  margin-right: 16rpx;
}

.avatar-circle {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.message-content {
  flex: 1;
}

.message-header {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.sender-name {
  font-size: 24rpx;
  font-weight: 600;
  color: #ffd700;
  margin-right: 16rpx;
}

.message-time {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
}

.message-bubble {
  background: rgba(255, 255, 255, 0.1);
  padding: 16rpx 20rpx;
  border-radius: 16rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.message-bubble text {
  font-size: 28rpx;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.9);
}

/* 输入区域 */
.input-area {
  display: flex;
  align-items: flex-end;
  gap: 16rpx;
  padding: 16rpx 0;
  border-top: 2rpx solid rgba(255, 255, 255, 0.1);
}

.input-wrapper {
  flex: 1;
  position: relative;
}

.message-input {
  width: 100%;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  font-size: 28rpx;
  color: #333;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.input-counter {
  position: absolute;
  bottom: 8rpx;
  right: 16rpx;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
}

.send-btn {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(255, 215, 0, 0.4);
}

.send-btn:disabled {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: none;
}

.send-icon {
  font-size: 28rpx;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 40rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(40rpx);
  border-top: 2rpx solid rgba(255, 255, 255, 0.2);
  display: flex;
  gap: 24rpx;
  z-index: 1000;
}

.action-btn {
  flex: 1;
  padding: 32rpx;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(20rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.action-btn.primary {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #333;
  box-shadow: 0 12rpx 40rpx rgba(255, 215, 0, 0.4);
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.action-btn:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 16rpx 50rpx rgba(0, 0, 0, 0.2);
}

.btn-icon {
  font-size: 28rpx;
}

/* 脉搏动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.4);
  }
  50% {
    transform: scale(1.08);
    box-shadow: 0 6rpx 16rpx rgba(255, 77, 79, 0.6);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.4);
  }
}
