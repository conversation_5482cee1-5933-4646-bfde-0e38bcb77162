import{__awaiter,__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import{getRect}from"../common/utils";const{prefix:prefix}=config,name=`${prefix}-cascader`;function parseOptions(e,t){var s,i;const l=null!==(s=null==t?void 0:t.label)&&void 0!==s?s:"label",n=null!==(i=null==t?void 0:t.value)&&void 0!==i?i:"value";return e.map(e=>({[l]:e[l],[n]:e[n]}))}const defaultState={contentHeight:0,stepHeight:0,tabsHeight:0,subTitlesHeight:0,stepsInitHeight:0};let Cascader=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`],this.options={multipleSlots:!0,pureDataPattern:/^options$/},this.properties=props,this.controlledProps=[{key:"value",event:"change"}],this.state=Object.assign({},defaultState),this.data={prefix:prefix,name:name,stepIndex:0,selectedIndexes:[],selectedValue:[],scrollTopList:[],steps:[],_optionsHeight:0},this.observers={visible(e){if(e){const e=this.selectComponent("#tabs");null==e||e.setTrack(),null==e||e.getTabHeight().then(e=>{this.state.tabsHeight=e.height}),this.initOptionsHeight(this.data.steps.length),this.updateScrollTop(),this.initWithValue()}else this.state=Object.assign({},defaultState)},value(){this.initWithValue()},options(){const{selectedValue:e,steps:t,items:s}=this.genItems();this.setData({steps:t,items:s,selectedValue:e,stepIndex:s.length-1})},selectedIndexes(){const{visible:e,theme:t}=this.properties,{selectedValue:s,steps:i,items:l}=this.genItems(),n={steps:i,selectedValue:s,stepIndex:l.length-1};JSON.stringify(l)!==JSON.stringify(this.data.items)&&Object.assign(n,{items:l}),this.setData(n),e&&"step"===t&&this.updateOptionsHeight(i.length)},stepIndex(){return __awaiter(this,void 0,void 0,function*(){const{visible:e}=this.data;e&&this.updateScrollTop()})}},this.methods={updateOptionsHeight(e){const{contentHeight:t,stepsInitHeight:s,stepHeight:i,subTitlesHeight:l}=this.state;this.setData({_optionsHeight:t-s-l-(e-1)*i})},initOptionsHeight(e){return __awaiter(this,void 0,void 0,function*(){const{theme:t,subTitles:s}=this.properties,{height:i}=yield getRect(this,`.${name}__content`);if(this.state.contentHeight=i,"step"===t&&(yield Promise.all([getRect(this,`.${name}__steps`),getRect(this,`.${name}__step`)]).then(([t,s])=>{this.state.stepsInitHeight=t.height-(e-1)*s.height,this.state.stepHeight=s.height})),s.length>0){const{height:e}=yield getRect(this,`.${name}__options-title`);this.state.subTitlesHeight=e}const l=this.state.contentHeight-this.state.subTitlesHeight;this.setData({_optionsHeight:"step"===t?l-this.state.stepsInitHeight-(e-1)*this.state.stepHeight:l-this.state.tabsHeight})})},initWithValue(){if(null!=this.data.value&&""!==this.data.value){const e=this.getIndexesByValue(this.data.options,this.data.value);e&&this.setData({selectedIndexes:e})}else this.setData({selectedIndexes:[]})},getIndexesByValue(e,t){var s,i,l;const{keys:n}=this.data;for(let a=0,h=e.length;a<h;a+=1){const h=e[a];if(h[null!==(s=null==n?void 0:n.value)&&void 0!==s?s:"value"]===t)return[a];if(h[null!==(i=null==n?void 0:n.children)&&void 0!==i?i:"children"]){const e=this.getIndexesByValue(h[null!==(l=null==n?void 0:n.children)&&void 0!==l?l:"children"],t);if(e)return[a,...e]}}},updateScrollTop(){const{visible:e,items:t,selectedIndexes:s,stepIndex:i}=this.data;e&&getRect(this,".cascader-radio-group-0").then(e=>{var l;const n=e.height/(null===(l=t[0])||void 0===l?void 0:l.length);this.setData({[`scrollTopList[${i}]`]:n*s[i]})})},hide(e){this.setData({visible:!1}),this.triggerEvent("close",{trigger:e})},onVisibleChange(){this.hide("overlay")},onClose(){this.data.checkStrictly&&this.triggerChange(),this.hide("close-btn")},onStepClick(e){const{index:t}=e.currentTarget.dataset;this.setData({stepIndex:t})},onTabChange(e){const{value:t}=e.detail;this.setData({stepIndex:t})},genItems(){var e,t,s,i,l;const{options:n,selectedIndexes:a,keys:h,placeholder:o}=this.data,d=[],r=[],c=[parseOptions(n,h)];if(n.length>0){let o=n;for(let n=0,u=a.length;n<u;n+=1){const u=o[a[n]];o=u[null!==(e=null==h?void 0:h.children)&&void 0!==e?e:"children"],d.push(u[null!==(t=null==h?void 0:h.value)&&void 0!==t?t:"value"]),r.push(u[null!==(s=null==h?void 0:h.label)&&void 0!==s?s:"label"]),u[null!==(i=null==h?void 0:h.children)&&void 0!==i?i:"children"]&&c.push(parseOptions(u[null!==(l=null==h?void 0:h.children)&&void 0!==l?l:"children"],h))}}return r.length<c.length&&r.push(o),{selectedValue:d,steps:r,items:c}},handleSelect(e){var t,s,i,l,n;const{level:a}=e.target.dataset,{value:h}=e.detail,{checkStrictly:o}=this.properties,{selectedIndexes:d,items:r,keys:c,options:u,selectedValue:p}=this.data,g=r[a].findIndex(e=>{var t;return e[null!==(t=null==c?void 0:c.value)&&void 0!==t?t:"value"]===h});let v=d.slice(0,a).reduce((e,t,s)=>{var i;return 0===s?e[t]:e[null!==(i=null==c?void 0:c.children)&&void 0!==i?i:"children"][t]},u);if(v=0===a?v[g]:v[null!==(t=null==c?void 0:c.children)&&void 0!==t?t:"children"][g],v.disabled)return;if(this.triggerEvent("pick",{value:v[null!==(s=null==c?void 0:c.value)&&void 0!==s?s:"value"],label:v[null!==(i=null==c?void 0:c.label)&&void 0!==i?i:"label"],index:g,level:a}),d[a]=g,o&&p.includes(String(h)))return d.length=a,void this.setData({selectedIndexes:d});d.length=a+1;const{items:m}=this.genItems();(null===(n=null==v?void 0:v[null!==(l=null==c?void 0:c.children)&&void 0!==l?l:"children"])||void 0===n?void 0:n.length)>=0?this.setData({selectedIndexes:d,[`items[${a+1}]`]:m[a+1]}):(this.setData({selectedIndexes:d},this.triggerChange),this.hide("finish"))},triggerChange(){var e;const{items:t,selectedValue:s,selectedIndexes:i}=this.data;this._trigger("change",{value:null!==(e=s[s.length-1])&&void 0!==e?e:"",selectedOptions:t.map((e,t)=>e[i[t]]).filter(Boolean)})}}}};Cascader=__decorate([wxComponent()],Cascader);export default Cascader;