// pages/cloud-debug/cloud-debug.js
// 云开发调试页面

Page({
  data: {
    envList: [
      'cloud1-7ggoe4se871c08ae',
      'cloud1-1g88tqlcd2222f2e'
    ],
    currentEnv: '',
    testResults: [],
    isLoading: false
  },

  onLoad() {
    console.log('🔧 云开发调试页面加载');
    this.checkBasicInfo();
  },

  // 检查基础信息
  checkBasicInfo() {
    const systemInfo = wx.getSystemInfoSync();
    const hasCloud = !!wx.cloud;
    
    console.log('📱 系统信息:', {
      platform: systemInfo.platform,
      version: systemInfo.version,
      SDKVersion: systemInfo.SDKVersion,
      hasCloud: hasCloud
    });

    this.addTestResult('系统检查', hasCloud ? '✅ 支持云开发' : '❌ 不支持云开发', {
      SDKVersion: systemInfo.SDKVersion,
      hasCloud: hasCloud
    });
  },

  // 测试单个环境
  async testEnvironment(envId) {
    console.log(`🧪 测试环境: ${envId}`);
    
    try {
      // 初始化云开发
      wx.cloud.init({
        env: envId,
        traceUser: true
      });

      this.setData({ currentEnv: envId });
      
      // 尝试调用一个简单的云函数来测试连接
      try {
        const result = await wx.cloud.callFunction({
          name: 'hello',
          data: { test: true, timestamp: Date.now() }
        });
        
        this.addTestResult(`环境 ${envId}`, '✅ 连接成功', result);
        return { success: true, envId, result };
        
      } catch (funcError) {
        // 如果是函数不存在，说明连接正常
        if (funcError.errMsg && (
          funcError.errMsg.includes('FunctionNotFound') || 
          funcError.errMsg.includes('FunctionName parameter could not be found')
        )) {
          this.addTestResult(`环境 ${envId}`, '✅ 连接正常（函数未部署）', {
            message: '环境可用，但云函数未部署'
          });
          return { success: true, envId, message: '环境可用' };
        }
        
        throw funcError;
      }
      
    } catch (error) {
      console.error(`❌ 环境 ${envId} 测试失败:`, error);
      this.addTestResult(`环境 ${envId}`, `❌ 失败: ${error.errMsg || error.message}`, error);
      return { success: false, envId, error };
    }
  },

  // 测试所有环境
  async testAllEnvironments() {
    this.setData({ 
      isLoading: true,
      testResults: []
    });

    const results = [];
    
    for (const envId of this.data.envList) {
      const result = await this.testEnvironment(envId);
      results.push(result);
      
      // 如果找到可用环境，更新全局状态
      if (result.success) {
        const app = getApp();
        app.globalData.cloudEnabled = true;
        app.globalData.cloudEnvId = envId;
        
        this.addTestResult('全局状态', '✅ 已更新', {
          cloudEnabled: true,
          cloudEnvId: envId
        });
        break;
      }
    }

    this.setData({ isLoading: false });
    
    const successCount = results.filter(r => r.success).length;
    this.addTestResult('测试总结', `${successCount}/${results.length} 个环境可用`, results);
  },

  // 手动初始化云开发
  async manualInit() {
    console.log('🔧 手动初始化云开发');
    
    if (!wx.cloud) {
      wx.showToast({
        title: '不支持云开发',
        icon: 'error'
      });
      return;
    }

    await this.testAllEnvironments();
  },

  // 创建云函数
  async createCloudFunction() {
    if (!this.data.currentEnv) {
      wx.showToast({
        title: '请先初始化环境',
        icon: 'error'
      });
      return;
    }

    wx.showModal({
      title: '创建云函数',
      content: `需要在云开发控制台手动创建云函数。\n\n当前环境: ${this.data.currentEnv}\n\n是否打开控制台？`,
      success: (res) => {
        if (res.confirm) {
          // 这里可以提供控制台链接
          wx.setClipboardData({
            data: `https://console.cloud.tencent.com/tcb/scf/index?envId=${this.data.currentEnv}`,
            success: () => {
              wx.showToast({
                title: '链接已复制',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  },

  // 添加测试结果
  addTestResult(title, message, data = null) {
    const result = {
      id: Date.now(),
      title,
      message,
      data: data ? JSON.stringify(data, null, 2) : null,
      timestamp: new Date().toLocaleTimeString()
    };

    this.setData({
      testResults: [...this.data.testResults, result]
    });
  },

  // 清除测试结果
  clearResults() {
    this.setData({ testResults: [] });
  },

  // 复制结果
  copyResult(e) {
    const { result } = e.currentTarget.dataset;
    const text = `${result.title}: ${result.message}\n时间: ${result.timestamp}${result.data ? '\n数据: ' + result.data : ''}`;
    
    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: '已复制',
          icon: 'success'
        });
      }
    });
  }
});
