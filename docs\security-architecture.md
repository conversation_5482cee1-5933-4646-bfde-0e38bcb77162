# 🔒 API安全架构指南

## 🚨 **当前问题**

你的应用存在**严重的安全漏洞**：

### ❌ **错误做法**
```javascript
// 在客户端代码中直接暴露API密钥
const API_KEY = 'sk-KqcYebI2ibq1SKBdRGlbtRIQX5xh9RqzKxURhQePxRaJ7Z1F';

wx.request({
  url: 'https://api.moonshot.cn/v1/chat/completions',
  header: {
    'Authorization': `Bearer ${API_KEY}` // ❌ 密钥完全暴露！
  }
});
```

### 🔥 **安全风险**
1. **API密钥完全暴露** - 任何人都能看到
2. **自动检测和禁用** - Moonshot AI会自动禁用泄露的密钥
3. **恶意使用** - 他人可能盗用你的密钥
4. **费用损失** - 可能产生意外的API调用费用

## ✅ **正确的解决方案**

### 方案1: 微信云开发（推荐）

#### 1. **启用云开发**
```javascript
// app.js
App({
  onLaunch: function () {
    // 初始化云开发
    wx.cloud.init({
      env: 'your-env-id', // 你的云开发环境ID
      traceUser: true
    });
  }
});
```

#### 2. **创建云函数**
```bash
# 在项目根目录
mkdir cloudfunctions
cd cloudfunctions
mkdir ai-service
cd ai-service
npm init -y
npm install axios
```

#### 3. **配置环境变量**
在云开发控制台中设置环境变量：
- `MOONSHOT_API_KEY`: 你的API密钥

#### 4. **客户端调用云函数**
```javascript
// 安全的客户端代码
async function generateScript(params) {
  try {
    const result = await wx.cloud.callFunction({
      name: 'ai-service',
      data: {
        action: 'generateScript',
        data: params
      }
    });
    
    return result.result.data;
  } catch (error) {
    console.error('AI服务调用失败:', error);
    throw error;
  }
}
```

### 方案2: 自建后端服务

#### 1. **Node.js后端示例**
```javascript
// server.js
const express = require('express');
const axios = require('axios');
const app = express();

// 从环境变量获取API密钥
const MOONSHOT_API_KEY = process.env.MOONSHOT_API_KEY;

app.post('/api/ai/generate', async (req, res) => {
  try {
    const response = await axios.post('https://api.moonshot.cn/v1/chat/completions', {
      model: 'kimi-k2-0711-preview',
      messages: req.body.messages
    }, {
      headers: {
        'Authorization': `Bearer ${MOONSHOT_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    res.json(response.data);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.listen(3000);
```

#### 2. **客户端调用后端**
```javascript
// 客户端代码
async function callAI(messages) {
  const response = await wx.request({
    url: 'https://your-backend.com/api/ai/generate',
    method: 'POST',
    data: { messages }
  });
  
  return response.data;
}
```

## 🛠 **立即行动计划**

### 第1步: 停止使用当前密钥
1. **立即删除** 当前暴露的API密钥
2. **清理代码** 中所有硬编码的密钥
3. **生成新密钥** 用于后端服务

### 第2步: 选择架构方案
- **简单项目**: 使用微信云开发
- **复杂项目**: 自建后端服务
- **快速原型**: 暂时使用模拟服务

### 第3步: 实施安全架构
1. 设置云函数或后端服务
2. 配置环境变量存储密钥
3. 更新客户端代码调用方式
4. 测试新的安全架构

### 第4步: 安全检查
- ✅ 确认客户端代码中没有密钥
- ✅ 验证后端服务正常工作
- ✅ 测试API调用功能
- ✅ 监控API使用情况

## 🔐 **安全最佳实践**

### 1. **密钥管理**
- ✅ 使用环境变量存储密钥
- ✅ 定期轮换API密钥
- ✅ 为不同环境使用不同密钥
- ❌ 永远不要在客户端代码中硬编码密钥

### 2. **访问控制**
- ✅ 实施用户认证和授权
- ✅ 限制API调用频率
- ✅ 监控异常使用模式
- ✅ 设置使用配额限制

### 3. **代码安全**
- ✅ 定期进行安全审计
- ✅ 使用代码扫描工具
- ✅ 实施安全的CI/CD流程
- ✅ 培训团队安全意识

### 4. **监控和日志**
- ✅ 记录所有API调用
- ✅ 监控异常错误率
- ✅ 设置安全告警
- ✅ 定期审查访问日志

## 📞 **紧急联系**

如果发现密钥泄露：
1. **立即禁用** 泄露的密钥
2. **生成新密钥** 并安全存储
3. **检查使用记录** 是否有异常
4. **联系客服** 如有异常费用

## 🎯 **总结**

**API密钥绝对不能暴露在客户端代码中！**

这是基本的安全原则。Moonshot AI检测到密钥泄露后会自动禁用，这就是你一直收到401错误的根本原因。

**立即采用云函数或后端代理的方式来安全地调用AI API！**
