/* AI功能演示页面样式 */
.ai-demo-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
  color: #fff;
}

/* 页面标题 */
.demo-header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.demo-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.demo-subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.8;
}

/* 配置区域 */
.config-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  text-align: center;
}

.config-item {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15rpx;
}

.config-label {
  font-size: 28rpx;
  font-weight: 500;
  min-width: 160rpx;
  margin-right: 20rpx;
}

.picker-value {
  flex: 1;
  padding: 15rpx 20rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10rpx;
  font-size: 28rpx;
}

.config-input, .config-textarea {
  flex: 1;
  padding: 15rpx 20rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #fff;
}

.config-textarea {
  min-height: 80rpx;
}

/* 演示按钮 */
.demo-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.demo-btn {
  padding: 25rpx 40rpx;
  border-radius: 15rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
}

.demo-btn.primary {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: #fff;
}

.demo-btn.secondary {
  background: linear-gradient(45deg, #4834d4, #686de0);
  color: #fff;
}

.demo-btn.tertiary {
  background: linear-gradient(45deg, #00d2d3, #54a0ff);
  color: #fff;
}

.demo-btn.clear {
  background: linear-gradient(45deg, #ff9ff3, #f368e0);
  color: #fff;
}

.demo-btn:disabled {
  opacity: 0.6;
  transform: none;
}

.demo-btn:not(:disabled):active {
  transform: scale(0.98);
}

/* 结果展示 */
.results-section {
  margin-bottom: 30rpx;
}

.result-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  backdrop-filter: blur(10rpx);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid rgba(255, 255, 255, 0.2);
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
}

.detail-btn {
  padding: 10rpx 20rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10rpx;
  font-size: 24rpx;
  color: #fff;
  border: none;
}

.result-content {
  font-size: 28rpx;
}

.result-item {
  display: flex;
  margin-bottom: 15rpx;
  align-items: flex-start;
}

.item-label {
  font-weight: bold;
  min-width: 120rpx;
  margin-right: 10rpx;
  opacity: 0.8;
}

.item-value {
  flex: 1;
  line-height: 1.5;
}

/* 角色分配列表 */
.assignment-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.assignment-item {
  display: flex;
  align-items: center;
  padding: 15rpx 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10rpx;
}

.player-id {
  font-weight: bold;
  color: #ffd700;
}

.arrow {
  margin: 0 15rpx;
  font-size: 24rpx;
  opacity: 0.7;
}

.character-name {
  flex: 1;
  font-weight: 500;
}

/* 问题列表 */
.question-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.question-item {
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15rpx;
}

.question-text {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
  line-height: 1.5;
}

.question-purpose {
  display: block;
  font-size: 24rpx;
  opacity: 0.7;
  font-style: italic;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  text-align: center;
  padding: 60rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
  border-top: 6rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.loading-tip {
  display: block;
  font-size: 24rpx;
  opacity: 0.7;
}

/* 底部操作 */
.bottom-actions {
  text-align: center;
  padding: 30rpx 0;
}

.action-btn {
  padding: 20rpx 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 15rpx;
  font-size: 28rpx;
  color: #fff;
  border: none;
  backdrop-filter: blur(10rpx);
}

.action-btn:active {
  transform: scale(0.98);
}
