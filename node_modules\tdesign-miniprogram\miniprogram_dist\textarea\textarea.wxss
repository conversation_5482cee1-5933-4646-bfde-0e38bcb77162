@import '../common/style/index.wxss';.t-textarea{display:flex;flex-direction:column;box-sizing:border-box;padding:32rpx 32rpx;background-color:var(--td-textarea-background-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));}
.t-textarea__label:not(:empty){font-size:var(--td-font-size-base,28rpx);color:var(--td-textarea-label-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));flex-shrink:0;line-height:44rpx;padding-bottom:var(--td-spacer,16rpx);overflow:hidden;white-space:nowrap;text-overflow:ellipsis;}
.t-textarea__wrapper{display:flex;flex-direction:column;width:100%;flex:1 1 auto;overflow:hidden;}
.t-textarea__wrapper-inner{flex:1 1 auto;box-sizing:border-box;width:inherit;min-width:0;min-height:20px;margin:0;padding:0;text-align:left;background-color:transparent;border:0;resize:none;font-size:var(--td-font-size-m,32rpx);color:var(--td-textarea-text-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));line-height:48rpx;}
.t-textarea__placeholder{color:var(--td-textarea-placeholder-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))));font-size:var(--td-font-size-m,32rpx);}
.t-textarea__indicator:not(:empty){flex-shrink:0;color:var(--td-textarea-indicator-text-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))));font-size:var(--td-spacer-1,24rpx);text-align:right;line-height:40rpx;padding-top:var(--td-spacer,16rpx);}
.t-textarea--border{border-radius:var(--td-textarea-border-radius,var(--td-radius-default,12rpx));border:2rpx solid var(--td-textarea-border-color,var(--td-component-border,var(--td-gray-color-4,#dcdcdc)));}
.t-textarea .t-is-disabled{color:var(--td-textarea-disabled-text-color,var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26))));}