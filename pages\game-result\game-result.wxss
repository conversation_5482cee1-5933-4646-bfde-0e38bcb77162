/* 游戏结果页面样式 - 艺术化设计 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #2c1810 100%);
  position: relative;
  overflow: hidden;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

.container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 30%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(0, 191, 255, 0.08) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(40rpx);
  border-bottom: 2rpx solid rgba(255, 255, 255, 0.2);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 40rpx;
  padding-top: calc(20rpx + env(safe-area-inset-top));
  height: 88rpx;
}

.nav-title {
  color: white;
  font-size: 36rpx;
  font-weight: 600;
  text-shadow: 0 0 20rpx rgba(255, 255, 255, 0.5);
}

/* 页面内容 */
.page-content {
  padding: 240rpx 40rpx 240rpx;
  position: relative;
  z-index: 1;
}

/* 艺术化卡片 */
.artistic-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(50rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 48rpx;
  padding: 48rpx;
  margin-bottom: 40rpx;
  box-shadow:
    0 24rpx 80rpx rgba(0, 0, 0, 0.15),
    0 8rpx 32rpx rgba(0, 0, 0, 0.1),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 结果卡片 */
.result-card {
  text-align: center;
  padding: 64rpx 48rpx;
}

.result-icon {
  font-size: 96rpx;
  margin-bottom: 32rpx;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0rpx); }
  50% { transform: translateY(-20rpx); }
}

.result-title {
  font-size: 56rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 16rpx;
  text-shadow: 0 0 40rpx rgba(255, 255, 255, 0.8);
}

.result-subtitle {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

/* 章节标题 */
.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.section-icon {
  font-size: 40rpx;
  margin-right: 16rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  text-shadow: 0 0 20rpx rgba(255, 255, 255, 0.6);
}

/* 真相内容 */
.truth-content {
  padding: 16rpx 0;
}

.truth-text {
  font-size: 32rpx;
  line-height: 1.8;
  color: rgba(255, 255, 255, 0.9);
  text-align: justify;
}

/* 玩家表现 */
.performance-list {
  padding: 16rpx 0;
}

.performance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  margin-bottom: 16rpx;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 20rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.15);
}

.player-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.player-avatar {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 20rpx;
}

.player-details {
  flex: 1;
}

.player-name {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 4rpx;
}

.player-role {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

.performance-result {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.performance-result.winner {
  background: linear-gradient(45deg, #52c41a, #389e0d);
  color: white;
}

.performance-result.loser {
  background: linear-gradient(45deg, #ff4d4f, #cf1322);
  color: white;
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  padding: 16rpx 0;
}

.stat-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.stat-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffd700;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 40rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(40rpx);
  border-top: 2rpx solid rgba(255, 255, 255, 0.2);
  display: flex;
  gap: 24rpx;
  z-index: 1000;
}

.action-btn {
  flex: 1;
  padding: 32rpx;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(20rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.action-btn.primary {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #333;
  box-shadow: 0 12rpx 40rpx rgba(255, 215, 0, 0.4);
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.action-btn:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 16rpx 50rpx rgba(0, 0, 0, 0.2);
}

.btn-icon {
  font-size: 28rpx;
}
