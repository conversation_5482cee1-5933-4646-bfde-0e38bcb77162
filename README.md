# AI社交推理游戏 - 微信小程序



## 🎮 项目特色

- **AI智能剧本生成** - 根据玩家偏好动态生成独特剧本
- **个人坚持机制** 🆕 - 创新的影响力系统，允许玩家改变剧情走向
- **实时语音聊天** - 支持多人语音讨论和私聊
- **真心话环节** - AI提问评价系统，增加游戏趣味性
- **智能角色分配** - AI根据剧本特点自动分配最优角色
- **成就系统** - 丰富的成就和影响力系统
- **精美UI设计** - 基于微信原生组件的现代化界面设计

## 📱 页面结构

### 核心页面
- `pages/home/<USER>
- `pages/room-create/` - 创建房间
- `pages/room-lobby/` - 房间大厅
- `pages/discussion/` - 讨论阶段
- `pages/profile/` - 个人中心
- `pages/history/` - 历史记录

### 游戏流程页面
- `pages/ai-script-loading/` - AI剧本生成Loading
- `pages/character-relations/` - 人物关系网络图
- `pages/game-settings/` - 游戏设置
- `pages/private-clues/` - 私密线索
- `pages/role-assignment/` - 角色分配
- `pages/truth-dare/` - 真心话环节
- `pages/mini-game/` - 小游戏互动
- `pages/plot-voting/` - 剧情分支投票
- `pages/voting-result/` - 投票结果与坚持机制
- `pages/persistence-demo/` - 个人坚持机制演示 🆕
- `pages/final-voting/` - 最终投票
- `pages/game-result/` - 游戏结果

## 🛠 技术栈

- **框架**: 微信小程序原生开发
- **UI组件**: 微信原生组件 + 自定义CSS样式
- **状态管理**: 页面级状态管理
- **网络请求**: 封装的API服务
- **工具函数**: 通用工具库
- **样式系统**: 艺术化CSS设计系统

## 📦 项目结构

```
d:\聚会游戏/
├── pages/                  # 页面目录
│   ├── home/               # 首页
│   ├── room-create/        # 创建房间
│   ├── room-lobby/         # 房间大厅
│   ├── discussion/         # 讨论阶段
│   ├── profile/            # 个人中心
│   ├── history/            # 历史记录
│   └── ...                 # 其他游戏页面
├── utils/                  # 工具函数
│   ├── api.js             # API接口封装
│   └── common.js          # 通用工具函数
├── prototype/             # 原型设计文件
│   └── index.html         # HTML原型展示
├── app.js                 # 小程序入口文件
├── app.json              # 小程序配置文件
├── app.wxss              # 全局样式文件
├── prd.md                # 产品需求文档
└── README.md             # 项目说明文档
```

## 🚀 快速开始

### 1. 环境准备
- 安装微信开发者工具
- 注册微信小程序账号
- 获取AppID

### 2. 项目特色
- **无第三方依赖**: 完全基于微信原生组件开发
- **艺术化设计**: 自定义CSS样式系统
- **高性能**: 原生组件保证最佳性能表现

### 3. 样式系统
项目采用自定义CSS设计系统：
```css
/* 艺术化卡片 */
.artistic-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}

/* 渐变按钮 */
.artistic-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}
```

### 4. 运行项目
1. 用微信开发者工具打开项目目录
2. 填入AppID
3. 点击编译运行
4. 享受原生组件的流畅体验

## 🎯 核心功能实现

### AI剧本生成
- 模拟AI生成过程的Loading页面
- 进度条和步骤展示
- 错误处理和重试机制

### 实时聊天系统
- 支持文本、语音、线索分享
- 私聊和群聊功能
- 消息状态和时间显示

### 游戏机制
- 坚持机制：消耗影响力改变剧情
- 真心话环节：AI评分系统
- 投票系统：多轮投票和结果展示

### 用户系统
- 个人资料管理
- 游戏历史记录
- 成就和影响力系统

## 🎨 UI设计特色

### 视觉风格
- **深空渐变背景** - 5层渐变营造神秘氛围
- **全息毛玻璃效果** - 30rpx模糊的半透明卡片设计
- **动态光效动画** - 金色、紫色、蓝色光晕效果
- **响应式布局** - 适配不同屏幕尺寸

### 组件特色
- **微信原生组件** - 保证最佳性能和兼容性
- **自定义CSS样式** - 艺术化设计系统
- **统一交互规范** - 一致的视觉语言和用户体验
- **无第三方依赖** - 减少包体积，提升加载速度

## 🎭 CSS设计系统

### 核心样式类
```css
/* 艺术化卡片 */
.artistic-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  backdrop-filter: blur(30rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 30rpx;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.2);
}

/* 全息按钮 */
.artistic-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: 2rpx solid transparent;
  border-radius: 16rpx;
  position: relative;
  overflow: hidden;
}

.artistic-btn::before {
  content: "";
  position: absolute;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
  background-size: 400% 400%;
  animation: gradient-shift 6s ease infinite;
}
```

### 动画系统
- **fadeIn**: 淡入动画，0.6s缓动
- **pulse**: 脉动效果，2s循环
- **glow**: 发光动画，动态阴影
- **float**: 浮动效果，3s上下浮动
- **gradient-shift**: 全息彩虹边框，6s循环

### 字体层次
- **text-xs**: 20rpx，用于辅助信息
- **text-sm**: 24rpx，用于次要文本
- **text-base**: 28rpx，用于正文内容
- **text-lg**: 32rpx，用于重要信息
- **text-xl**: 36rpx，用于小标题
- **text-2xl**: 40rpx，用于标题
- **text-3xl**: 48rpx，用于大标题
- **text-4xl**: 56rpx，用于主标题

## ⚡ 性能优势

### 原生组件 vs 第三方组件库
| 对比项目 | 原生组件+CSS | TDesign组件库 |
|---------|-------------|--------------|
| **包体积** | ~50KB | ~500KB |
| **加载速度** | 即时加载 | 需下载组件库 |
| **运行性能** | 最优性能 | 有额外开销 |
| **兼容性** | 微信原生保证 | 依赖版本更新 |
| **定制化** | 完全自定义 | 受限于组件库 |
| **维护成本** | 自主可控 | 需跟随更新 |

### 技术亮点
- 🚀 **零依赖**: 无需安装任何第三方组件库
- 🎨 **艺术化**: 独特的深空科技风格设计
- ⚡ **高性能**: CSS3硬件加速，流畅动画
- 📱 **原生体验**: 完美适配微信小程序生态
- 🔧 **易维护**: 纯CSS实现，代码简洁清晰

## 📋 开发规范

### 代码结构
- 每个页面包含 `.wxml`、`.wxss`、`.js`、`.json` 四个文件
- 工具函数统一放在 `utils/` 目录
- API接口统一封装在 `utils/api.js`

### 命名规范
- 页面文件夹使用kebab-case命名
- 变量和函数使用camelCase命名
- 常量使用UPPER_SNAKE_CASE命名

### 注释规范
- 页面和组件需要添加功能说明注释
- 复杂逻辑需要添加详细注释
- API接口需要添加参数和返回值说明

## 🔧 配置说明

### app.json配置
- 页面路由配置
- TabBar配置
- 窗口样式配置
- 原生组件配置

### 网络请求配置
- 在 `utils/api.js` 中配置API基础地址
- 统一的请求拦截和错误处理
- Token自动添加和刷新

## 📱 页面功能说明

### 首页 (home)
- 热门房间列表展示
- 快速创建和加入房间
- 房间状态和标签显示

### 创建房间 (room-create)
- 丰富的游戏设置选项
- 游戏风格和难度选择
- 特殊机制开关配置

### 房间大厅 (room-lobby)
- 玩家网格展示
- 准备状态管理
- 房间信息和设置

### 讨论阶段 (discussion)
- 多标签聊天界面
- 实时消息展示
- 线索分享和质疑功能

### 个人中心 (profile)
- 用户信息展示
- 数据统计图表
- 设置和账户管理

### 历史记录 (history)
- 游戏历史列表
- 成就系统展示
- 好友互动记录

## 🚀 部署说明

### 开发环境
1. 使用微信开发者工具进行开发调试
2. 配置本地API服务器地址
3. 开启调试模式和vconsole

### 生产环境
1. 配置生产环境API地址
2. 上传代码到微信后台
3. 提交审核和发布

## 📞 技术支持

如有问题或建议，请联系开发团队。

---

© 2024 AI推理游戏开发团队
