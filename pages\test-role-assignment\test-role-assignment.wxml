<!--测试角色分配功能页面-->
<view class="container">
  <view class="header">
    <text class="title">🎭 角色分配功能测试</text>
    <text class="subtitle">验证AI剧本生成和角色分配功能</text>
  </view>

  <!-- 测试进度 -->
  <view class="progress-section">
    <view class="progress-title">测试进度</view>
    <view class="steps-list">
      <view 
        class="step-item {{index <= testStep ? 'completed' : ''}}" 
        wx:for="{{steps}}" 
        wx:key="index"
      >
        <view class="step-number">{{index + 1}}</view>
        <text class="step-text">{{item}}</text>
        <view class="step-status">
          {{index < testStep ? '✅' : index === testStep ? '🔄' : '⏳'}}
        </view>
      </view>
    </view>
  </view>

  <!-- 控制按钮 -->
  <view class="controls">
    <button class="control-btn primary" bind:tap="startTest">
      🚀 开始测试
    </button>
    <button class="control-btn secondary" bind:tap="resetTest">
      🔄 重置测试
    </button>
  </view>

  <!-- 测试结果 -->
  <view class="results-section" wx:if="{{roomId}}">
    <view class="result-item">
      <text class="result-label">房间ID:</text>
      <text class="result-value">{{roomId}}</text>
    </view>
    
    <view class="result-item" wx:if="{{scriptData}}">
      <text class="result-label">剧本标题:</text>
      <text class="result-value">{{scriptData.storyInfo.title}}</text>
      <button class="detail-btn" bind:tap="viewScriptDetail">查看详情</button>
    </view>
    
    <view class="result-item" wx:if="{{assignmentResult}}">
      <text class="result-label">角色分配:</text>
      <text class="result-value">{{assignmentResult.assignments.length}}个角色已分配</text>
      <button class="detail-btn" bind:tap="viewAssignmentDetail">查看详情</button>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="actions" wx:if="{{testStep >= 5}}">
    <button class="action-btn" bind:tap="goToRoleAssignment">
      🎭 进入角色分配页面
    </button>
  </view>

  <!-- 测试说明 -->
  <view class="test-info">
    <text class="info-title">📋 测试说明</text>
    <view class="info-list">
      <text class="info-item">• 自动创建测试房间和玩家</text>
      <text class="info-item">• 调用AI服务生成剧本</text>
      <text class="info-item">• 智能分配角色给玩家</text>
      <text class="info-item">• 验证分配结果的正确性</text>
      <text class="info-item">• 可进入实际角色分配页面体验</text>
    </view>
  </view>
</view>
