/* 线索收集页面样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  padding: 20rpx;
  padding-bottom: 140rpx;
}

/* 顶部状态栏 */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.timer {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.timer-icon {
  font-size: 32rpx;
}

.timer-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  font-family: 'Courier New', monospace;
}

.phase-indicator {
  background: rgba(255, 215, 0, 0.2);
  border: 1px solid rgba(255, 215, 0, 0.5);
  border-radius: 15rpx;
  padding: 10rpx 20rpx;
}

.phase-text {
  color: #ffd700;
  font-size: 24rpx;
  font-weight: bold;
}

/* 角色信息卡片 */
.role-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.role-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.role-name {
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
}

.role-title {
  color: #ffd700;
  font-size: 28rpx;
  background: rgba(255, 215, 0, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.role-objectives {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.objectives-label {
  color: #ccc;
  font-size: 26rpx;
}

.objectives-text {
  color: #fff;
  font-size: 26rpx;
}

/* 过滤器区域 */
.filters-section {
  margin-bottom: 20rpx;
}

.filters-scroll {
  white-space: nowrap;
}

.filters-list {
  display: flex;
  gap: 15rpx;
  padding: 0 10rpx;
}

.filter-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 25rpx;
  padding: 15rpx 25rpx;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.filter-item.active {
  background: rgba(255, 215, 0, 0.3);
  border-color: rgba(255, 215, 0, 0.6);
  transform: scale(1.05);
}

.filter-name {
  color: #fff;
  font-size: 26rpx;
  font-weight: 500;
}

.filter-badge {
  background: #ff6b6b;
  color: #fff;
  font-size: 20rpx;
  font-weight: bold;
  border-radius: 50%;
  width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36rpx;
}

/* 线索区域 */
.clues-section {
  flex: 1;
}

.clues-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.clue-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20rpx;
  padding: 30rpx;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.clue-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.clue-card.key-clue::before {
  background: linear-gradient(90deg, #ffd700 0%, #ffed4e 100%);
  opacity: 1;
}

.clue-card.discovered {
  border-color: rgba(76, 175, 80, 0.5);
}

.clue-card.undiscovered {
  border-color: rgba(255, 193, 7, 0.5);
}

.clue-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15rpx;
}

.clue-title {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  flex: 1;
  margin-right: 20rpx;
}

.clue-meta {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.clue-time {
  color: #ccc;
  font-size: 22rpx;
}

.clue-status {
  font-size: 24rpx;
}

.clue-content {
  color: #e0e0e0;
  font-size: 28rpx;
  line-height: 1.6;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.clue-footer {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.clue-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.clue-tag {
  background: rgba(100, 149, 237, 0.3);
  color: #87ceeb;
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  border: 1px solid rgba(100, 149, 237, 0.5);
}

.related-chars {
  color: #ffd700;
  font-size: 24rpx;
}

/* 加载和空状态 */
.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  padding: 40rpx;
}

.loading-animation {
  margin-bottom: 30rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #ffd700;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 15rpx;
}

.loading-subtitle {
  color: #ccc;
  font-size: 26rpx;
  text-align: center;
  line-height: 1.4;
}

.empty-text {
  color: #ccc;
  font-size: 28rpx;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(26, 26, 46, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 20rpx;
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.action-btn:active {
  transform: scale(0.98);
}

/* 线索详情弹窗 */
.clue-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.modal-content {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 30rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-title {
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
}

.close-btn {
  background: none;
  border: none;
  color: #ccc;
  font-size: 36rpx;
  padding: 0;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.clue-full-content {
  color: #e0e0e0;
  font-size: 30rpx;
  line-height: 1.8;
  margin-bottom: 30rpx;
  display: block;
}

.clue-details {
  margin-bottom: 30rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
  margin-bottom: 15rpx;
}

.detail-label {
  color: #ccc;
  font-size: 26rpx;
  min-width: 120rpx;
}

.detail-value {
  color: #fff;
  font-size: 26rpx;
}

.detail-value.key-importance {
  color: #ffd700;
  font-weight: bold;
}

.clue-tags-detail {
  margin-bottom: 20rpx;
}

.tags-label {
  color: #ccc;
  font-size: 26rpx;
  margin-bottom: 15rpx;
  display: block;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.tag-item {
  background: rgba(100, 149, 237, 0.3);
  color: #87ceeb;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 15rpx;
  border: 1px solid rgba(100, 149, 237, 0.5);
}

.modal-footer {
  padding: 30rpx;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
  border-radius: 20rpx;
  font-size: 28rpx;
  font-weight: bold;
}
