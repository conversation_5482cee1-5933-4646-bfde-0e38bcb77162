产品需求文档（PRD）
一、产品概述
本产品是一款基于微信小程序的 AI驱动多人实时社交推理游戏，通过AI生成个性化剧本、角色、线索与剧情分支，融合“真心话大冒险”“轻度解谜”“投票博弈”“小游戏互动”等元素，为玩家提供沉浸式、社交性强的娱乐体验。

二、产品目标
在微信生态下提供高流畅、多人同步的剧本杀类社交游戏体验

引入AI动态生成剧本与剧情发展，提升重复可玩性和沉浸感

融合“真心话”、“小游戏”、“AI点评”等社交玩法增强互动趣味性

确保用户隐私安全与内容合规，系统高并发稳定运行
采用微信小程序原生开发，使用WXML模板+WXSS样式+艺术化设计风格，适配iPhone15 Pro显示效果。

📁 小程序目录结构
```
miniprogram/
├── app.js              # 小程序入口文件
├── app.json            # 全局配置（页面路由、窗口样式等）
├── app.wxss            # 全局样式文件
├── pages/              # 页面目录
│   ├── home/           # 首页（房间列表）
│   │   ├── home.js     # 页面逻辑
│   │   ├── home.wxml   # 页面模板
│   │   └── home.wxss   # 页面样式
│   ├── room-create/    # 创建房间
│   ├── room-lobby/     # 房间大厅（等待玩家）
│   ├── game-role/      # 角色分配
│   ├── game-discuss/   # 讨论阶段
│   ├── game-minigame/  # 小游戏（你画我猜）
│   ├── game-vote/      # 投票阶段
│   ├── game-result/    # 结果展示
│   └── profile/        # 个人中心
├── components/         # 自定义组件
├── utils/              # 工具函数
└── images/             # 静态资源
```

🎨 设计规范
- **开发框架**：微信小程序原生开发（WXML + WXSS + JS）
- **页面路由**：通过app.json配置页面路径，使用wx.navigateTo等API进行页面跳转
- **模板语言**：WXML模板，支持数据绑定、条件渲染、列表渲染
- **样式系统**：WXSS样式，支持rpx响应式单位、CSS3动画
- **设备适配**：iPhone15 Pro（393×852px，状态栏44px，安全区域34px）
- **主色调**：深空渐变 #667eea → #764ba2 + 全息光效背景
- **字体**：微信小程序默认字体栈
- **交互动画**：WXSS动画（fadeIn/slideIn/scaleIn）
- **特色效果**：毛玻璃卡片、全息边框、动态光晕

三、核心流程设计
1. 游戏流程
创建/加入房间

等待玩家就绪

系统分配角色

剧情开场，展示案件背景

私密线索下发（仅玩家本人可见）

玩家讨论与推理（实时文字/语音聊天）

穿插小游戏与真心话环节（AI触发）

投票阶段 + 个人选择坚持机制

根据投票生成剧情分支

展示结果与游戏总结

四、功能需求明细
**1. 房间系统**

| 功能 | 说明 |
|------|------|
| 创建房间 | 生成6位房间号，设置密码，可通过微信邀请好友或二维码邀请入局 |
| 加入房间 | 支持输入房间号/扫码方式加入 |
| 房间管理 | 显示房主、玩家列表，房主可踢人/设置房间规则 |

**2. 剧本与角色系统（AI自动生成）**

| 功能 | 说明 |
|------|------|
| 角色分配 | 每位玩家分配不同身份、背景、技能 |
| 背景故事生成 | 每位角色拥有独立且关联的背景故事 |
| 人物关系网络 | AI生成角色间的冲突、秘密、目标等联系 |
| 私密线索分发 | 每人2-3条仅自己可见的关键信息 |

**3. 讨论与互动系统**

| 功能 | 说明 |
|------|------|
| 实时聊天 | 支持全员频道+私聊（可设定某些阶段禁私聊） |
| 支持文字/语音/表情 | 支持文字输入、语音留言、表情包互动 |
| 匿名发言机制 | 可在某些阶段启用匿名投票、匿名发言机制 |

**4. 小游戏系统**

| 功能 | 说明 |
|------|------|
| 触发机制 | AI监控游戏张力，在合适时机插入小游戏 |
| 小游戏类型 | - 快问快答（30秒内回答5题）<br>- 你画我猜<br>- 表情包大战<br>- 模仿秀 |

5. 真心话系统
功能	说明
问题类型	💕情感类 🎯梦想类 😅糗事类 🤔价值观 🎪趣味假设类
AI评价系统	对玩家回答进行点评（鼓励/吐槽），增加互动感
玩家评分机制	其他玩家打分：真诚度、有趣度、共鸣度，高分可获得奖励（线索/道具）

6. 投票与坚持机制
功能	说明
投票选择	每轮可选多个分支，玩家投票产生结果
坚持机制	投票前后询问玩家是否“坚持原选择”
投票后剧情分支生成	AI根据投票结果生成下一阶段剧情

7. 结果展示系统
功能	说明
最终结果展示	谁赢了/失败，各玩家最终身份揭晓
玩家选择回顾	展示每轮关键决策和转折点
玩家表现评价	AI根据发言、选择、表现等给予个性化点评
数据统计	- 个人胜率
- 真心话获赞最多
- 好友互动记录

五、非功能性需求
1. 性能要求
指标	要求
冷启动时间	≤2秒
页面滑动/切换	无明显卡顿
图片加载提示	进入页面1秒内显示加载提示
WebSocket延迟	≤200ms 实现实时通信

2. 安全与合规
类别	说明
用户登录	微信授权登录，使用 UnionID 做身份唯一标识
数据传输	使用 HTTPS + TLS 加密所有通信数据
内容安全审核	接入微信内容审查接口，对用户发言/图像内容进行安全校验
防作弊机制	投票锁定机制，AI检测作弊行为（如恶意灌票、频繁退出等）

六、UI设计与体验要求

| 项目 | 要求 |
|------|------|
| 界面组件 | 采用微信小程序原生组件 + 自定义WXSS艺术化样式，风格统一，性能最优 |
| 操作流畅 | 按钮点击有加载反馈，图片生成/脚本加载有进度提示 |
| 视觉风格 | 深空科技感、全息光效、沉浸感背景与动画效果辅助氛围塑造 |
| 动画反馈 | WXSS动画，如fadeIn、slideIn、scaleIn等轻量过渡 |
| 响应式适配 | 使用rpx单位适配不同尺寸手机屏幕，支持横竖屏切换 |
| 性能优势 | 原生组件保证最佳性能，无第三方依赖，包体积更小 |

**技术特色：**
- 毛玻璃卡片效果：`backdrop-filter: blur(30rpx)`
- 全息渐变边框：6s彩虹动画循环
- 深空渐变背景：5层渐变色彩叠加
- 动态光效：金色、紫色、蓝色光晕
- WXSS硬件加速：`transform3d`优化性能
- 原生组件优势：无兼容性问题，加载速度快

**🚀 技术优势对比：**

| 对比项 | 传统方案（TDesign） | 当前方案（原生+WXSS） |
|--------|-------------------|---------------------|
| 包体积 | ~500KB | ~50KB（减少90%） |
| 加载速度 | 需要下载组件库 | 即时加载 |
| 性能表现 | 第三方组件开销 | 原生组件最优 |
| 兼容性 | 依赖组件库版本 | 微信原生保证 |
| 定制化 | 受限于组件库 | 完全自定义 |
| 维护成本 | 需要跟随组件库更新 | 自主可控 |

七、技术架构

## 7.1 前端架构

| 模块 | 技术选型 |
|------|----------|
| 前端小程序 | 微信小程序原生开发（WXML + WXSS + JS） + WXS模块 |
| UI设计系统 | 艺术化WXSS设计：毛玻璃、全息光效、深空渐变 |
| 页面路由 | 微信小程序原生路由系统（wx.navigateTo、wx.redirectTo等） |
| 数据绑定 | WXML模板数据绑定 + setData更新机制 |
| 性能优化 | 原生组件 + WXSS硬件加速 + 无第三方依赖 |
| 实时通信 | WebSocket + Node.js 实时同步房间状态 |

## 7.2 后端技术架构（腾讯云开发）

### 核心技术栈

| 技术组件 | 选型方案 | 说明 |
|----------|----------|------|
| **云服务器** | 腾讯云CVM（2核4G起步） | 承载Node.js后端服务，支持弹性扩容 |
| **后端框架** | Node.js + Express.js | 轻量级、高性能，与微信小程序生态完美契合 |
| **数据库** | 腾讯云MongoDB + Redis | MongoDB存储游戏数据，Redis缓存热点数据 |
| **实时通信** | Socket.IO + WebSocket | 支持房间广播、私聊、实时状态同步 |
| **AI服务** | 腾讯云AI + OpenAI API | 剧本生成、角色创建、智能点评 |
| **文件存储** | 腾讯云COS对象存储 | 存储用户头像、语音、图片等静态资源 |
| **内容安全** | 腾讯云天御 + 微信内容安全 | 文本、图像、语音内容审核 |
| **负载均衡** | 腾讯云CLB | 支持高并发访问，自动故障转移 |

### 详细技术方案

#### 1. 服务器架构设计

```
┌─────────────────────────────────────────────────────────┐
│                    腾讯云负载均衡 CLB                      │
└─────────────────────┬───────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
   ┌────▼────┐   ┌────▼────┐   ┌────▼────┐
   │ Node.js │   │ Node.js │   │ Node.js │
   │ 服务器1  │   │ 服务器2  │   │ 服务器3  │
   └─────────┘   └─────────┘   └─────────┘
        │             │             │
        └─────────────┼─────────────┘
                      │
        ┌─────────────▼─────────────┐
        │        数据层              │
        │  MongoDB + Redis Cluster  │
        └───────────────────────────┘
```

#### 2. 核心服务模块

| 服务模块 | 技术实现 | 功能说明 |
|----------|----------|----------|
| **用户服务** | Express + JWT | 微信登录、用户信息管理、权限验证 |
| **房间服务** | Socket.IO + Redis | 房间创建、加入、状态管理、实时同步 |
| **游戏引擎** | Node.js + MongoDB | 游戏流程控制、状态机管理、规则引擎 |
| **AI服务** | Express + 腾讯云AI | 剧本生成、角色分配、智能点评 |
| **聊天服务** | Socket.IO + Redis | 实时聊天、语音转文字、表情包 |
| **投票服务** | Express + MongoDB | 投票逻辑、结果统计、分支生成 |
| **内容审核** | 腾讯云天御 | 文本、图像、语音内容安全检测 |

#### 3. 数据库设计

**MongoDB 集合设计：**

```javascript
// 用户集合
users: {
  _id: ObjectId,
  openid: String,        // 微信openid
  unionid: String,       // 微信unionid
  nickname: String,      // 用户昵称
  avatar: String,        // 头像URL
  gameStats: {           // 游戏统计
    totalGames: Number,
    winRate: Number,
    favoriteRole: String
  },
  createdAt: Date,
  updatedAt: Date
}

// 房间集合
rooms: {
  _id: ObjectId,
  roomCode: String,      // 6位房间号
  hostId: ObjectId,      // 房主ID
  status: String,        // waiting/playing/finished
  players: [{
    userId: ObjectId,
    nickname: String,
    avatar: String,
    role: String,        // 分配的角色
    isReady: Boolean
  }],
  gameConfig: {
    maxPlayers: Number,
    gameMode: String,
    difficulty: String
  },
  currentPhase: String,  // 当前游戏阶段
  createdAt: Date,
  expiresAt: Date       // 房间过期时间
}

// 游戏记录集合
games: {
  _id: ObjectId,
  roomId: ObjectId,
  script: {
    title: String,
    background: String,
    characters: Array,
    clues: Array
  },
  phases: [{
    phase: String,       // discussion/voting/result
    startTime: Date,
    endTime: Date,
    data: Object        // 阶段相关数据
  }],
  messages: [{
    userId: ObjectId,
    content: String,
    type: String,       // text/voice/image
    timestamp: Date,
    isPrivate: Boolean
  }],
  votes: [{
    phase: String,
    userId: ObjectId,
    choice: String,
    timestamp: Date
  }],
  result: {
    winner: String,
    playerScores: Array,
    aiComments: Array
  },
  createdAt: Date
}
```

**Redis 缓存策略：**

```javascript
// 房间状态缓存（5分钟过期）
room:{roomCode} = {
  players: Array,
  currentPhase: String,
  gameState: Object
}

// 用户在线状态（30分钟过期）
user:online:{userId} = {
  roomCode: String,
  lastActive: Timestamp,
  socketId: String
}

// 热点数据缓存
hot:scripts = Array     // 热门剧本
hot:rooms = Array       // 活跃房间列表
```

#### 4. API 接口设计

**RESTful API 规范：**

```javascript
// 用户相关
POST   /api/auth/login          // 微信登录
GET    /api/user/profile        // 获取用户信息
PUT    /api/user/profile        // 更新用户信息

// 房间相关
POST   /api/room/create         // 创建房间
POST   /api/room/join           // 加入房间
GET    /api/room/:code          // 获取房间信息
PUT    /api/room/:code/ready    // 玩家准备
DELETE /api/room/:code/leave    // 离开房间

// 游戏相关
POST   /api/game/start          // 开始游戏
GET    /api/game/:id/script     // 获取剧本
GET    /api/game/:id/clues      // 获取个人线索
POST   /api/game/:id/vote       // 投票
GET    /api/game/:id/result     // 获取结果

// AI相关
POST   /api/ai/generate-script  // 生成剧本
POST   /api/ai/generate-comment // 生成点评
POST   /api/ai/content-check    // 内容审核
```

**WebSocket 事件设计：**

```javascript
// 客户端发送事件
socket.emit('join-room', { roomCode, userId })
socket.emit('send-message', { content, type, isPrivate })
socket.emit('player-ready', { isReady })
socket.emit('submit-vote', { choice })

// 服务端广播事件
socket.broadcast.emit('player-joined', playerInfo)
socket.broadcast.emit('new-message', messageData)
socket.broadcast.emit('phase-changed', { phase, data })
socket.broadcast.emit('vote-result', voteData)
```

#### 5. 部署架构

**腾讯云服务配置：**

| 服务 | 规格配置 | 用途说明 |
|------|----------|----------|
| **CVM云服务器** | 2核4G（可扩展至8核16G） | 运行Node.js应用服务 |
| **MongoDB** | 副本集3节点，每节点2核4G | 主数据存储，支持读写分离 |
| **Redis集群** | 3主3从，每节点1核2G | 缓存热点数据，Session存储 |
| **CLB负载均衡** | 标准型 | 流量分发，健康检查 |
| **COS对象存储** | 标准存储 | 静态资源、用户上传文件 |
| **CDN加速** | 全球加速 | 静态资源分发，降低延迟 |

**容器化部署（推荐）：**

```dockerfile
# Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["node", "server.js"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongo:27017/game
      - REDIS_URL=redis://redis:6379
    depends_on:
      - mongo
      - redis

  mongo:
    image: mongo:5.0
    volumes:
      - mongo_data:/data/db

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  mongo_data:
  redis_data:
```

#### 6. 安全策略

| 安全层面 | 技术方案 | 实现细节 |
|----------|----------|----------|
| **身份认证** | JWT + 微信OAuth | 使用微信授权登录，JWT token有效期2小时 |
| **API安全** | HTTPS + 请求签名 | 所有API使用HTTPS，关键接口增加签名验证 |
| **数据加密** | AES-256 + RSA | 敏感数据AES加密存储，传输使用RSA加密 |
| **访问控制** | RBAC权限模型 | 基于角色的权限控制，房主/玩家权限分离 |
| **防刷机制** | Redis限流 + IP黑名单 | 接口限流（100次/分钟），异常IP自动封禁 |
| **内容审核** | 腾讯云天御 | 实时审核文本、图片、语音内容 |

#### 7. 性能优化策略

**缓存策略：**
```javascript
// 多级缓存架构
L1: 应用内存缓存（Node.js Memory）- 热点数据，1分钟过期
L2: Redis缓存 - 用户会话、房间状态，5-30分钟过期
L3: MongoDB - 持久化数据存储
L4: CDN缓存 - 静态资源，24小时过期
```

**数据库优化：**
```javascript
// MongoDB索引策略
db.rooms.createIndex({ "roomCode": 1 })           // 房间号查询
db.rooms.createIndex({ "status": 1, "createdAt": -1 }) // 房间列表
db.users.createIndex({ "openid": 1 })             // 用户登录
db.games.createIndex({ "roomId": 1, "createdAt": -1 }) // 游戏记录

// 分片策略（高并发场景）
sh.shardCollection("gamedb.games", { "roomId": "hashed" })
```

**连接池配置：**
```javascript
// MongoDB连接池
const mongoOptions = {
  maxPoolSize: 50,        // 最大连接数
  minPoolSize: 5,         // 最小连接数
  maxIdleTimeMS: 30000,   // 连接空闲时间
  serverSelectionTimeoutMS: 5000
}

// Redis连接池
const redisOptions = {
  host: 'redis-cluster',
  port: 6379,
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  lazyConnect: true,
  maxmemoryPolicy: 'allkeys-lru'
}
```

#### 8. 监控与运维

**监控指标：**

| 监控类型 | 关键指标 | 告警阈值 |
|----------|----------|----------|
| **系统监控** | CPU使用率、内存使用率、磁盘IO | CPU>80%, 内存>85% |
| **应用监控** | QPS、响应时间、错误率 | 响应时间>500ms, 错误率>1% |
| **数据库监控** | 连接数、慢查询、锁等待 | 连接数>80%, 慢查询>100ms |
| **业务监控** | 在线用户数、房间创建数、游戏完成率 | 完成率<70% |

**日志管理：**
```javascript
// 使用Winston进行结构化日志
const winston = require('winston')

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
    new winston.transports.Console()
  ]
})

// 关键业务日志
logger.info('Room created', {
  roomCode: 'ABC123',
  hostId: 'user123',
  timestamp: new Date()
})
```

#### 9. 成本预估

**月度成本预算（预计1000并发用户）：**

| 服务项目 | 配置规格 | 月费用（元） | 说明 |
|----------|----------|-------------|------|
| CVM云服务器 | 2核4G × 3台 | 900 | 应用服务器集群 |
| MongoDB | 2核4G × 3节点 | 1200 | 数据库副本集 |
| Redis集群 | 1核2G × 6节点 | 800 | 缓存集群 |
| CLB负载均衡 | 标准型 | 200 | 流量分发 |
| COS对象存储 | 100GB | 50 | 静态资源存储 |
| CDN流量 | 500GB | 150 | 内容分发 |
| 带宽费用 | 100M | 500 | 网络带宽 |
| **总计** | - | **3800元/月** | 支持1000并发用户 |

**扩容方案：**
- **5000并发**：服务器扩展至6台，数据库升级至4核8G，预算约8000元/月
- **10000并发**：引入微服务架构，数据库分片，预算约15000元/月

#### 10. 开发环境配置

**本地开发环境：**
```bash
# 环境要求
Node.js >= 18.0.0
MongoDB >= 5.0
Redis >= 6.0
Docker >= 20.10

# 项目初始化
git clone <repository>
cd game-backend
npm install
cp .env.example .env

# 启动开发环境
docker-compose -f docker-compose.dev.yml up -d
npm run dev

# 数据库初始化
npm run db:seed
```

**环境变量配置：**
```bash
# .env 文件
NODE_ENV=development
PORT=3000

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/game_dev
REDIS_URL=redis://localhost:6379

# 微信小程序配置
WECHAT_APP_ID=wxa9e14950d650cc0c
WECHAT_APP_SECRET=your_app_secret

# 腾讯云配置
TENCENT_SECRET_ID=your_secret_id
TENCENT_SECRET_KEY=your_secret_key
COS_BUCKET=your_bucket_name
COS_REGION=ap-guangzhou

# AI服务配置
OPENAI_API_KEY=your_openai_key
TENCENT_AI_APP_ID=your_ai_app_id
```

#### 11. CI/CD 流水线

**GitHub Actions 配置：**
```yaml
# .github/workflows/deploy.yml
name: Deploy to Tencent Cloud

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run tests
      run: npm test

    - name: Run linting
      run: npm run lint

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Build Docker image
      run: |
        docker build -t game-backend:${{ github.sha }} .
        docker tag game-backend:${{ github.sha }} game-backend:latest

    - name: Deploy to Tencent Cloud
      run: |
        # 推送镜像到腾讯云容器镜像服务
        docker push ccr.ccs.tencentyun.com/namespace/game-backend:latest

        # 更新云服务器上的容器
        ssh user@server "docker pull ccr.ccs.tencentyun.com/namespace/game-backend:latest"
        ssh user@server "docker-compose up -d --no-deps app"
```

**代码质量控制：**
```json
// package.json scripts
{
  "scripts": {
    "dev": "nodemon server.js",
    "start": "node server.js",
    "test": "jest --coverage",
    "test:watch": "jest --watch",
    "lint": "eslint . --ext .js",
    "lint:fix": "eslint . --ext .js --fix",
    "format": "prettier --write .",
    "db:seed": "node scripts/seed.js",
    "db:migrate": "node scripts/migrate.js"
  }
}
```

#### 12. 技术风险评估

| 风险类型 | 风险描述 | 影响程度 | 应对策略 |
|----------|----------|----------|----------|
| **高并发风险** | 用户激增导致服务器压力 | 高 | 自动扩容、负载均衡、缓存优化 |
| **数据一致性** | 多人游戏状态同步问题 | 中 | Redis分布式锁、事务处理 |
| **AI服务稳定性** | 第三方AI服务不稳定 | 中 | 多供应商备份、本地缓存 |
| **内容安全风险** | 用户生成内容违规 | 高 | 多重审核机制、人工复审 |
| **网络延迟** | 实时通信延迟过高 | 中 | CDN加速、就近部署 |

#### 13. 技术选型对比

**后端框架对比：**

| 框架 | 优势 | 劣势 | 适用场景 |
|------|------|------|----------|
| **Express.js** ✅ | 轻量、生态丰富、学习成本低 | 缺少内置功能 | 快速开发、中小型项目 |
| Koa.js | 现代化、中间件机制好 | 生态相对较小 | 追求代码质量的项目 |
| Nest.js | 企业级、TypeScript支持 | 学习成本高、较重 | 大型企业项目 |

**数据库对比：**

| 数据库 | 优势 | 劣势 | 选择理由 |
|--------|------|------|----------|
| **MongoDB** ✅ | 文档型、扩展性好、适合游戏数据 | 事务支持有限 | 游戏数据结构灵活 |
| MySQL | 事务完整、生态成熟 | 扩展性相对较差 | 传统业务系统 |
| PostgreSQL | 功能强大、标准SQL | 学习成本较高 | 复杂查询场景 |

**实时通信对比：**

| 技术 | 优势 | 劣势 | 选择理由 |
|------|------|------|----------|
| **Socket.IO** ✅ | 自动降级、房间管理、易用 | 包体积较大 | 游戏实时通信首选 |
| 原生WebSocket | 性能最优、标准协议 | 需要自己处理重连等 | 对性能要求极高的场景 |
| Server-Sent Events | 单向通信、简单 | 只支持服务端推送 | 简单的推送场景 |


八、开发计划（版本迭代）
阶段	关键功能	开发周期
MVP阶段	房间系统 + 角色分配 + 投票讨论流程	4 周
第二阶段	AI剧本与私密线索生成	+3 周
第三阶段	小游戏、真心话系统 + AI点评	+4 周
第四阶段	分支剧情生成 + 数据统计结果展示	+3 周
上线准备阶段	性能优化 + 内容审核 + 微信审核合规	+2 周

九、关键成功指标（KPI）
指标项	目标值
用户留存率（7日）	≥ 30%
单局平均时长	≥ 15 分钟
平均NPS推荐值	≥ 50
系统稳定性	Uptime ≥ 99.9%；延迟≤200ms
错误率	< 1%
这个API key我的示例代码如下：
from openai import OpenAI
 
client = OpenAI(
    api_key = "$MOONSHOT_API_KEY",
    base_url = "https://api.moonshot.cn/v1",
)
 
completion = client.chat.completions.create(
    model = "kimi-k2-0711-preview",
    messages = [
        {"role": "system", "content": "You are Kimi, an AI assistant provided by Moonshot AI. You are proficient in Chinese and English conversations. You provide users with safe, helpful, and accurate answers. You will reject any questions involving terrorism, racism, or explicit content. Moonshot AI is a proper noun and should not be translated."},
        {"role": "user", "content": "Hello, my name is Li Lei. What is 1+1?"}
    ],
    temperature = 0.6,
)
 
print(completion.choices[0].message.content)
这个Api key如下：
sk-rFun7AywY7jUUdJAtUBbFD