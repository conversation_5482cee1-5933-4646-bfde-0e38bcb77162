// test/persistence-mechanism-test.js
// 个人坚持机制功能测试

const aiService = require('../utils/ai-service');
const AIPrompts = require('../utils/ai-prompts');

/**
 * 测试个人坚持机制功能
 */
class PersistenceMechanismTest {
  constructor() {
    this.testResults = [];
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始个人坚持机制功能测试...\n');

    try {
      await this.testPersistencePromptGeneration();
      await this.testInfluenceCalculation();
      await this.testDefaultPersistenceOptions();
      await this.testInfluenceValidation();
      await this.testDifferentRoleScenarios();
      
      this.printTestSummary();
    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error);
    }
  }

  /**
   * 测试坚持机制提示词生成
   */
  async testPersistencePromptGeneration() {
    console.log('📝 测试坚持机制提示词生成...');
    
    const testContext = {
      voteResult: '深入调查神秘访客',
      playerRole: '管家',
      currentInfluence: 65,
      roleStatus: '中等',
      currentRound: 2,
      plotImportance: '高',
      characterBackground: '庄园的忠实管家，掌握许多秘密'
    };

    try {
      const prompt = AIPrompts.getPersistencePrompt(testContext);
      
      if (prompt && prompt.length > 100) {
        console.log('✅ 坚持机制提示词生成成功');
        console.log(`   提示词长度: ${prompt.length} 字符`);
        this.addTestResult('坚持机制提示词生成', true, '提示词生成正常');
      } else {
        throw new Error('提示词内容不足');
      }
    } catch (error) {
      console.log('❌ 坚持机制提示词生成失败:', error.message);
      this.addTestResult('坚持机制提示词生成', false, error.message);
    }
  }

  /**
   * 测试影响力计算
   */
  async testInfluenceCalculation() {
    console.log('📊 测试影响力计算...');
    
    const testContext = {
      characterName: '管家',
      characterType: '特殊角色',
      roleStatus: '中等',
      currentInfluence: 55,
      performanceScore: 75,
      speakCount: 8,
      clueShared: 3,
      agreementCount: 5,
      correctDeductions: 2,
      voteAccuracy: 80,
      teamworkScore: 70,
      roleplayScore: 85,
      persistenceUsed: 1
    };

    try {
      // 测试默认影响力计算
      const defaultResult = aiService.getDefaultInfluenceCalculation(testContext);
      
      if (defaultResult && defaultResult.currentInfluence && defaultResult.influenceLevel) {
        console.log('✅ 默认影响力计算成功');
        console.log(`   当前影响力: ${defaultResult.currentInfluence}`);
        console.log(`   影响力等级: ${defaultResult.influenceLevel}`);
        this.addTestResult('影响力计算', true, '计算结果正常');
      } else {
        throw new Error('影响力计算结果不完整');
      }
    } catch (error) {
      console.log('❌ 影响力计算失败:', error.message);
      this.addTestResult('影响力计算', false, error.message);
    }
  }

  /**
   * 测试默认坚持机制选择
   */
  async testDefaultPersistenceOptions() {
    console.log('🎯 测试默认坚持机制选择...');
    
    const testContext = {
      voteResult: '立即搜查所有房间',
      playerRole: '庄园主',
      currentInfluence: 85,
      roleStatus: '极高',
      currentRound: 3,
      plotImportance: '极高'
    };

    try {
      const options = aiService.getDefaultPersistenceOptions(testContext);
      
      if (options && options.persistenceOptions && options.persistenceOptions.length === 4) {
        console.log('✅ 默认坚持机制选择生成成功');
        console.log(`   选择数量: ${options.persistenceOptions.length}`);
        
        // 验证选择结构
        const firstOption = options.persistenceOptions[0];
        if (firstOption.type && firstOption.name && firstOption.description) {
          console.log('✅ 选择结构验证通过');
          this.addTestResult('默认坚持机制选择', true, '选择生成正常');
        } else {
          throw new Error('选择结构不完整');
        }
      } else {
        throw new Error('选择数量不正确');
      }
    } catch (error) {
      console.log('❌ 默认坚持机制选择失败:', error.message);
      this.addTestResult('默认坚持机制选择', false, error.message);
    }
  }

  /**
   * 测试影响力验证
   */
  async testInfluenceValidation() {
    console.log('🔍 测试影响力验证...');
    
    const testCases = [
      { influence: 25, expectedAvailable: [0, 1] }, // 低影响力只能选择前两个
      { influence: 55, expectedAvailable: [0, 1, 2] }, // 中等影响力可选择前三个
      { influence: 85, expectedAvailable: [0, 1, 2, 3] } // 高影响力可选择所有
    ];

    let passedCases = 0;

    for (const testCase of testCases) {
      try {
        const context = {
          currentInfluence: testCase.influence,
          playerRole: '测试角色',
          voteResult: '测试投票'
        };
        
        const options = aiService.getDefaultPersistenceOptions(context);
        const availableOptions = options.persistenceOptions.filter(
          option => option.influenceCost <= testCase.influence
        );

        if (availableOptions.length >= testCase.expectedAvailable.length) {
          console.log(`✅ 影响力${testCase.influence}验证通过`);
          passedCases++;
        } else {
          console.log(`❌ 影响力${testCase.influence}验证失败`);
        }
      } catch (error) {
        console.log(`❌ 影响力${testCase.influence}验证出错:`, error.message);
      }
    }

    const success = passedCases === testCases.length;
    this.addTestResult('影响力验证', success, `${passedCases}/${testCases.length} 测试用例通过`);
  }

  /**
   * 测试不同角色场景
   */
  async testDifferentRoleScenarios() {
    console.log('👥 测试不同角色场景...');
    
    const roleScenarios = [
      { role: '庄园主', status: '极高', expectedInfluence: 90 },
      { role: '管家', status: '中等', expectedInfluence: 50 },
      { role: '女仆', status: '低等', expectedInfluence: 35 }
    ];

    let passedScenarios = 0;

    for (const scenario of roleScenarios) {
      try {
        const context = {
          voteResult: '继续调查',
          playerRole: scenario.role,
          currentInfluence: scenario.expectedInfluence,
          roleStatus: scenario.status,
          currentRound: 1,
          plotImportance: '中'
        };

        const options = aiService.getDefaultPersistenceOptions(context);
        
        if (options && options.persistenceOptions && options.contextAnalysis) {
          console.log(`✅ ${scenario.role}场景测试通过`);
          passedScenarios++;
        } else {
          console.log(`❌ ${scenario.role}场景测试失败`);
        }
      } catch (error) {
        console.log(`❌ ${scenario.role}场景测试出错:`, error.message);
      }
    }

    const success = passedScenarios === roleScenarios.length;
    this.addTestResult('不同角色场景', success, `${passedScenarios}/${roleScenarios.length} 场景通过`);
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, success, message) {
    this.testResults.push({
      name: testName,
      success: success,
      message: message,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 打印测试总结
   */
  printTestSummary() {
    console.log('\n📋 测试总结:');
    console.log('=' * 50);
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(result => result.success).length;
    const failedTests = totalTests - passedTests;

    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests} ✅`);
    console.log(`失败: ${failedTests} ❌`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    console.log('\n详细结果:');
    this.testResults.forEach((result, index) => {
      const status = result.success ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.name}: ${result.message}`);
    });

    if (passedTests === totalTests) {
      console.log('\n🎉 所有测试通过！个人坚持机制功能正常。');
    } else {
      console.log('\n⚠️  部分测试失败，请检查相关功能。');
    }
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const test = new PersistenceMechanismTest();
  test.runAllTests().catch(console.error);
}

module.exports = PersistenceMechanismTest;
