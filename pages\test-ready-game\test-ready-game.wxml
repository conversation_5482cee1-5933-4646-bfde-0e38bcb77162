<!--测试准备开始游戏功能页面-->
<view class="container">
  <view class="header">
    <text class="title">测试准备开始游戏功能</text>
    <text class="subtitle">验证所有边界条件和异常情况</text>
  </view>

  <!-- 控制按钮 -->
  <view class="controls">
    <button
      class="control-btn start"
      bind:tap="startTests"
      disabled="{{isRunning}}"
    >
      开始测试
    </button>

    <button
      class="control-btn clear"
      bind:tap="clearResults"
      disabled="{{isRunning}}"
    >
      清除结果
    </button>
  </view>

  <!-- 进度条 -->
  <view class="progress-section" wx:if="{{isRunning || progress > 0}}">
    <view class="progress-label">测试进度: {{progress}}%</view>
    <view class="progress-bar">
      <view class="progress-fill" style="width: {{progress}}%"></view>
    </view>
  </view>

  <!-- 当前测试 -->
  <view class="current-test" wx:if="{{currentTest}}">
    <text class="current-test-label">当前测试:</text>
    <text class="current-test-name">{{currentTest}}</text>
  </view>

  <!-- 测试结果 -->
  <view class="test-results" wx:if="{{testResults.length > 0}}">
    <view class="results-header">
      <text class="results-title">测试结果</text>
      <text class="results-summary">{{successCount}}/{{totalCount}} 通过</text>
    </view>

    <view class="result-item" wx:for="{{testResults}}" wx:key="index">
      <view class="result-header">
        <text class="result-icon">{{item.success ? '✅' : '❌'}}</text>
        <text class="result-name">{{item.name}}</text>
        <text class="result-time">{{item.time}}</text>
      </view>
      <text class="result-message">{{item.message}}</text>
    </view>
  </view>

  <!-- 测试说明 -->
  <view class="test-info" wx:if="{{!isRunning && testResults.length === 0}}">
    <text class="info-title">测试内容</text>
    <view class="info-list">
      <text class="info-item">• 房主单人无法开始游戏</text>
      <text class="info-item">• 人数不足无法开始游戏</text>
      <text class="info-item">• 未全部准备无法开始游戏</text>
      <text class="info-item">• 满足条件可以开始游戏</text>
      <text class="info-item">• 准备状态切换功能</text>
      <text class="info-item">• 房主权限验证</text>
    </view>
  </view>

  <!-- 最终结果 -->
  <view class="final-result" wx:if="{{currentTest === '测试完成'}}">
    <view class="result-card {{allTestsPassed ? 'success' : 'warning'}}">
      <text class="result-emoji">{{allTestsPassed ? '🎉' : '⚠️'}}</text>
      <text class="result-text">{{allTestsPassed ? '所有测试通过！' : '部分测试失败'}}</text>
      <text class="result-detail">通过率: {{successCount}}/{{totalCount}}</text>
    </view>
  </view>
</view>
