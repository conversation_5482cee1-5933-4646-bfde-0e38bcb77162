// 人物关系页面逻辑
Page({
  data: {
    roomId: '',
    loading: false,
    relations: [
      { from: '艾米丽', to: '威廉', relation: '妻子', type: 'family' },
      { from: '约翰', to: '威廉', relation: '商业伙伴', type: 'business' },
      { from: '玛丽', to: '艾米丽', relation: '闺蜜', type: 'friend' },
      { from: '汤姆', to: '约翰', relation: '竞争对手', type: 'enemy' },
      { from: '莉莉', to: '威廉', relation: '秘书', type: 'work' },
      { from: '杰克', to: '玛丽', relation: '暗恋', type: 'love' }
    ]
  },

  onLoad(options) {
    console.log('人物关系页面加载', options);
    if (options.roomId) {
      this.setData({ roomId: options.roomId });
    }
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },

  // 继续到讨论阶段
  continueToDiscussion() {
    if (this.data.loading) return;

    this.setData({ loading: true });

    console.log('跳转到讨论阶段', this.data.roomId);

    setTimeout(() => {
      wx.navigateTo({
        url: `/pages/discussion/discussion?roomId=${this.data.roomId}`,
        success: (res) => {
          console.log('跳转讨论阶段成功', res);
          this.setData({ loading: false });
        },
        fail: (err) => {
          console.error('跳转讨论阶段失败', err);
          this.setData({ loading: false });
          wx.showToast({
            title: '页面跳转失败',
            icon: 'error'
          });
        }
      });
    }, 1000);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})