/* 角色分配页面样式 */
.role-assignment-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
  color: white;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: rgba(26, 26, 46, 0.95);
  backdrop-filter: blur(20rpx);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  padding-top: calc(20rpx + env(safe-area-inset-top));
}

.navbar-title {
  color: white;
  font-size: 36rpx;
  font-weight: 600;
}

/* 分配状态 */
.assignment-status {
  padding: 32rpx;
  margin-top: calc(100rpx + env(safe-area-inset-top));
}

.status-card {
  display: flex;
  align-items: center;
  gap: 24rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 32rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.status-icon {
  flex-shrink: 0;
}

.rotating {
  animation: rotate 2s linear infinite;
}

.status-text {
  flex: 1;
}

.status-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.status-desc {
  font-size: 26rpx;
  color: #ccc;
  line-height: 1.5;
}

/* 区块标题 */
.section-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 30rpx;
  font-weight: 600;
  margin: 32rpx 32rpx 24rpx;
  color: white;
}

/* 我的角色卡片 */
.my-role-section {
  padding: 0 32rpx;
}

.role-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  border-radius: 28rpx;
  padding: 32rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  margin-bottom: 24rpx;
}

.my-role-card {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
  border: 2rpx solid rgba(102, 126, 234, 0.3);
  box-shadow: 0 16rpx 48rpx rgba(102, 126, 234, 0.2);
}

.role-header {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.role-avatar {
  flex-shrink: 0;
}

.role-info {
  flex: 1;
}

.role-name {
  font-size: 40rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
  color: white;
}

.role-title {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 16rpx;
}

.role-tags {
  display: flex;
  gap: 12rpx;
}

.role-description, .role-objectives, .role-secrets {
  margin-bottom: 24rpx;
}

.desc-title, .obj-title, .secrets-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  color: #e8e8e8;
}

.desc-content {
  font-size: 28rpx;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  background: rgba(0, 0, 0, 0.3);
  padding: 24rpx;
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
}

.obj-list, .secrets-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.obj-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  padding: 16rpx 20rpx;
  background: rgba(102, 126, 234, 0.15);
  border-radius: 16rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.3);
}

.secret-item {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  padding: 20rpx;
  background: rgba(255, 107, 107, 0.15);
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 107, 107, 0.3);
  line-height: 1.5;
}

/* 其他玩家角色 */
.other-roles-section {
  padding: 0 32rpx;
}

.roles-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.other-role-card {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.08);
  transition: all 0.3s ease;
}

.other-role-card:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.12);
}

.role-avatar-small {
  flex-shrink: 0;
}

.role-basic-info {
  flex: 1;
}

.role-name-small {
  font-size: 26rpx;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.role-player {
  font-size: 22rpx;
  color: #999;
}

.role-mystery {
  flex-shrink: 0;
}

/* 关系提示 */
.relationship-hint {
  padding: 0 32rpx;
  margin-bottom: 32rpx;
}

.hint-card {
  background: rgba(255, 193, 7, 0.1);
  border: 2rpx solid rgba(255, 193, 7, 0.3);
  border-radius: 24rpx;
  padding: 24rpx;
}

.hint-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
  color: #ffc107;
}

.hint-content {
  font-size: 24rpx;
  color: #fff3cd;
  line-height: 1.5;
}

/* 底部按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(26, 26, 46, 0.95);
  backdrop-filter: blur(20rpx);
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  border-top: 2rpx solid rgba(255, 255, 255, 0.1);
  display: flex;
  gap: 16rpx;
}

.secondary-btn {
  flex: 1;
  background: rgba(255, 255, 255, 0.1) !important;
  border: 2rpx solid rgba(255, 255, 255, 0.2) !important;
  color: white !important;
}

.primary-btn {
  flex: 2;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  font-weight: 600 !important;
}

/* 角色详情弹窗 */
.role-detail-content {
  padding: 16rpx 0;
  text-align: center;
}

.detail-avatar {
  margin-bottom: 24rpx;
}

.detail-info {
  text-align: left;
}

.detail-title {
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  color: #333;
}

.detail-player {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.detail-desc {
  font-size: 26rpx;
  color: #333;
  line-height: 1.6;
}

/* 动画 */
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* TDesign组件样式覆盖 */
.t-tag {
  background: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  border: 1rpx solid rgba(255, 255, 255, 0.3) !important;
}

.t-button--theme-primary {
  border-radius: 24rpx !important;
  font-size: 30rpx !important;
}

.t-button--theme-default {
  border-radius: 24rpx !important;
  font-size: 28rpx !important;
}
