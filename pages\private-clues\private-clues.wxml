<!--私人线索页面-->
<view class="private-clues-container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <t-icon name="chevron-left" size="48rpx" color="white" bind:tap="goBack" />
      <view class="navbar-title">私人线索</view>
      <view class="navbar-extra">
        <t-icon name="refresh" size="40rpx" color="white" bind:tap="refreshClues" />
      </view>
    </view>
  </view>

  <!-- 角色信息卡片 -->
  <view class="character-info-section">
    <view class="character-card">
      <view class="character-avatar">
        <t-avatar size="large" image="{{myCharacter.avatar}}">
          {{myCharacter.name.charAt(0)}}
        </t-avatar>
        <view class="character-status">
          <t-icon name="{{myCharacter.isAlive ? 'check-circle' : 'close-circle'}}"
                 size="24rpx"
                 color="{{myCharacter.isAlive ? '#52c41a' : '#ff4d4f'}}" />
        </view>
      </view>
      <view class="character-info">
        <view class="character-name">{{myCharacter.name}}</view>
        <view class="character-title">{{myCharacter.title}}</view>
        <view class="character-stats">
          <view class="stat-item">
            <t-icon name="lightbulb" size="20rpx" color="#ffc107" />
            <text>{{clues.length}}条线索</text>
          </view>
          <view class="stat-item">
            <t-icon name="target" size="20rpx" color="#667eea" />
            <text>{{completedObjectives}}/{{totalObjectives}}目标</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 线索分类标签 -->
  <view class="clue-tabs-section">
    <view class="clue-tabs">
      <view
        class="clue-tab {{activeTab === item.key ? 'active' : ''}}"
        wx:for="{{clueTabs}}"
        wx:key="key"
        bind:tap="switchTab"
        data-tab="{{item.key}}"
      >
        <t-icon name="{{item.icon}}" size="28rpx" />
        <text>{{item.label}}</text>
        <view class="tab-badge" wx:if="{{item.count > 0}}">{{item.count}}</view>
      </view>
    </view>
  </view>

  <!-- 线索列表 -->
  <view class="clues-section">
    <scroll-view class="clues-scroll" scroll-y="{{true}}" enhanced="{{true}}">
      <view class="clues-list">
        <view
          class="clue-card {{item.importance}}"
          wx:for="{{filteredClues}}"
          wx:key="id"
          bind:tap="viewClueDetail"
          data-clue-id="{{item.id}}"
        >
          <view class="clue-header">
            <view class="clue-icon">
              <t-icon name="{{getClueIcon(item.type)}}"
                     size="32rpx"
                     color="{{getClueColor(item.importance)}}" />
            </view>
            <view class="clue-title-area">
              <view class="clue-title">{{item.title}}</view>
              <view class="clue-meta">
                <t-tag theme="{{getClueTheme(item.importance)}}" size="small">
                  {{getImportanceText(item.importance)}}
                </t-tag>
                <text class="clue-time">{{item.discoveredTime}}</text>
              </view>
            </view>
            <view class="clue-status">
              <t-icon name="{{item.isRead ? 'check' : 'time'}}"
                     size="24rpx"
                     color="{{item.isRead ? '#52c41a' : '#ffc107'}}" />
            </view>
          </view>

          <view class="clue-preview">{{item.preview}}</view>

          <view class="clue-tags" wx:if="{{item.tags.length > 0}}">
            <view class="clue-tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">
              #{{tag}}
            </view>
          </view>

          <view class="clue-relations" wx:if="{{item.relatedCharacters.length > 0}}">
            <text class="relations-label">相关角色:</text>
            <view class="relations-avatars">
              <t-avatar
                size="small"
                wx:for="{{item.relatedCharacters}}"
                wx:key="id"
                wx:for-item="character"
                image="{{character.avatar}}"
              >
                {{character.name.charAt(0)}}
              </t-avatar>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{filteredClues.length === 0}}">
        <t-icon name="search" size="80rpx" color="#999" />
        <view class="empty-title">暂无{{activeTabLabel}}线索</view>
        <view class="empty-desc">继续游戏探索，发现更多线索</view>
      </view>
    </scroll-view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <t-button
      theme="default"
      size="medium"
      bind:tap="viewNotes"
      class="action-btn"
    >
      <t-icon name="edit" slot="prefixIcon" />
      笔记
    </t-button>
    <t-button
      theme="default"
      size="medium"
      bind:tap="shareClue"
      class="action-btn"
    >
      <t-icon name="share" slot="prefixIcon" />
      分享
    </t-button>
    <t-button
      theme="primary"
      size="medium"
      bind:tap="enterDiscussion"
      class="primary-action-btn"
    >
      <t-icon name="chat" slot="prefixIcon" />
      进入讨论
    </t-button>
  </view>
</view>

<!-- 线索详情弹窗 -->
<t-dialog
  id="clue-detail-dialog"
  title="{{selectedClue.title}}"
  visible="{{showClueDetail}}"
  bind:close="closeClueDetail"
  class="clue-detail-dialog"
>
  <view class="clue-detail-content" wx:if="{{selectedClue}}">
    <view class="detail-header">
      <view class="detail-importance">
        <t-tag theme="{{getClueTheme(selectedClue.importance)}}" size="medium">
          {{getImportanceText(selectedClue.importance)}}
        </t-tag>
      </view>
      <view class="detail-time">发现时间: {{selectedClue.discoveredTime}}</view>
    </view>

    <view class="detail-content">{{selectedClue.content}}</view>

    <view class="detail-analysis" wx:if="{{selectedClue.analysis}}">
      <view class="analysis-title">
        <t-icon name="lightbulb" size="24rpx" color="#ffc107" />
        <text>AI分析</text>
      </view>
      <view class="analysis-content">{{selectedClue.analysis}}</view>
    </view>

    <view class="detail-actions">
      <t-button theme="default" size="small" bind:tap="markAsImportant">
        <t-icon name="star" slot="prefixIcon" />
        标记重要
      </t-button>
      <t-button theme="primary" size="small" bind:tap="addToNotes">
        <t-icon name="edit" slot="prefixIcon" />
        添加笔记
      </t-button>
    </view>
  </view>
</t-dialog>

<!-- 提示组件 -->
<t-toast id="t-toast" />