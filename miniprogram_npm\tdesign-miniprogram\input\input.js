import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import{getCharacterLength,calcIcon}from"../common/utils";import{isDef}from"../common/validator";const{prefix:prefix}=config,name=`${prefix}-input`;let Input=class extends SuperComponent{constructor(){super(...arguments),this.options={multipleSlots:!0},this.externalClasses=[`${prefix}-class`,`${prefix}-class-prefix-icon`,`${prefix}-class-label`,`${prefix}-class-input`,`${prefix}-class-clearable`,`${prefix}-class-suffix`,`${prefix}-class-suffix-icon`,`${prefix}-class-tips`],this.behaviors=["wx://form-field"],this.properties=props,this.data={prefix:prefix,classPrefix:name,classBasePrefix:prefix,showClearIcon:!0},this.lifetimes={ready(){var e;const{value:t,defaultValue:i}=this.properties;this.updateValue(null!==(e=null!=t?t:i)&&void 0!==e?e:"")}},this.observers={prefixIcon(e){this.setData({_prefixIcon:calcIcon(e)})},suffixIcon(e){this.setData({_suffixIcon:calcIcon(e)})},clearable(e){this.setData({_clearIcon:calcIcon(e,"close-circle-filled")})},"clearTrigger, clearable, disabled, readonly"(){this.updateClearIconVisible()}},this.methods={updateValue(e){const{allowInputOverMax:t,maxcharacter:i,maxlength:r}=this.properties;if(!t&&i&&i>0&&!Number.isNaN(i)){const{length:t,characters:r}=getCharacterLength("maxcharacter",e,i);this.setData({value:r,count:t})}else if(!t&&r&&r>0&&!Number.isNaN(r)){const{length:t,characters:i}=getCharacterLength("maxlength",e,r);this.setData({value:i,count:t})}else this.setData({value:e,count:isDef(e)?String(e).length:0})},updateClearIconVisible(e=!1){const{clearTrigger:t,disabled:i,readonly:r}=this.properties;i||r?this.setData({showClearIcon:!1}):this.setData({showClearIcon:e||"always"===t})},onInput(e){const{value:t,cursor:i,keyCode:r}=e.detail;this.updateValue(t),this.triggerEvent("change",{value:this.data.value,cursor:i,keyCode:r})},onFocus(e){this.updateClearIconVisible(!0),this.triggerEvent("focus",e.detail)},onBlur(e){if(this.updateClearIconVisible(),"function"==typeof this.properties.format){const t=this.properties.format(e.detail.value);return this.updateValue(t),void this.triggerEvent("blur",{value:this.data.value,cursor:this.data.count})}this.triggerEvent("blur",e.detail)},onConfirm(e){this.triggerEvent("enter",e.detail)},onSuffixClick(){this.triggerEvent("click",{trigger:"suffix"})},onSuffixIconClick(){this.triggerEvent("click",{trigger:"suffix-icon"})},clearInput(e){this.triggerEvent("clear",e.detail),this.setData({value:""})},onKeyboardHeightChange(e){this.triggerEvent("keyboardheightchange",e.detail)},onNickNameReview(e){this.triggerEvent("nicknamereview",e.detail)}}}};Input=__decorate([wxComponent()],Input);export default Input;