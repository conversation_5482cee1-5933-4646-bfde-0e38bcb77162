/* pages/api-settings/api-settings.wxss */
.container {
  padding: 40rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

/* 当前密钥状态 */
.current-key-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.key-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.key-display {
  flex: 1;
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}

.key-text {
  font-family: 'Courier New', monospace;
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
}

.status-indicator {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.status-indicator.valid {
  background-color: #d4edda;
  color: #155724;
}

.status-indicator.invalid {
  background-color: #f8d7da;
  color: #721c24;
}

.test-btn {
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 10rpx;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
}

.test-btn[disabled] {
  background-color: #ccc;
  color: #666;
}

/* 输入区域 */
.input-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.input-group {
  margin-bottom: 40rpx;
}

.api-key-input {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 2rpx solid #ddd;
  border-radius: 10rpx;
  font-size: 28rpx;
  font-family: 'Courier New', monospace;
  background-color: #f8f9fa;
  box-sizing: border-box;
}

.api-key-input:focus {
  border-color: #007bff;
  background-color: white;
}

.validation-message {
  margin-top: 20rpx;
  font-size: 24rpx;
  padding: 10rpx;
  border-radius: 8rpx;
}

.validation-message.valid {
  background-color: #d4edda;
  color: #155724;
}

.validation-message.invalid {
  background-color: #f8d7da;
  color: #721c24;
}

.button-group {
  display: flex;
  gap: 20rpx;
}

.save-btn, .clear-btn {
  flex: 1;
  padding: 24rpx;
  border: none;
  border-radius: 10rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.save-btn {
  background-color: #28a745;
  color: white;
}

.save-btn[disabled] {
  background-color: #ccc;
  color: #666;
}

.clear-btn {
  background-color: #dc3545;
  color: white;
}

.clear-btn[disabled] {
  background-color: #ccc;
  color: #666;
}

/* 使用说明 */
.instructions-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.instruction-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  background-color: #007bff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 20rpx;
}

.step-text {
  font-size: 28rpx;
  color: #333;
}

.help-btn {
  width: 100%;
  background-color: #17a2b8;
  color: white;
  border: none;
  border-radius: 10rpx;
  padding: 24rpx;
  font-size: 28rpx;
  margin-top: 30rpx;
}

.test-page-btn {
  width: 100%;
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 10rpx;
  padding: 24rpx;
  font-size: 28rpx;
  margin-top: 20rpx;
}

/* 安全提醒 */
.security-notice {
  background: #fff3cd;
  border: 2rpx solid #ffeaa7;
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: flex-start;
}

.notice-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.notice-content {
  flex: 1;
}

.notice-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #856404;
  margin-bottom: 10rpx;
}

.notice-text {
  font-size: 24rpx;
  color: #856404;
  line-height: 1.5;
}
