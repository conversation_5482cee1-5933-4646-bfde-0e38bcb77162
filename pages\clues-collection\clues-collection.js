// 线索收集页面
const aiService = require('../../utils/ai-service-simple');
const roomManager = require('../../utils/room-manager');

Page({
  data: {
    roomId: '',
    myRole: null,
    clues: [],
    filteredClues: [],
    activeFilter: 'all',
    filters: [
      { id: 'all', name: '全部', count: 0 },
      { id: 'key', name: '关键', count: 0 },
      { id: 'person', name: '人物', count: 0 },
      { id: 'location', name: '地点', count: 0 },
      { id: 'item', name: '物品', count: 0 }
    ],
    loading: false,
    selectedClue: null,
    showClueDetail: false,
    gamePhase: 'clue_collection', // 游戏阶段
    timeRemaining: 600 // 10分钟倒计时
  },

  onLoad(options) {
    const { roomId } = options;
    if (roomId) {
      this.setData({ roomId });
      this.loadRoleInfo();
      // 在loadRoleInfo之后再生成线索
      this.startTimer();
    }
  },

  // 加载角色信息
  loadRoleInfo() {
    try {
      // 从本地存储获取角色分配信息
      const roleAssignment = wx.getStorageSync(`role_assignment_${this.data.roomId}`);
      const scriptData = wx.getStorageSync(`script_${this.data.roomId}`);

      if (roleAssignment && roleAssignment.myRole) {
        // 增强角色信息，添加剧本标题
        const enhancedRole = {
          ...roleAssignment.myRole,
          scriptTitle: scriptData?.storyInfo?.title || '神秘剧本'
        };

        this.setData({ myRole: enhancedRole });
        console.log('✅ 角色信息加载成功:', enhancedRole.name, '剧本:', enhancedRole.scriptTitle);
      } else {
        // 如果没有角色信息，使用默认角色
        this.setData({
          myRole: {
            name: '艾米丽·哈特',
            title: '庄园女主人',
            objectives: ['找出真相', '保护秘密'],
            scriptTitle: scriptData?.storyInfo?.title || '神秘庄园'
          }
        });
      }

      // 角色信息加载完成后，开始生成线索
      this.generateClues();

    } catch (error) {
      console.error('❌ 加载角色信息失败:', error);
      // 即使加载失败，也尝试生成默认线索
      this.loadDefaultClues();
    }
  },

  // 生成线索
  async generateClues() {
    this.setData({ loading: true });

    try {
      console.log('🔍 开始AI生成线索...');

      // 获取剧本数据
      const scriptData = wx.getStorageSync(`script_${this.data.roomId}`);

      if (!scriptData) {
        throw new Error('未找到剧本数据');
      }

      console.log('📖 剧本数据:', scriptData.storyInfo?.title);
      console.log('🎭 当前角色:', this.data.myRole?.name);

      // 使用AI生成线索
      const cluesData = await aiService.generateClues({
        scriptData: scriptData,
        myRole: this.data.myRole,
        gamePhase: this.data.gamePhase,
        clueCount: 6
      });

      console.log('🤖 AI生成结果:', cluesData);

      // 处理生成的线索
      const processedClues = this.processClues(cluesData);

      this.setData({
        clues: processedClues,
        filteredClues: processedClues
      });

      // 更新过滤器计数
      this.updateFilterCounts();

      console.log('✅ 线索处理完成，共', processedClues.length, '条线索');

      // 显示成功提示
      if (cluesData.fallback) {
        wx.showToast({
          title: 'AI生成失败，使用智能模板',
          icon: 'none',
          duration: 2000
        });
      } else {
        wx.showToast({
          title: `AI生成了${processedClues.length}条线索`,
          icon: 'success',
          duration: 2000
        });
      }

    } catch (error) {
      console.error('❌ 线索生成失败:', error);
      // 使用默认线索
      this.loadDefaultClues();

      wx.showToast({
        title: '生成失败，使用默认线索',
        icon: 'none',
        duration: 2000
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载默认线索
  loadDefaultClues() {
    console.log('📝 加载默认线索...');

    const defaultClues = [
      {
        id: 'default_001',
        title: '神秘的日记',
        content: '在房间的抽屉里发现了一本日记，记录着一些奇怪的事件。日记的最后一页被撕掉了，似乎隐藏着重要的秘密。',
        type: '物品',
        importance: '关键',
        tags: ['秘密', '线索'],
        relatedCharacters: [],
        discoveredAt: new Date().toISOString(),
        status: 'discovered'
      },
      {
        id: 'default_002',
        title: '破损的照片',
        content: '一张破损的老照片，显示几个人站在一起，但其中一个人的脸被刮花了。背面写着一个日期和地点。',
        type: '物品',
        importance: '普通',
        tags: ['照片', '过去'],
        relatedCharacters: [],
        discoveredAt: new Date().toISOString(),
        status: 'discovered'
      },
      {
        id: 'default_003',
        title: '管家的证词',
        content: '管家声称在事发当晚听到了争吵声，但他拒绝透露更多细节。他的眼神闪烁，似乎在隐瞒什么。',
        type: '人物',
        importance: '关键',
        tags: ['证词', '争吵'],
        relatedCharacters: ['管家'],
        discoveredAt: new Date().toISOString(),
        status: 'discovered'
      },
      {
        id: 'default_004',
        title: '地下室的钥匙',
        content: '在花园里发现了一把生锈的钥匙，看起来像是地下室的钥匙。钥匙上还沾着新鲜的泥土。',
        type: '物品',
        importance: '普通',
        tags: ['钥匙', '地下室'],
        relatedCharacters: [],
        discoveredAt: new Date().toISOString(),
        status: 'discovered'
      },
      {
        id: 'default_005',
        title: '书房的密室',
        content: '书房的书架后面隐藏着一个密室，里面有一些重要的文件和一个保险箱。保险箱需要密码才能打开。',
        type: '地点',
        importance: '关键',
        tags: ['密室', '文件'],
        relatedCharacters: [],
        discoveredAt: new Date().toISOString(),
        status: 'discovered'
      }
    ];

    this.setData({
      clues: defaultClues,
      discoveredCount: defaultClues.length,
      totalCount: defaultClues.length
    });

    console.log('✅ 默认线索加载完成');
  },

  // 处理AI生成的线索
  processClues(cluesData) {
    console.log('🔄 处理线索数据:', cluesData);

    // 如果AI生成失败，使用默认线索
    if (!cluesData || !cluesData.clues || !Array.isArray(cluesData.clues)) {
      console.log('⚠️ 线索数据无效，使用默认线索');
      return this.getDefaultClues();
    }

    const processedClues = cluesData.clues.map((clue, index) => {
      return {
        id: clue.id || `clue_${Date.now()}_${index}`,
        title: clue.title || `线索${index + 1}`,
        content: clue.content || '暂无详细信息',
        type: clue.type || 'item',
        importance: clue.importance || 'normal',
        timeLimit: clue.timeLimit || Math.floor(Math.random() * 20) + 10,
        tags: Array.isArray(clue.tags) ? clue.tags : [],
        relatedCharacters: Array.isArray(clue.relatedCharacters) ? clue.relatedCharacters : [],
        discovered: clue.discovered !== undefined ? clue.discovered : (index < 2),
        discoveredAt: clue.discoveredAt || (index < 2 ? new Date().toISOString() : null)
      };
    });

    console.log('✅ 线索处理完成:', processedClues);
    return processedClues;
  },

  // 获取默认线索
  getDefaultClues() {
    return [
      {
        id: 'clue_001',
        title: '神秘的日记',
        content: '在书房的抽屉里发现了一本日记，记录着庄园主人的秘密往事...',
        type: 'item',
        importance: 'key',
        timeLimit: 10,
        tags: ['日记', '秘密', '书房'],
        relatedCharacters: ['艾米丽'],
        discovered: true,
        discoveredAt: new Date().toISOString()
      },
      {
        id: 'clue_002',
        title: '破损的照片',
        content: '在壁炉旁发现了一张被撕毁的老照片，照片中有两个人...',
        type: 'item',
        importance: 'normal',
        timeLimit: 15,
        tags: ['照片', '机器', '过去'],
        relatedCharacters: ['艾米丽', '詹姆斯'],
        discovered: true,
        discoveredAt: new Date().toISOString()
      },
      {
        id: 'clue_003',
        title: '管家的证词',
        content: '管家莉莉安声称在案发当晚听到了争吵声，但她拒绝透露更多细节...',
        type: 'person',
        importance: 'key',
        timeLimit: 25,
        tags: ['证词', '争吵', '当晚'],
        relatedCharacters: ['莉莉安'],
        discovered: false,
        discoveredAt: null
      },
      {
        id: 'clue_004',
        title: '地下室的钥匙',
        content: '在花园里找到了一把生锈的钥匙，似乎可以打开地下室的门...',
        type: 'item',
        importance: 'key',
        timeLimit: 20,
        tags: ['钥匙', '地下室', '花园'],
        relatedCharacters: [],
        discovered: false,
        discoveredAt: null
      },
      {
        id: 'clue_005',
        title: '书房的密室',
        content: '书房后面隐藏着一个密室，里面存放着重要的文件和证据...',
        type: 'location',
        importance: 'key',
        timeLimit: 30,
        tags: ['密室', '文件', '证据'],
        relatedCharacters: [],
        discovered: false,
        discoveredAt: null
      }
    ];
  },

  // 切换过滤器
  switchFilter(e) {
    const filterId = e.currentTarget.dataset.filter;
    this.setData({ activeFilter: filterId });
    this.filterClues(filterId);
  },

  // 过滤线索
  filterClues(filterId) {
    let filtered = this.data.clues;

    if (filterId !== 'all') {
      filtered = this.data.clues.filter(clue => {
        switch (filterId) {
          case 'key':
            return clue.importance === 'key';
          case 'person':
            return clue.type === 'person';
          case 'location':
            return clue.type === 'location';
          case 'item':
            return clue.type === 'item';
          default:
            return true;
        }
      });
    }

    this.setData({ filteredClues: filtered });
  },

  // 更新过滤器计数
  updateFilterCounts() {
    const filters = this.data.filters.map(filter => {
      let count = 0;
      
      if (filter.id === 'all') {
        count = this.data.clues.length;
      } else if (filter.id === 'key') {
        count = this.data.clues.filter(c => c.importance === 'key').length;
      } else {
        count = this.data.clues.filter(c => c.type === filter.id).length;
      }

      return { ...filter, count };
    });

    this.setData({ filters });
  },

  // 查看线索详情
  viewClueDetail(e) {
    const clueId = e.currentTarget.dataset.clueId;
    const clue = this.data.clues.find(c => c.id === clueId);
    
    if (clue) {
      // 标记线索为已发现
      if (!clue.discovered) {
        clue.discovered = true;
        clue.discoveredAt = new Date().toISOString();
        
        // 更新数据
        const clues = this.data.clues.map(c => c.id === clueId ? clue : c);
        this.setData({ clues });
        this.filterClues(this.data.activeFilter);
      }

      this.setData({
        selectedClue: clue,
        showClueDetail: true
      });
    }
  },

  // 关闭线索详情
  closeClueDetail() {
    this.setData({
      showClueDetail: false,
      selectedClue: null
    });
  },

  // 开始计时器
  startTimer() {
    this.timer = setInterval(() => {
      const timeRemaining = this.data.timeRemaining - 1;
      this.setData({ timeRemaining });

      if (timeRemaining <= 0) {
        this.endClueCollection();
      }
    }, 1000);
  },

  // 结束线索收集
  endClueCollection() {
    if (this.timer) {
      clearInterval(this.timer);
    }

    wx.showModal({
      title: '线索收集结束',
      content: '线索收集阶段已结束，即将进入讨论阶段',
      showCancel: false,
      success: () => {
        // 跳转到讨论阶段
        wx.redirectTo({
          url: `/pages/discussion/discussion?roomId=${this.data.roomId}`
        });
      }
    });
  },

  // 格式化时间
  formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  },

  // 页面卸载
  onUnload() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  }
});
