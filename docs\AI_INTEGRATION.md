# AI集成功能说明

## 概述

本项目集成了Moonshot AI（Kimi）API，为"AI推理大师"微信小程序提供智能剧本生成、角色分配和动态内容生成功能。

## 核心功能

### 1. AI剧本生成 (`utils/ai-service.js`)

**功能描述**: 根据用户选择的参数，智能生成完整的推理游戏剧本。

**主要特性**:
- 支持6种剧本类型：悬疑推理、恐怖惊悚、浪漫爱情、冒险探索、轻松喜剧、奇幻魔法
- 3个难度等级：简单、中等、困难
- 自动生成角色、线索、时间线、真心话问题等完整内容
- 确保游戏平衡性和逻辑一致性

**使用示例**:
```javascript
const aiService = require('./utils/ai-service');

const params = {
  storyType: 'mystery',      // 剧本类型
  playerCount: 6,            // 玩家数量
  difficulty: 'medium',      // 难度等级
  theme: '现代都市悬疑',      // 故事主题
  specialRequirements: '',   // 特殊要求
  gameSettings: {
    duration: '60-90分钟',
    rounds: '3轮',
    truthDare: true,
    miniGame: true
  }
};

const scriptData = await aiService.generateScript(params);
```

### 2. 智能角色分配

**功能描述**: 根据剧本内容和玩家列表，智能分配角色，确保游戏平衡。

**使用示例**:
```javascript
const playerIds = ['player_1', 'player_2', 'player_3', 'player_4', 'player_5', 'player_6'];
const assignmentResult = await aiService.generateRoleAssignment(scriptData, playerIds);
```

### 3. 个人坚持机制 🆕

**功能描述**: 允许玩家在集体投票后使用个人影响力改变剧情走向的创新机制。

**主要特性**:
- **智能选择生成**: 基于游戏状态和角色背景生成个性化选择
- **影响力系统**: 动态计算和管理玩家影响力
- **多层次选择**: 跟随集体、轻度坚持、强力坚持、极限坚持四个层次
- **风险评估**: 为每个选择提供风险等级和效果预测
- **策略建议**: 基于当前情况提供最优策略建议

**使用示例**:
```javascript
// 生成坚持机制选择
const persistenceContext = {
  voteResult: '深入调查神秘访客',
  playerRole: '管家',
  currentInfluence: 65,
  roleStatus: '中等',
  currentRound: 2,
  plotImportance: '高',
  characterBackground: '庄园的忠实管家，掌握许多秘密'
};

const persistenceOptions = await aiService.generatePersistenceOptions(persistenceContext);

// 计算影响力
const influenceContext = {
  characterName: '管家',
  roleStatus: '中等',
  currentInfluence: 65,
  performanceScore: 75,
  speakCount: 8,
  clueShared: 3
};

const influenceResult = await aiService.calculateInfluence(influenceContext);
```

### 4. 动态内容生成

**功能描述**: 根据游戏进程动态生成真心话问题等互动内容。

**使用示例**:
```javascript
const gameContext = {
  storyBackground: '神秘庄园谋杀案',
  currentPhase: '第二轮讨论',
  aliveCharacters: ['艾米丽', '约翰', '玛丽'],
  eliminatedCharacters: ['杰克']
};

const truthQuestions = await aiService.generateDynamicContent('truthQuestions', gameContext);
```

## 提示词系统 (`utils/ai-prompts.js`)

### 设计理念

采用模块化的提示词管理系统，确保AI生成内容的质量和一致性。

### 核心提示词

1. **系统提示词**: 定义AI的角色和专业能力
2. **剧本生成提示词**: 详细的剧本创作指导
3. **角色分配提示词**: 智能角色分配逻辑
4. **个人坚持机制提示词**: 生成坚持机制选择和影响力计算 🆕
5. **动态内容提示词**: 游戏过程中的内容生成

#### 个人坚持机制提示词详解

**坚持机制选择提示词** (`getPersistencePrompt`):
- 基于游戏上下文生成4个层次的坚持选择
- 包含详细的效果描述和风险评估
- 考虑角色背景和当前剧情的合理性
- 确保选择的吸引力与消耗成正比

**影响力计算提示词** (`getInfluenceCalculationPrompt`):
- 基于角色地位和游戏表现计算影响力
- 提供详细的影响力分解和等级描述
- 包含提升影响力的具体建议
- 计算各类坚持机制的可用次数

### 提示词特点

- **专业性**: 将AI定位为资深游戏设计师
- **详细性**: 提供具体的创作要求和格式规范
- **适配性**: 针对微信小程序环境优化
- **平衡性**: 确保游戏的公平性和娱乐性

## API配置

### Moonshot AI配置

```javascript
// API配置信息
const API_CONFIG = {
  baseURL: 'https://api.moonshot.cn/v1',
  apiKey: 'sk-rFun7AywY7jUUdJAtUBbFD',
  model: 'moonshot-v1-8k',
  timeout: 30000
};
```

### 请求参数

- **temperature**: 0.7-0.8 (创意性内容)
- **max_tokens**: 2000-3000 (根据内容复杂度)
- **timeout**: 30秒

## 错误处理

### 多层错误处理机制

1. **API级别**: 网络请求失败处理
2. **解析级别**: JSON格式错误处理
3. **业务级别**: 内容质量验证
4. **降级机制**: 提供默认内容作为备选

### 错误处理示例

```javascript
try {
  const scriptData = await aiService.generateScript(params);
} catch (error) {
  // 使用错误处理器显示友好提示
  errorHandler.showError(error, 'AI剧本生成', this);
  
  // 降级到默认内容
  const fallbackContent = this.generateOptimizedContent(this.data.selectedType);
}
```

## 性能优化

### 缓存策略

- 剧本数据本地存储: `wx.setStorageSync()`
- 避免重复生成相同内容
- 智能预加载常用剧本类型

### 请求优化

- 合理设置超时时间
- 批量处理相关请求
- 异步处理非关键内容

## 测试

### 测试文件: `test/ai-service-test.js`

**测试覆盖**:
- 剧本生成功能测试
- 角色分配功能测试
- 动态内容生成测试
- 性能测试
- 错误处理测试

**运行测试**:
```bash
node test/ai-service-test.js
```

## 使用流程

### 1. 页面集成

在需要AI功能的页面中引入服务:

```javascript
const aiService = require('../../utils/ai-service');
const errorHandler = require('../../utils/error-handler');
```

### 2. 调用AI服务

```javascript
// 生成剧本
const scriptData = await aiService.generateScript(params);

// 保存到本地存储
wx.setStorageSync(`script_${roomId}`, scriptData);

// 格式化显示
const displayContent = this.formatScriptForDisplay(scriptData);
```

### 3. 错误处理

```javascript
try {
  // AI服务调用
} catch (error) {
  console.error('AI服务失败:', error);
  errorHandler.showError(error, '功能名称', this);
  // 降级处理
}
```

## 最佳实践

### 1. 参数验证

在调用AI服务前验证输入参数的完整性和有效性。

### 2. 用户体验

- 显示加载状态
- 提供友好的错误提示
- 实现降级方案

### 3. 内容质量

- 验证生成内容的结构完整性
- 检查逻辑一致性
- 确保文本质量

### 4. 性能监控

- 记录API调用时间
- 监控成功率
- 优化慢查询

## 扩展功能

### 未来可扩展的功能

1. **剧本优化**: 根据用户反馈优化剧本内容
2. **个性化推荐**: 基于用户偏好推荐剧本类型
3. **多语言支持**: 支持不同语言的剧本生成
4. **实时互动**: 游戏过程中的实时AI辅助

### 扩展接口

```javascript
// 剧本优化
await aiService.optimizeScript(originalScript, feedback);

// 个性化推荐
await aiService.recommendStoryType(userPreferences);
```

## 注意事项

1. **API密钥安全**: 确保API密钥不被泄露
2. **请求频率**: 遵守API调用频率限制
3. **内容审核**: 确保生成内容符合平台规范
4. **用户隐私**: 不上传用户敏感信息

## 技术支持

如有问题，请查看:
- 错误日志: 控制台输出
- 测试文件: `test/ai-service-test.js`
- API文档: Moonshot AI官方文档
