var getBadgeValue = function (props) {
  if (props.dot) {
    return '';
  }
  if (isNaN(props.count) || isNaN(props.maxCount)) {
    return props.count;
  }
  return parseInt(props.count) > props.maxCount ? props.maxCount + '+' : props.count;
};

var hasUnit = function (unit) {
  return (
    unit.indexOf('px') > 0 ||
    unit.indexOf('rpx') > 0 ||
    unit.indexOf('em') > 0 ||
    unit.indexOf('rem') > 0 ||
    unit.indexOf('%') > 0 ||
    unit.indexOf('vh') > 0 ||
    unit.indexOf('vm') > 0
  );
};

var getBadgeStyles = function (props) {
  var styleStr = '';
  if (props.color) {
    styleStr += 'background:' + props.color + ';';
  }
  if (props.offset[0]) {
    styleStr +=
      'left: calc(100% + ' + (hasUnit(props.offset[0].toString()) ? props.offset[0] : props.offset[0] + 'px') + ');';
  }
  if (props.offset[1]) {
    styleStr += 'top:' + (hasUnit(props.offset[1].toString()) ? props.offset[1] : props.offset[1] + 'px') + ';';
  }
  return styleStr;
};

var getBadgeOuterClass = function (props) {
  var baseClass = 't-badge';
  var classNames = [baseClass, props.shape === 'ribbon' ? baseClass + '__ribbon-outer' : ''];
  return classNames.join(' ');
};

var getBadgeInnerClass = function (props) {
  var baseClass = 't-badge';
  var classNames = [
    baseClass + '--basic',
    props.dot ? baseClass + '--dot' : '',
    baseClass + '--' + props.size,
    baseClass + '--' + props.shape,
    !props.dot && props.count ? baseClass + '--count' : '',
  ];
  return classNames.join(' ');
};

var isShowBadge = function (props) {
  if (props.dot) {
    return true;
  }
  if (!props.showZero && !isNaN(props.count) && parseInt(props.count) === 0) {
    return false;
  }
  if (props.count == null) return false;
  return true;
};

module.exports.getBadgeValue = getBadgeValue;
module.exports.getBadgeStyles = getBadgeStyles;
module.exports.getBadgeOuterClass = getBadgeOuterClass;
module.exports.getBadgeInnerClass = getBadgeInnerClass;
module.exports.isShowBadge = isShowBadge;
