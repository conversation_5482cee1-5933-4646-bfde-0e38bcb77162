// test-simple.js - 简单的Node.js兼容测试
const MockAIService = require('./utils/mock-ai-service');

async function testMockService() {
  console.log('🧪 测试模拟AI服务');
  console.log('================================');

  const mockService = new MockAIService();

  // 1. 测试故事生成
  console.log('📖 1. 测试故事生成');
  try {
    const storyParams = {
      theme: '现代都市',
      playerCount: 4,
      difficulty: 'easy'
    };

    console.log('生成参数:', storyParams);
    const startTime = Date.now();
    
    const story = await mockService.generateStoryPrompt(storyParams);
    
    const endTime = Date.now();
    console.log(`✅ 故事生成成功 (耗时: ${endTime - startTime}ms)`);
    console.log('生成的故事:');
    console.log(JSON.stringify(story, null, 2));
    console.log('');

    // 2. 测试角色生成
    console.log('👥 2. 测试角色生成');
    const characters = await mockService.generateCharacters(story, storyParams.playerCount);
    console.log('✅ 角色生成成功');
    console.log('生成的角色:');
    characters.forEach(char => {
      console.log(`- ${char.name} (${char.role}): ${char.description}`);
      console.log(`  秘密: ${char.secrets}`);
    });
    console.log('');

    // 3. 测试线索生成
    console.log('🔍 3. 测试线索生成');
    const clues = await mockService.generateClues(story, characters);
    console.log('✅ 线索生成成功');
    console.log('生成的线索:');
    clues.forEach(clue => {
      console.log(`- [${clue.importance}] ${clue.description}`);
    });
    console.log('');

    // 4. 测试服务状态
    console.log('📊 4. 测试服务状态');
    const status = await mockService.checkServiceStatus();
    console.log('✅ 服务状态检查完成');
    console.log('状态信息:', status);
    console.log('');

    // 5. 测试不同主题
    console.log('🎭 5. 测试不同主题');
    const themes = ['古代宫廷', '科幻未来'];
    for (const theme of themes) {
      console.log(`\n测试主题: ${theme}`);
      const themeStory = await mockService.generateStoryPrompt({
        theme: theme,
        playerCount: 3,
        difficulty: 'medium'
      });
      console.log(`- 标题: ${themeStory.title}`);
      console.log(`- 背景: ${themeStory.background.substring(0, 50)}...`);
    }

  } catch (error) {
    console.log('❌ 测试失败:', error.message);
    console.log('错误详情:', error);
  }

  console.log('');
  console.log('🎉 模拟服务测试完成！');
  console.log('================================');
  
  console.log('📝 测试总结:');
  console.log('- ✅ 故事生成功能正常');
  console.log('- ✅ 角色生成功能正常');
  console.log('- ✅ 线索生成功能正常');
  console.log('- ✅ 服务状态检查正常');
  console.log('- ✅ 多主题支持正常');
  
  console.log('');
  console.log('💡 模拟AI服务特点:');
  console.log('   - 🎭 提供丰富的预设故事模板');
  console.log('   - ⚡ 快速响应，无需网络连接');
  console.log('   - 🎲 随机化元素保证内容多样性');
  console.log('   - 🔧 完全兼容真实AI服务接口');
  console.log('   - 🚀 适合开发和测试环境使用');
}

// 运行测试
console.log('🚀 启动模拟AI服务测试');
console.log('时间:', new Date().toLocaleString());
console.log('');

testMockService().catch(error => {
  console.error('💥 测试过程中发生错误:', error);
  process.exit(1);
});
