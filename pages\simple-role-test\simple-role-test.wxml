<!--简单角色分配测试页面-->
<view class="container">
  <view class="header">
    <text class="title">🧪 简单角色分配测试</text>
    <text class="subtitle">快速验证角色分配功能</text>
  </view>

  <!-- 测试按钮 -->
  <view class="test-controls">
    <button 
      class="test-btn primary" 
      bind:tap="testRoleAssignment"
      loading="{{isLoading}}"
      disabled="{{isLoading}}"
    >
      {{isLoading ? '测试中...' : '🚀 开始测试'}}
    </button>
    
    <button class="test-btn secondary" bind:tap="clearResult">
      🗑️ 清空结果
    </button>
  </view>

  <!-- 测试结果 -->
  <view class="result-section" wx:if="{{testResult}}">
    <view class="result-header">
      <text class="result-title">📊 测试结果</text>
    </view>
    <view class="result-content">
      <text class="result-text">{{testResult}}</text>
    </view>
  </view>

  <!-- 导航按钮 -->
  <view class="navigation">
    <button class="nav-btn" bind:tap="goToRoleAssignment">
      🎭 角色分配页面
    </button>
    <button class="nav-btn" bind:tap="goToFullTest">
      🔬 完整测试页面
    </button>
  </view>

  <!-- 说明信息 -->
  <view class="info-section">
    <text class="info-title">ℹ️ 测试说明</text>
    <view class="info-list">
      <text class="info-item">• 测试AI服务模块加载</text>
      <text class="info-item">• 测试房间创建和玩家管理</text>
      <text class="info-item">• 测试剧本生成功能</text>
      <text class="info-item">• 测试角色分配算法</text>
      <text class="info-item">• 验证分配结果正确性</text>
    </view>
  </view>
</view>
