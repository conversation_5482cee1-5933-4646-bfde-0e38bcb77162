// 游戏结果页面逻辑
Page({
  data: {
    roomId: '',
    gameResult: {
      icon: '🎉',
      title: '游戏结束',
      subtitle: '真相大白，正义得到伸张！',
      truth: '经过激烈的讨论和推理，玩家们成功找出了真凶。原来庄园主人威廉·布莱克是被他的商业伙伴约翰谋害的，动机是为了独吞一笔巨额投资...'
    },
    playersPerformance: [
      { id: 1, name: '玩家1', role: '侦探', avatar: '🕵️', won: true },
      { id: 2, name: '玩家2', role: '凶手', avatar: '😈', won: false },
      { id: 3, name: '玩家3', role: '平民', avatar: '👤', won: true },
      { id: 4, name: '玩家4', role: '平民', avatar: '👤', won: true },
      { id: 5, name: '玩家5', role: '共犯', avatar: '😰', won: false },
      { id: 6, name: '玩家6', role: '平民', avatar: '👤', won: true }
    ],
    gameStats: {
      duration: '85分钟',
      rounds: '3轮',
      votes: '18票',
      messages: '127条'
    }
  },

  onLoad(options) {
    console.log('游戏结果页面加载', options);
    if (options.roomId) {
      this.setData({ roomId: options.roomId });
    }
  },

  // 返回首页
  backToHome() {
    wx.reLaunch({
      url: '/pages/home/<USER>'
    });
  },

  // 再玩一局
  playAgain() {
    wx.reLaunch({
      url: '/pages/room-create/room-create'
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})