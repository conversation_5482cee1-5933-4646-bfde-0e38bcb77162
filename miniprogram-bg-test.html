<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小程序地球星空背景测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'PingFang SC', sans-serif;
            overflow-x: hidden;
        }

        /* 地球星空背景首页 - 小程序版本 */
        .home-container {
            min-height: 100vh;
            background: 
                /* 星空效果 */
                radial-gradient(1px 1px at 50px 75px, #fff, transparent),
                radial-gradient(0.5px 0.5px at 100px 40px, rgba(255,255,255,0.8), transparent),
                radial-gradient(0.5px 0.5px at 150px 100px, #fff, transparent),
                radial-gradient(1px 1px at 75px 150px, rgba(255,255,255,0.6), transparent),
                radial-gradient(0.5px 0.5px at 175px 60px, #fff, transparent),
                radial-gradient(0.5px 0.5px at 40px 125px, rgba(255,255,255,0.7), transparent),
                radial-gradient(1px 1px at 140px 25px, #fff, transparent),
                radial-gradient(0.5px 0.5px at 90px 175px, rgba(255,255,255,0.9), transparent),
                /* 深空渐变背景 */
                linear-gradient(180deg, #000428 0%, #004e92 100%);
            padding: 0;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* 地球元素 */
        .home-container::before {
            content: '';
            position: absolute;
            bottom: -50px;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 200px;
            background: 
                radial-gradient(circle at 30% 40%, rgba(34, 139, 34, 0.8) 0%, transparent 50%),
                radial-gradient(circle at 70% 60%, rgba(30, 144, 255, 0.9) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(135, 206, 235, 0.7) 0%, rgba(65, 105, 225, 0.8) 70%, rgba(25, 25, 112, 0.9) 100%);
            border-radius: 50%;
            box-shadow: 
                0 0 50px rgba(135, 206, 235, 0.3),
                inset -25px -25px 50px rgba(0, 0, 0, 0.3);
            z-index: 1;
        }

        /* 背景遮罩层 - 确保文字可读性 */
        .home-container::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.2);
            z-index: 1;
        }

        /* 主标题区域 */
        .hero-section {
            text-align: center;
            padding: 100px 20px 60px;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .logo-text {
            font-size: 32px;
            font-weight: 300;
            color: #ffffff;
            letter-spacing: 1px;
            text-shadow: 0 1px 4px rgba(0, 0, 0, 0.5);
            margin-bottom: 16px;
        }

        .app-subtitle {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 400;
            letter-spacing: 0.5px;
            margin-bottom: 40px;
            text-shadow: 0 0.5px 2px rgba(0, 0, 0, 0.3);
        }

        /* 主操作按钮 */
        .main-actions {
            display: flex;
            flex-direction: column;
            gap: 12px;
            padding: 0 20px;
            margin-bottom: 40px;
            position: relative;
            z-index: 2;
        }

        .minimal-btn {
            width: 100%;
            height: 48px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .primary-btn {
            background: #000000;
            color: #ffffff;
        }

        .secondary-btn {
            background: rgba(255, 255, 255, 0.9);
            color: #000000;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        /* 快速加入区域 */
        .quick-join-section {
            display: flex;
            gap: 8px;
            padding: 0 20px;
            margin-bottom: 30px;
            position: relative;
            z-index: 2;
        }

        .minimal-input {
            flex: 1;
            height: 40px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 0 12px;
            font-size: 14px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .join-btn {
            width: 60px;
            height: 40px;
            background: #000000;
            color: #ffffff;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
        }

        /* 房间列表 */
        .rooms-section {
            padding: 0 20px;
            margin-bottom: 30px;
            position: relative;
            z-index: 2;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 16px;
            text-shadow: 0 1px 4px rgba(0, 0, 0, 0.5);
        }

        .room-card {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
            margin-bottom: 8px;
            cursor: pointer;
        }

        .room-name {
            font-size: 14px;
            font-weight: 600;
            color: #000000;
            margin-bottom: 4px;
        }

        .room-players {
            font-size: 12px;
            color: #666666;
        }

        .room-status {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 8px;
            font-weight: 500;
        }

        .room-status.waiting {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .room-status.playing {
            background: #fff3e0;
            color: #f57c00;
        }

        /* 移动端优化 */
        @media (max-width: 480px) {
            .hero-section {
                padding: 80px 15px 40px;
            }
            
            .logo-text {
                font-size: 28px;
            }
            
            .main-actions {
                padding: 0 15px;
            }
            
            .quick-join-section {
                padding: 0 15px;
            }
            
            .rooms-section {
                padding: 0 15px;
            }
        }
    </style>
</head>
<body>
    <div class="home-container">
        <!-- 主标题区域 -->
        <div class="hero-section">
            <div class="app-logo">
                <div class="logo-text">AI推理大师</div>
            </div>
            <div class="app-subtitle">智能剧本 · 沉浸推理</div>
        </div>

        <!-- 主操作按钮 -->
        <div class="main-actions">
            <button class="minimal-btn primary-btn">创建房间</button>
            <button class="minimal-btn secondary-btn">加入游戏</button>
        </div>

        <!-- 快速加入 -->
        <div class="quick-join-section">
            <input class="minimal-input" placeholder="输入房间号" maxlength="6" type="number" />
            <button class="join-btn">加入</button>
        </div>

        <!-- 热门房间 -->
        <div class="rooms-section">
            <div class="section-title">热门房间</div>
            <div class="rooms-list">
                <div class="room-card">
                    <div class="room-info">
                        <div class="room-name">神秘庄园</div>
                        <div class="room-players">3/6</div>
                    </div>
                    <div class="room-status waiting">等待中</div>
                </div>
                <div class="room-card">
                    <div class="room-info">
                        <div class="room-name">古堡疑云</div>
                        <div class="room-players">5/6</div>
                    </div>
                    <div class="room-status playing">进行中</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
