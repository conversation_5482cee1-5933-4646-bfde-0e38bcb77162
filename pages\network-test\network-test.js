// pages/network-test/network-test.js
const aiService = require('../../utils/ai-service');
const NetworkDiagnostic = require('../../utils/network-diagnostic');
const DomainConfigTest = require('../../test/domain-config-test');
const SimpleApiTest = require('../../test/simple-api-test');

Page({
  data: {
    testResults: [],
    isRunning: false,
    diagnosticResults: null,
    connectionStatus: 'unknown'
  },

  onLoad() {
    console.log('网络测试页面加载');
    // 暂时禁用自动测试，避免频率限制
    // this.runInitialTests();
    this.checkBasicNetwork();
  },

  /**
   * 运行初始测试
   */
  async runInitialTests() {
    this.setData({ isRunning: true });
    
    try {
      // 基础网络检查
      await this.checkBasicNetwork();
      
      // API连接测试
      await this.testApiConnection();
      
    } catch (error) {
      console.error('初始测试失败:', error);
      this.addTestResult('初始测试', false, error.message);
    } finally {
      this.setData({ isRunning: false });
    }
  },



  /**
   * 检查基础网络
   */
  async checkBasicNetwork() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          const isConnected = res.networkType !== 'none';
          this.setData({ connectionStatus: res.networkType });
          this.addTestResult('网络连接', isConnected, `网络类型: ${res.networkType}`);

          // 添加频率限制提示
          this.addTestResult('系统提示', true, '⚠️ API频率限制中，请等待2分钟后手动测试API连接');

          resolve();
        },
        fail: (error) => {
          this.addTestResult('网络连接', false, `获取网络状态失败: ${error.errMsg}`);
          resolve();
        }
      });
    });
  },

  /**
   * 测试API连接
   */
  async testApiConnection() {
    try {
      const success = await aiService.testConnection();
      this.addTestResult('API连接', success, success ? 'API连接正常' : 'API连接失败');
    } catch (error) {
      this.addTestResult('API连接', false, `API连接错误: ${error.message}`);
    }
  },

  /**
   * 运行完整诊断
   */
  async runFullDiagnostic() {
    this.setData({ isRunning: true });
    
    try {
      wx.showLoading({ title: '运行诊断中...' });
      
      const results = await aiService.runNetworkDiagnostic();
      this.setData({ diagnosticResults: results });
      
      wx.hideLoading();
      wx.showToast({
        title: '诊断完成',
        icon: 'success'
      });
      
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '诊断失败',
        icon: 'error'
      });
      console.error('诊断失败:', error);
    } finally {
      this.setData({ isRunning: false });
    }
  },

  /**
   * 测试域名配置
   */
  async testDomainConfig() {
    this.setData({ isRunning: true });

    try {
      wx.showLoading({ title: '检查域名配置...' });

      const results = await DomainConfigTest.runFullTest();

      wx.hideLoading();

      if (results.domainTest.success) {
        this.addTestResult('域名配置', true, '域名配置正确');
        wx.showToast({
          title: '域名配置正确',
          icon: 'success'
        });
      } else {
        this.addTestResult('域名配置', false, results.domainTest.message);
        wx.showModal({
          title: '域名配置问题',
          content: results.domainTest.message + '\n\n请在微信小程序后台添加: https://api.moonshot.cn',
          showCancel: false
        });
      }

    } catch (error) {
      wx.hideLoading();
      this.addTestResult('域名配置', false, `检查失败: ${error.message}`);
      wx.showToast({
        title: '检查失败',
        icon: 'error'
      });
    } finally {
      this.setData({ isRunning: false });
    }
  },

  /**
   * 验证API密钥
   */
  async validateApiKey() {
    this.setData({ isRunning: true });

    try {
      wx.showLoading({ title: '验证API密钥...' });

      const validator = new ApiKeyValidator();
      const results = await validator.runFullValidation(aiService.apiKey);

      wx.hideLoading();

      if (results.connectionTest.success) {
        this.addTestResult('API密钥验证', true, 'API密钥有效');
        wx.showToast({
          title: 'API密钥有效',
          icon: 'success'
        });
      } else {
        this.addTestResult('API密钥验证', false, results.connectionTest.message);

        let content = results.connectionTest.message;
        if (results.connectionTest.statusCode === 401) {
          content += '\n\n可能的解决方案:\n1. 检查API密钥是否正确\n2. 确认密钥是否已过期\n3. 联系API提供商';
        }

        wx.showModal({
          title: 'API密钥验证失败',
          content: content,
          showCancel: false
        });
      }

    } catch (error) {
      wx.hideLoading();
      this.addTestResult('API密钥验证', false, `验证失败: ${error.message}`);
      wx.showToast({
        title: '验证失败',
        icon: 'error'
      });
    } finally {
      this.setData({ isRunning: false });
    }
  },

  /**
   * 简单API测试
   */
  async testSimpleApi() {
    this.setData({ isRunning: true });

    try {
      wx.showLoading({ title: '运行简单API测试...' });

      const simpleTest = new SimpleApiTest();
      const results = await simpleTest.runFullTest();

      wx.hideLoading();

      if (results.basicTest.success) {
        this.addTestResult('简单API测试', true, '基础API测试通过');
        wx.showToast({
          title: 'API测试成功',
          icon: 'success'
        });
      } else {
        this.addTestResult('简单API测试', false, results.basicTest.message);

        let content = results.basicTest.message;
        if (results.basicTest.statusCode === 401) {
          content += '\n\n这表明API密钥认证失败。请检查:\n1. API密钥是否正确\n2. 密钥是否已过期\n3. 密钥权限是否足够';
        }

        wx.showModal({
          title: '简单API测试失败',
          content: content,
          showCancel: false
        });
      }

    } catch (error) {
      wx.hideLoading();
      this.addTestResult('简单API测试', false, `测试失败: ${error.message}`);
      wx.showToast({
        title: '测试失败',
        icon: 'error'
      });
    } finally {
      this.setData({ isRunning: false });
    }
  },

  /**
   * 测试简单请求
   */
  async testSimpleRequest() {
    this.setData({ isRunning: true });
    
    try {
      wx.showLoading({ title: '测试请求中...' });
      
      const testParams = {
        theme: '测试主题',
        playerCount: 4,
        difficulty: 'easy',
        gameType: 'mystery'
      };
      
      const result = await aiService.generateScript(testParams);
      
      wx.hideLoading();
      this.addTestResult('简单请求', true, '请求成功，AI响应正常');
      
      wx.showModal({
        title: '测试成功',
        content: 'AI服务响应正常，生成了测试剧本',
        showCancel: false
      });
      
    } catch (error) {
      wx.hideLoading();
      this.addTestResult('简单请求', false, `请求失败: ${error.message}`);
      
      wx.showModal({
        title: '测试失败',
        content: `请求失败: ${error.message}`,
        showCancel: false
      });
    } finally {
      this.setData({ isRunning: false });
    }
  },

  /**
   * 清除测试结果
   */
  clearResults() {
    this.setData({
      testResults: [],
      diagnosticResults: null
    });
  },

  /**
   * 添加测试结果
   */
  addTestResult(testName, success, message) {
    const result = {
      name: testName,
      success: success,
      message: message,
      timestamp: new Date().toLocaleTimeString()
    };
    
    const results = [...this.data.testResults, result];
    this.setData({ testResults: results });
  },

  /**
   * 复制诊断结果
   */
  copyDiagnosticResults() {
    if (!this.data.diagnosticResults) {
      wx.showToast({
        title: '没有诊断结果',
        icon: 'none'
      });
      return;
    }
    
    const text = JSON.stringify(this.data.diagnosticResults, null, 2);
    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });
      }
    });
  },

  /**
   * 查看详细建议
   */
  viewRecommendations() {
    if (!this.data.diagnosticResults || !this.data.diagnosticResults.recommendations) {
      wx.showToast({
        title: '没有建议信息',
        icon: 'none'
      });
      return;
    }
    
    const recommendations = this.data.diagnosticResults.recommendations;
    const content = recommendations.map((rec, index) => 
      `${index + 1}. ${rec.title}\n${rec.description}\n建议: ${rec.action}`
    ).join('\n\n');
    
    wx.showModal({
      title: '网络诊断建议',
      content: content,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 测试JSON解析修复功能
   */
  async testJsonParsing() {
    console.log('🔧 开始测试JSON解析修复功能...');
    this.addTestResult('JSON解析测试', null, '正在测试JSON解析修复功能...');

    try {
      const aiServiceInstance = getApp().globalData.aiService;

      // 测试各种问题JSON
      const testCases = [
        {
          name: '退格字符问题',
          json: '{\n  "\\bstoryInfo\\b": {\n    "\\btitle\\b": "测试标题"\n  }\n}'
        },
        {
          name: '代码块包装',
          json: '```json\n{"title": "测试"}\n```'
        },
        {
          name: '非法转义字符',
          json: '{"\\astory\\e": "测试"}'
        }
      ];

      let successCount = 0;
      let totalCount = testCases.length;
      let details = [];

      for (const testCase of testCases) {
        try {
          // 尝试直接解析
          JSON.parse(testCase.json);
          details.push(`✅ ${testCase.name}: 直接解析成功`);
          successCount++;
        } catch (directError) {
          // 尝试修复后解析
          try {
            const fixed = aiServiceInstance.superJsonFix(testCase.json);
            if (fixed) {
              JSON.parse(fixed);
              details.push(`🔧 ${testCase.name}: 修复后解析成功`);
              successCount++;
            } else {
              details.push(`❌ ${testCase.name}: 修复失败`);
            }
          } catch (fixError) {
            details.push(`❌ ${testCase.name}: 修复后仍失败`);
          }
        }
      }

      const successRate = (successCount / totalCount * 100).toFixed(1);
      const isSuccess = successCount === totalCount;

      this.addTestResult(
        'JSON解析测试',
        isSuccess,
        `测试完成 (${successCount}/${totalCount} 成功，成功率: ${successRate}%)\n\n${details.join('\n')}`
      );

      if (isSuccess) {
        wx.showToast({
          title: 'JSON解析功能正常',
          icon: 'success'
        });
      } else {
        wx.showModal({
          title: 'JSON解析测试结果',
          content: `部分测试失败\n成功率: ${successRate}%\n\n${details.join('\n')}`,
          showCancel: false
        });
      }

    } catch (error) {
      console.error('JSON解析测试失败:', error);
      this.addTestResult('JSON解析测试', false, `测试失败: ${error.message}`);
      wx.showToast({
        title: 'JSON解析测试失败',
        icon: 'error'
      });
    }
  },

  /**
   * 分享诊断结果
   */
  onShareAppMessage() {
    return {
      title: '网络诊断工具',
      path: '/pages/network-test/network-test'
    };
  }
});
