// app.js
const api = require('./utils/api');
const common = require('./utils/common');
const store = require('./utils/store');
const errorHandler = require('./utils/error-handler');
const CloudEnvChecker = require('./utils/cloud-env-checker');

App({
  onLaunch(options) {
    console.log('小程序启动', options);

    // 初始化应用
    this.initApp();

    // 检查更新
    this.checkForUpdate();

    // 记录启动日志
    this.recordLaunchLog();
  },

  onShow(options) {
    console.log('小程序显示', options);

    // 检查网络状态
    this.checkNetworkStatus();

    // 刷新用户信息
    this.refreshUserInfo();
  },

  onHide() {
    console.log('小程序隐藏');

    // 保存应用状态
    this.saveAppState();
  },

  onError(error) {
    console.error('小程序错误:', error);

    // 错误上报
    this.reportError(error);
  },

  // 初始化应用
  async initApp() {
    try {
      // 初始化云开发
      await this.initCloudDevelopment();

      // 获取系统信息
      const systemInfo = await common.getSystemInfo();
      this.globalData.systemInfo = systemInfo;

      // 检查登录状态
      await this.checkLoginStatus();

      // 初始化用户信息
      await this.initUserInfo();

    } catch (error) {
      console.error('应用初始化失败:', error);
    }
  },

  // 初始化云开发
  async initCloudDevelopment() {
    if (!wx.cloud) {
      console.error('❌ 请使用 2.2.3 或以上的基础库以使用云能力');
      return;
    }

    // 根据诊断结果，直接使用第一个可用环境
    const availableEnvs = [
      'cloud1-1g88tqlcd2222f2e',  // 在微信小程序后台找到的环境
      'cloud1-7ggoe4se871c08ae'   // 备用环境
    ];

    for (const envId of availableEnvs) {
      try {
        console.log(`☁️ 尝试初始化云开发环境: ${envId}`);

        wx.cloud.init({
          env: envId,
          traceUser: true
        });

        console.log(`✅ 云开发初始化成功，环境: ${envId}`);
        this.globalData.cloudEnabled = true;
        this.globalData.cloudEnvId = envId;

        // 简单测试连接（不调用云函数）
        console.log('☁️ 云开发环境已就绪，可以开始使用');
        return; // 成功则退出

      } catch (error) {
        console.warn(`☁️ 环境 ${envId} 初始化失败:`, error);

        // 如果是最后一个环境也失败了
        if (envId === availableEnvs[availableEnvs.length - 1]) {
          console.error('☁️ 所有云开发环境初始化失败');
          this.globalData.cloudEnabled = false;
          this.showCloudInitError(error);
        }
      }
    }
  },

  // 显示云开发设置指南
  showCloudSetupGuide(report) {
    console.log('📖 显示云开发设置指南');

    const recommendation = report.environmentTests.recommendation;

    if (recommendation.action === 'create_new_env') {
      console.log('💡 建议创建新的云开发环境');
      console.log('📋 设置步骤:', recommendation.steps);
    }

    // 保存诊断报告到全局数据，供测试页面使用
    this.globalData.cloudDiagnosticReport = report;
  },

  // 显示云开发初始化错误
  showCloudInitError(error) {
    const errorCode = error.errCode || error.code;
    let message = '云开发初始化失败';

    if (errorCode === -501000) {
      message = '云开发环境与小程序不匹配，请检查环境ID和AppID关联';
    } else if (errorCode === -501001) {
      message = '云开发环境不存在或已删除';
    } else {
      message = `云开发初始化失败: ${error.errMsg || error.message}`;
    }

    console.error('☁️ 云开发错误详情:', {
      errorCode,
      message,
      currentAppId: 'wxe5962f9a3d2cdd06',
      availableEnvs: ['cloud1-7ggoe4se871c08ae', 'cloud1-1g88tqlcd2222f2e']
    });
  },



  // 检查登录状态
  async checkLoginStatus() {
    const token = common.getStorage('token');
    if (token) {
      try {
        // 验证token有效性
        // const res = await api.verifyToken();
        this.globalData.isLoggedIn = true;
      } catch (error) {
        // token无效，清除本地存储
        common.removeStorage('token');
        common.removeStorage('userInfo');
        this.globalData.isLoggedIn = false;
      }
    } else {
      this.globalData.isLoggedIn = false;
    }
  },

  // 初始化用户信息
  async initUserInfo() {
    if (this.globalData.isLoggedIn) {
      try {
        // const userInfo = await api.getUserInfo();
        // this.globalData.userInfo = userInfo.data;

        // 从本地存储获取用户信息
        const localUserInfo = common.getStorage('userInfo');
        if (localUserInfo) {
          this.globalData.userInfo = localUserInfo;
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }
    }
  },

  // 刷新用户信息
  async refreshUserInfo() {
    if (this.globalData.isLoggedIn) {
      try {
        // const userInfo = await api.getUserInfo();
        // this.globalData.userInfo = userInfo.data;
        // common.setStorage('userInfo', userInfo.data);
      } catch (error) {
        console.error('刷新用户信息失败:', error);
      }
    }
  },

  // 检查网络状态
  async checkNetworkStatus() {
    const isConnected = await common.checkNetworkStatus();
    this.globalData.isNetworkConnected = isConnected;

    if (!isConnected) {
      wx.showToast({
        title: '网络连接异常',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 检查更新
  checkForUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager();

      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          console.log('发现新版本');
        }
      });

      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      });

      updateManager.onUpdateFailed(() => {
        console.error('新版本下载失败');
      });
    }
  },

  // 记录启动日志
  recordLaunchLog() {
    const logs = common.getStorage('logs', []);
    logs.unshift({
      timestamp: Date.now(),
      scene: this.globalData.launchScene,
      path: this.globalData.launchPath
    });

    // 只保留最近50条日志
    if (logs.length > 50) {
      logs.splice(50);
    }

    common.setStorage('logs', logs);
  },

  // 保存应用状态
  saveAppState() {
    const appState = {
      lastActiveTime: Date.now(),
      currentPage: getCurrentPages().pop()?.route || '',
      userInfo: this.globalData.userInfo
    };

    common.setStorage('appState', appState);
  },

  // 错误上报
  reportError(error) {
    // 这里可以上报错误到服务器
    console.error('错误上报:', error);

    // 保存错误日志到本地
    const errorLogs = common.getStorage('errorLogs', []);
    errorLogs.unshift({
      timestamp: Date.now(),
      error: error.toString(),
      stack: error.stack || '',
      page: getCurrentPages().pop()?.route || ''
    });

    // 只保留最近20条错误日志
    if (errorLogs.length > 20) {
      errorLogs.splice(20);
    }

    common.setStorage('errorLogs', errorLogs);
  },

  // 全局数据
  globalData: {
    userInfo: null,
    systemInfo: null,
    isLoggedIn: false,
    isNetworkConnected: true,
    cloudEnabled: false, // 云开发是否可用
    cloudEnvId: null, // 当前使用的云开发环境ID
    cloudDiagnosticReport: null, // 云开发诊断报告
    launchScene: null,
    launchPath: null,
    version: '1.2.0',
    store: store, // 全局状态管理
    errorHandler: errorHandler // 全局错误处理
  }
});
