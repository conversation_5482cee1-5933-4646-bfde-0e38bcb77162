# 角色分配功能实现文档

## 概述

本文档描述了AI剧本杀游戏中角色分配功能的完整实现，包括从AI生成的剧本中智能分配角色给玩家的后端逻辑和前端展示。

## 功能特性

### 🎭 智能角色分配
- **平衡分配算法**: 确保角色分配的公平性和游戏平衡
- **AI剧本集成**: 从AI生成的剧本中提取角色信息
- **降级机制**: 当AI服务不可用时，自动使用默认角色
- **实时验证**: 确保角色数量与玩家数量匹配

### 🎨 角色信息增强
- **阵营判断**: 根据角色特征自动判断阵营（正义/中立/凶手）
- **难度评估**: 基于角色复杂度自动评估难度等级
- **目标提取**: 从角色背景中智能提取游戏目标
- **关系构建**: 建立角色间的基础关系网络

### 📱 用户界面
- **角色卡片展示**: 美观的角色信息卡片
- **分配进度显示**: 实时显示角色分配进度
- **详情查看**: 支持查看其他玩家的基本角色信息
- **响应式设计**: 适配不同屏幕尺寸

## 技术架构

### 后端服务 (`utils/ai-service-simple.js`)

#### 核心方法

```javascript
// 生成角色分配
async generateRoleAssignment(scriptData, playerIds)

// 生成平衡的角色分配
generateBalancedAssignment(characters, playerIds)

// 生成随机角色分配（降级方案）
generateRandomAssignment(scriptData, playerIds)
```

#### 分配算法
1. **输入验证**: 检查角色数量与玩家数量是否匹配
2. **随机打乱**: 使用Fisher-Yates算法随机打乱角色和玩家顺序
3. **一对一分配**: 确保每个玩家获得唯一角色
4. **结果验证**: 验证分配结果的完整性

### 前端页面 (`pages/role-assignment/`)

#### 主要功能
- **角色分配流程**: 自动执行分配并显示进度
- **我的角色展示**: 详细显示当前玩家的角色信息
- **其他玩家**: 显示其他玩家的基本角色信息
- **角色详情弹窗**: 支持查看角色详细信息

#### 数据流
1. 从本地存储获取AI生成的剧本数据
2. 从房间管理器获取玩家列表
3. 调用AI服务进行角色分配
4. 增强角色信息（阵营、难度、目标等）
5. 保存分配结果到本地存储

## 实现细节

### 角色信息增强

#### 阵营判断逻辑
```javascript
determineFaction(character) {
  const title = character.title?.toLowerCase() || '';
  const background = character.background?.toLowerCase() || '';
  
  if (title.includes('凶手') || background.includes('杀害')) {
    return '凶手阵营';
  } else if (title.includes('警察') || background.includes('调查')) {
    return '正义阵营';
  } else {
    return '中立阵营';
  }
}
```

#### 难度评估逻辑
```javascript
determineDifficulty(character) {
  const secretsCount = character.secrets?.length || 0;
  const backgroundLength = character.background?.length || 0;
  
  if (secretsCount >= 3 || backgroundLength > 200) {
    return '困难难度';
  } else if (secretsCount >= 2 || backgroundLength > 100) {
    return '中等难度';
  } else {
    return '简单难度';
  }
}
```

### 数据结构

#### 角色分配结果
```javascript
{
  success: true,
  assignments: [
    {
      playerId: "player_1",
      characterId: "char_001",
      assignedAt: "2025-08-05T14:30:00.000Z"
    }
  ],
  timestamp: "2025-08-05T14:30:00.000Z"
}
```

#### 增强后的角色信息
```javascript
{
  id: "char_001",
  name: "艾米丽·哈特",
  title: "庄园女主人",
  faction: "中立阵营",
  difficulty: "中等难度",
  background: "角色背景描述...",
  objectives: ["保护庄园的秘密", "找出真相"],
  secrets: ["秘密信息1", "秘密信息2"],
  relationships: [
    {
      targetId: "char_002",
      relation: "旧情人",
      description: "关系描述"
    }
  ]
}
```

## 测试功能

### 测试页面 (`pages/test-role-assignment/`)
- **自动化测试**: 完整的角色分配流程测试
- **结果验证**: 验证分配结果的正确性
- **可视化展示**: 直观显示测试进度和结果
- **实际体验**: 可跳转到真实的角色分配页面

### 测试用例 (`test/role-assignment-test.js`)
- **功能测试**: 测试角色分配的核心功能
- **边界测试**: 测试异常情况的处理
- **性能测试**: 验证分配算法的效率
- **集成测试**: 测试与其他模块的集成

## 使用方法

### 1. 进入角色分配页面
```javascript
wx.navigateTo({
  url: `/pages/role-assignment/role-assignment?roomId=${roomId}`
});
```

### 2. 自动分配流程
页面加载后会自动执行以下步骤：
1. 模拟分配过程（3秒动画）
2. 从本地存储获取AI剧本数据
3. 获取房间玩家信息
4. 调用AI服务分配角色
5. 增强角色信息
6. 显示分配结果

### 3. 查看角色详情
- 点击其他玩家角色卡片查看基本信息
- 查看自己的完整角色信息（背景、目标、秘密）
- 查看角色关系信息

## 错误处理

### 降级机制
1. **AI服务不可用**: 自动使用模拟服务生成默认角色
2. **剧本数据缺失**: 使用预设的默认角色模板
3. **角色数量不匹配**: 智能调整或提示用户
4. **网络异常**: 本地缓存和重试机制

### 用户提示
- 分配进度实时显示
- 错误信息友好提示
- 操作指引清晰明确

## 性能优化

### 数据缓存
- 角色分配结果本地存储
- 剧本数据缓存复用
- 房间信息内存缓存

### 异步处理
- 非阻塞的角色分配流程
- 渐进式UI更新
- 后台数据预加载

## 扩展性

### 支持的扩展
- 自定义分配算法
- 角色平衡性调整
- 多种游戏模式适配
- 个性化推荐系统

### 接口设计
- 模块化的服务接口
- 可配置的分配策略
- 插件式的增强功能
- 标准化的数据格式

## 总结

角色分配功能是剧本杀游戏的核心组件，本实现提供了：

✅ **完整的分配流程**: 从AI剧本到角色分配的端到端解决方案
✅ **智能化处理**: 自动判断阵营、难度和目标
✅ **用户友好界面**: 美观直观的角色展示
✅ **健壮的错误处理**: 多层降级机制确保功能可用
✅ **全面的测试覆盖**: 自动化测试和手动测试工具
✅ **良好的扩展性**: 支持未来功能扩展和定制

该实现为玩家提供了流畅的角色分配体验，确保游戏的公平性和趣味性。
