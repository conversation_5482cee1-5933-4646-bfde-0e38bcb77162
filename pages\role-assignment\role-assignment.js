// 角色分配页面逻辑
const api = require('../../utils/api');
const common = require('../../utils/common');
const errorHandler = require('../../utils/error-handler');
const aiService = require('../../utils/ai-service-simple');
const roomManager = require('../../utils/room-manager');

Page({
  data: {
    roomId: '',
    assignmentComplete: false,
    starting: false,
    showRoleDetail: false,
    selectedRole: null,

    // 我的角色信息
    myRole: null,

    // 其他玩家角色（只显示基本信息）
    otherRoles: []
  },

  onLoad(options) {
    if (options.roomId) {
      this.setData({ roomId: options.roomId });
    }

    // 检查是否为单人测试模式（从AI故事生成器跳转过来）
    const isSingleTestMode = options.roomId && options.roomId.startsWith('single_test_');

    // 开始角色分配流程
    this.startRoleAssignment();

    // 如果是单人测试模式，角色分配完成后自动开始游戏
    if (isSingleTestMode) {
      this.isSingleTestMode = true;
      console.log('🎮 单人测试模式：将自动完成角色分配并开始游戏');
    }
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },

  // 开始角色分配
  async startRoleAssignment() {
    try {
      // 模拟AI分配角色的过程
      await this.simulateAssignment();

      // 获取角色分配结果
      await this.loadRoleAssignment();

    } catch (error) {
      console.error('角色分配失败:', error);
      errorHandler.showError(error, '角色分配', this);
    }
  },

  // 模拟分配过程
  async simulateAssignment() {
    // 模拟3秒的分配时间
    await new Promise(resolve => setTimeout(resolve, 3000));
  },

  // 加载角色分配结果
  async loadRoleAssignment() {
    try {
      // 尝试从本地存储获取AI生成的剧本数据
      const scriptData = wx.getStorageSync(`script_${this.data.roomId}`);

      if (scriptData && scriptData.characters && scriptData.characters.length > 0) {
        console.log('使用AI生成的剧本数据:', scriptData);
        await this.assignRolesFromScript(scriptData);
      } else {
        console.log('未找到AI剧本数据，使用默认角色');
        await this.loadDefaultRoles();
      }

    } catch (error) {
      console.error('角色分配失败:', error);
      // 降级到默认角色
      await this.loadDefaultRoles();
    }
  },

  // 从AI剧本分配角色
  async assignRolesFromScript(scriptData) {
    try {
      console.log('🎭 开始从AI剧本分配角色...');

      // 确保房间存在，如果不存在则创建测试房间
      let room;
      let currentUser;

      try {
        room = roomManager.getRoomInfo(this.data.roomId);
        currentUser = roomManager.getCurrentUser();
      } catch (error) {
        console.log('🏠 房间不存在，创建测试环境...');
        const testEnv = this.createTestEnvironment();
        room = testEnv.room;
        currentUser = testEnv.currentUser;
      }

      if (!room || !room.players) {
        throw new Error('无法获取房间玩家信息');
      }

      // 检查房间是否可以开始角色分配
      if (!this.canStartRoleAssignment(room)) {
        console.log('⚠️ 房间未准备好，使用默认角色...');
        return await this.loadDefaultRoles();
      }

      // 获取所有玩家ID
      const playerIds = Array.from(room.players.keys());
      console.log('🎮 房间玩家:', playerIds);
      console.log('👤 当前用户:', currentUser.id);

      // 使用AI服务进行角色分配
      const assignmentResult = await aiService.generateRoleAssignment(scriptData, playerIds);
      console.log('✅ 角色分配结果:', assignmentResult);

      // 找到当前玩家的角色分配
      const currentPlayerAssignment = assignmentResult.assignments.find(a => a.playerId === currentUser.id);

      if (currentPlayerAssignment) {
        const myCharacter = scriptData.characters.find(c => c.id === currentPlayerAssignment.characterId);

        if (myCharacter) {
          // 增强角色信息
          const enhancedRole = this.enhanceRoleInfo(myCharacter);

          this.setData({
            myRole: enhancedRole,
            assignmentComplete: true
          });

          // 设置其他玩家角色（只显示基本信息）
          const otherRoles = this.buildOtherRolesInfo(scriptData, assignmentResult, room, currentUser.id);
          this.setData({ otherRoles });

          // 如果是单人测试模式，自动开始游戏
          if (this.isSingleTestMode) {
            console.log('🎮 单人测试模式：自动开始游戏');
            setTimeout(() => {
              this.startGame();
            }, 2000); // 2秒后自动开始
          }

          // 保存角色分配到本地存储
          wx.setStorageSync(`role_assignment_${this.data.roomId}`, {
            myRole: enhancedRole,
            otherRoles: otherRoles,
            assignmentResult: assignmentResult,
            timestamp: new Date().toISOString()
          });

          console.log('✅ AI角色分配完成:', enhancedRole.name);
          return;
        }
      }

      // 如果分配失败，降级到默认角色
      await this.loadDefaultRoles();

    } catch (error) {
      console.error('❌ AI角色分配失败:', error);
      await this.loadDefaultRoles();
    }
  },

  // 增强角色信息
  enhanceRoleInfo(character) {
    return {
      id: character.id,
      name: character.name,
      title: character.title,
      difficulty: this.determineDifficulty(character),
      avatar: character.avatar || '',
      background: character.background || '暂无背景信息',
      objectives: this.extractObjectives(character),
      secrets: character.secrets || ['暂无秘密信息'],
      relationships: this.buildRelationships(character),
      preview: character.preview || {}
    };
  },

  // 确定角色难度
  determineDifficulty(character) {
    if (character.difficulty) return character.difficulty;

    const secretsCount = character.secrets?.length || 0;
    const backgroundLength = character.background?.length || 0;

    if (secretsCount >= 3 || backgroundLength > 200) {
      return '困难难度';
    } else if (secretsCount >= 2 || backgroundLength > 100) {
      return '中等难度';
    } else {
      return '简单难度';
    }
  },

  // 提取角色目标
  extractObjectives(character) {
    if (character.objectives) return character.objectives;

    // 从背景中提取可能的目标
    const objectives = [];
    const background = character.background || '';

    if (background.includes('找出') || background.includes('发现')) {
      objectives.push('找出真相');
    }
    if (background.includes('保护') || background.includes('守护')) {
      objectives.push('保护重要信息');
    }
    if (background.includes('隐藏') || background.includes('秘密')) {
      objectives.push('隐藏自己的秘密');
    }

    return objectives.length > 0 ? objectives : ['完成角色使命'];
  },

  // 构建角色关系
  buildRelationships(character) {
    if (character.relationships) return character.relationships;

    // 基于角色信息构建基础关系
    return [
      {
        targetId: 'unknown',
        relation: '待发现',
        description: '在游戏过程中逐渐揭示与其他角色的关系'
      }
    ];
  },

  // 构建其他玩家角色信息
  buildOtherRolesInfo(scriptData, assignmentResult, room, currentUserId) {
    const otherRoles = [];

    assignmentResult.assignments.forEach(assignment => {
      if (assignment.playerId !== currentUserId) {
        const character = scriptData.characters.find(c => c.id === assignment.characterId);
        const player = room.players.get(assignment.playerId);

        if (character && player) {
          otherRoles.push({
            id: character.id,
            name: character.name,
            title: character.title,
            playerName: player.nickname,
            playerId: assignment.playerId,
            avatar: character.avatar || player.avatar || ''
          });
        }
      }
    });

    return otherRoles;
  },

  // 加载默认角色数据
  async loadDefaultRoles() {
    try {
      // 使用原有的模拟角色数据
      const mockMyRole = {
        id: 'role_001',
        name: '艾米丽·哈特',
        title: '庄园女主人',
        difficulty: '中等难度',
        avatar: '',
        background: '你是这座神秘庄园的女主人，表面上优雅端庄，但内心隐藏着不为人知的秘密。你邀请这些客人来到庄园，是为了揭露一个埋藏多年的真相。',
        objectives: [
          '保护庄园的秘密不被发现',
          '找出真正的幕后黑手',
          '确保自己能够活到最后'
        ],
        secrets: [
          '你知道庄园地下室隐藏着重要证据',
          '你与其中一位客人有着不为人知的关系'
        ],
        relationships: [
          { targetId: 'role_002', relation: '旧情人', description: '你们曾经相爱但因为某些原因分开' }
        ]
      };

      const mockOtherRoles = [
        {
          id: 'role_002',
          name: '詹姆斯·布莱克',
          title: '私家侦探',
          playerName: '玩家2',
          avatar: '',
          publicInfo: '一位经验丰富的私家侦探，善于观察细节和推理分析。'
        },
        {
          id: 'role_003',
          name: '维多利亚·格林',
          title: '艺术收藏家',
          playerName: '玩家3',
          avatar: '',
          publicInfo: '热爱艺术的收藏家，对古董和艺术品有着敏锐的鉴赏力。'
        },
        {
          id: 'role_004',
          name: '查尔斯·怀特',
          title: '律师',
          playerName: '玩家4',
          avatar: '',
          publicInfo: '精明的律师，擅长法律条文和合同细节。'
        }
      ];

      this.setData({
        assignmentComplete: true,
        myRole: mockMyRole,
        otherRoles: mockOtherRoles
      });

      // 添加触觉反馈
      wx.vibrateShort();

      this.showToast('角色分配完成！', 'success');

    } catch (error) {
      console.error('加载角色分配失败:', error);
      errorHandler.showError(error, '加载角色分配', this);
    }
  },

  // 查看角色详情
  viewRoleDetail(e) {
    const roleId = e.currentTarget.dataset.roleId;
    const role = this.data.otherRoles.find(r => r.id === roleId);

    if (role) {
      this.setData({
        selectedRole: role,
        showRoleDetail: true
      });
    }
  },

  // 关闭角色详情
  closeRoleDetail() {
    this.setData({ showRoleDetail: false });
  },

  // 查看角色关系
  viewCharacterRelations() {
    wx.navigateTo({
      url: `/pages/character-relations/character-relations?roomId=${this.data.roomId}`
    });
  },

  // 开始游戏
  async startGame() {
    try {
      this.setData({ starting: true });

      // 模拟准备游戏
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 这里应该调用API开始游戏
      // await api.startGame(this.data.roomId);

      this.showToast('游戏即将开始！', 'success');

      // 跳转到线索收集页面
      setTimeout(() => {
        wx.redirectTo({
          url: `/pages/clues-collection/clues-collection?roomId=${this.data.roomId}`
        });
      }, 1000);

    } catch (error) {
      console.error('开始游戏失败:', error);
      errorHandler.showError(error, '开始游戏', this);
    } finally {
      this.setData({ starting: false });
    }
  },

  // 继续到私密线索页面
  continueToPrivateClues() {
    if (this.data.loading) return;

    this.setData({ loading: true });

    console.log('跳转到私密线索页面', this.data.roomId);

    setTimeout(() => {
      wx.navigateTo({
        url: `/pages/private-clues/private-clues?roomId=${this.data.roomId}`,
        success: (res) => {
          console.log('跳转私密线索页面成功', res);
          this.setData({ loading: false });
        },
        fail: (err) => {
          console.error('跳转私密线索页面失败', err);
          this.setData({ loading: false });
          wx.showToast({
            title: '页面跳转失败',
            icon: 'error'
          });
        }
      });
    }, 1000);
  },

  // 查看角色详情
  viewRoleDetail(e) {
    const roleId = e.currentTarget.dataset.roleId;
    const role = this.data.otherRoles.find(r => r.id === roleId);

    if (role) {
      this.setData({
        selectedRole: role,
        showRoleDetail: true
      });
    }
  },

  // 关闭角色详情
  closeRoleDetail() {
    this.setData({
      showRoleDetail: false,
      selectedRole: null
    });
  },

  // 查看角色关系
  viewRelationships() {
    if (!this.data.myRole || !this.data.myRole.relationships) {
      this.showToast('暂无角色关系信息');
      return;
    }

    const relationships = this.data.myRole.relationships
      .map(rel => `${rel.relation}: ${rel.description}`)
      .join('\n');

    wx.showModal({
      title: '角色关系',
      content: relationships,
      showCancel: false
    });
  },

  // 创建测试环境
  createTestEnvironment() {
    console.log('🧪 创建测试环境...');

    try {
      // 创建测试房间
      const roomResult = roomManager.createRoom({
        gameMode: '经典推理模式',
        maxPlayers: 6,
        timeLimit: 600,
        rounds: 3
      });

      // 更新当前房间ID
      this.setData({ roomId: roomResult.roomId });

      // 添加测试玩家
      const testPlayers = [
        { id: 'test_player_2', nickname: '测试玩家2', avatar: '', isReady: true },
        { id: 'test_player_3', nickname: '测试玩家3', avatar: '', isReady: true },
        { id: 'test_player_4', nickname: '测试玩家4', avatar: '', isReady: true },
        { id: 'test_player_5', nickname: '测试玩家5', avatar: '', isReady: true },
        { id: 'test_player_6', nickname: '测试玩家6', avatar: '', isReady: true }
      ];

      for (const player of testPlayers) {
        roomManager.joinRoom(roomResult.roomId, player);
      }

      const room = roomManager.getRoomInfo(roomResult.roomId);
      const currentUser = roomManager.getCurrentUser();

      console.log('✅ 测试环境创建成功:', roomResult.roomId);
      console.log('👥 玩家数量:', room.players.size);

      return { room, currentUser };

    } catch (error) {
      console.error('❌ 测试环境创建失败:', error);
      throw new Error('无法创建测试环境');
    }
  },

  // 检查房间是否可以开始角色分配
  canStartRoleAssignment(room) {
    if (!room || !room.players) {
      return false;
    }

    // 检查是否有足够的玩家
    if (room.players.size < 2) {
      console.log('⚠️ 玩家数量不足');
      return false;
    }

    // 检查玩家是否都准备好了（在实际游戏中）
    // 这里为了测试，我们假设都准备好了
    return true;
  },

  // 显示提示
  showToast(message, theme = 'warning') {
    wx.showToast({
      title: message,
      icon: theme === 'success' ? 'success' : theme === 'error' ? 'error' : 'none',
      duration: 2000
    });
  }
});