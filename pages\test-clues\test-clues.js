// 线索收集测试页面
Page({
  data: {
    testButtons: [
      {
        id: 'direct_test',
        name: '🔍 直接测试线索收集',
        desc: '跳转到线索收集页面进行测试',
        url: '/pages/clues-collection/clues-collection?roomId=test_room_clues'
      },
      {
        id: 'with_role',
        name: '🎭 带角色测试',
        desc: '先设置角色信息再测试',
        action: 'setupRoleAndTest'
      },
      {
        id: 'ai_test',
        name: '🤖 AI生成测试',
        desc: '测试AI线索生成功能',
        action: 'testAIGeneration'
      },
      {
        id: 'complete_flow',
        name: '🎯 完整流程测试',
        desc: '从角色分配到线索收集的完整流程',
        action: 'testCompleteFlow'
      },
      {
        id: 'script_generation',
        name: '📝 剧本生成测试',
        desc: '测试AI剧本生成页面',
        action: 'testScriptGeneration'
      },
      {
        id: 'quick_mock_test',
        name: '⚡ 快速模拟测试',
        desc: '使用模拟服务快速测试完整流程',
        action: 'quickMockTest'
      },
      {
        id: 'force_ai_test',
        name: '🤖 强制AI测试',
        desc: '强制使用真实AI服务生成剧本',
        action: 'forceAITest'
      },
      {
        id: 'clues_only_test',
        name: '🔍 线索收集测试',
        desc: '直接测试线索收集页面功能',
        action: 'testCluesOnly'
      }
    ]
  },

  onLoad() {
    console.log('🧪 线索收集测试页面加载');
  },

  // 处理按钮点击
  handleButtonTap(e) {
    const button = e.currentTarget.dataset.button;
    
    if (button.url) {
      // 直接跳转
      wx.navigateTo({
        url: button.url
      });
    } else if (button.action) {
      // 执行特定动作
      this[button.action]();
    }
  },

  // 设置角色信息并测试
  setupRoleAndTest() {
    const testRoomId = 'test_room_clues';
    
    // 设置测试角色信息
    const roleAssignment = {
      myRole: {
        id: 'char_001',
        name: '艾米丽·哈特',
        title: '庄园女主人',
        faction: '中立阵营',
        objectives: ['找出真相', '保护家族秘密'],
        background: '优雅的庄园女主人，隐藏着不为人知的秘密'
      },
      allRoles: [
        {
          id: 'char_001',
          name: '艾米丽·哈特',
          title: '庄园女主人'
        },
        {
          id: 'char_002',
          name: '詹姆斯·威尔逊',
          title: '管家'
        }
      ]
    };

    // 设置测试剧本信息
    const scriptData = {
      storyInfo: {
        title: '神秘庄园',
        theme: '神秘庄园',
        background: '在一个风雨交加的夜晚，庄园里发生了一起神秘事件...',
        setting: '19世纪末的英国乡村庄园'
      },
      characters: roleAssignment.allRoles
    };

    try {
      // 保存到本地存储
      wx.setStorageSync(`role_assignment_${testRoomId}`, roleAssignment);
      wx.setStorageSync(`script_${testRoomId}`, scriptData);
      
      wx.showToast({
        title: '角色设置完成',
        icon: 'success',
        duration: 1500
      });

      // 延迟跳转
      setTimeout(() => {
        wx.navigateTo({
          url: `/pages/clues-collection/clues-collection?roomId=${testRoomId}`
        });
      }, 1500);

    } catch (error) {
      console.error('设置角色信息失败:', error);
      wx.showToast({
        title: '设置失败',
        icon: 'error'
      });
    }
  },

  // 测试AI生成功能
  async testAIGeneration() {
    wx.showLoading({
      title: '测试线索生成...'
    });

    try {
      const aiService = require('../../utils/ai-service-simple');

      console.log('🧪 开始测试AI线索生成...');

      // 测试AI线索生成
      const result = await aiService.generateClues({
        scriptData: {
          storyInfo: {
            title: '神秘庄园谋杀案',
            background: '在一个风雨交加的夜晚，庄园主人神秘死亡，所有人都有嫌疑...',
            setting: '19世纪末的英国乡村庄园'
          }
        },
        myRole: {
          name: '艾米丽·哈特',
          title: '庄园女主人',
          background: '优雅的庄园女主人，与死者有着复杂的关系'
        },
        gamePhase: 'clue_collection',
        clueCount: 5
      });

      wx.hideLoading();

      console.log('✅ 线索生成测试结果:', result);

      if (result.success) {
        // 生成成功
        const sourceText = result.source === 'ai' ? '🤖 AI生成' :
                          result.source === 'mock' ? '🎭 模拟服务' : '📝 智能模板';

        const cluesList = result.clues.map((clue, index) =>
          `${index + 1}. ${clue.title}\n   ${clue.content.substring(0, 50)}...`
        ).join('\n\n');

        wx.showModal({
          title: `${sourceText}成功`,
          content: `生成了${result.clues.length}条线索:\n\n${cluesList}`,
          showCancel: true,
          cancelText: '关闭',
          confirmText: '查看详情',
          success: (res) => {
            if (res.confirm) {
              this.showDetailedClues(result.clues);
            }
          }
        });
      } else if (result.fallback) {
        // 使用了备选方案
        wx.showModal({
          title: '⚠️ 使用备选方案',
          content: `主要服务不可用，使用了备选方案生成${result.clues.length}条线索\n\n原因: ${result.error}`,
          showCancel: true,
          cancelText: '关闭',
          confirmText: '查看线索',
          success: (res) => {
            if (res.confirm) {
              this.showDetailedClues(result.clues);
            }
          }
        });
      } else {
        wx.showModal({
          title: '❌ 生成失败',
          content: result.error || '未知错误',
          showCancel: false
        });
      }

    } catch (error) {
      wx.hideLoading();
      console.error('❌ 线索生成测试失败:', error);
      wx.showModal({
        title: '测试失败',
        content: `错误详情: ${error.message || '未知错误'}\n\n请检查AI服务配置`,
        showCancel: false
      });
    }
  },

  // 显示详细线索信息
  showDetailedClues(clues) {
    const details = clues.map((clue, index) =>
      `【${clue.title}】\n类型: ${clue.type}\n重要性: ${clue.importance}\n内容: ${clue.content}`
    ).join('\n\n---\n\n');

    wx.showModal({
      title: '线索详情',
      content: details,
      showCancel: false
    });
  },

  // 完整流程测试
  async testCompleteFlow() {
    wx.showLoading({
      title: '准备完整流程测试...'
    });

    try {
      const testRoomId = 'test_complete_flow_' + Date.now();
      const aiService = require('../../utils/ai-service-simple');

      // 步骤1: 生成AI剧本
      wx.showLoading({ title: '🤖 AI生成剧本...' });

      const scriptResult = await aiService.generateScript({
        storyType: 'mystery',
        playerCount: 6,
        difficulty: 'medium',
        theme: '现代都市'
      });

      if (!scriptResult.success) {
        throw new Error('剧本生成失败: ' + scriptResult.error);
      }

      // 保存剧本数据
      wx.setStorageSync(`script_${testRoomId}`, scriptResult.scriptData);

      // 步骤2: 创建测试角色分配
      wx.showLoading({ title: '🎭 分配角色...' });

      const testRole = {
        id: 'char_001',
        name: scriptResult.scriptData.characters[0]?.name || '主角',
        title: scriptResult.scriptData.characters[0]?.title || '调查员',
        faction: '正义阵营',
        objectives: ['找出真相', '保护无辜'],
        background: scriptResult.scriptData.characters[0]?.background || '神秘的调查员',
        scriptTitle: scriptResult.scriptData.storyInfo.title
      };

      // 保存角色分配
      wx.setStorageSync(`role_assignment_${testRoomId}`, {
        myRole: testRole,
        otherRoles: [],
        timestamp: new Date().toISOString()
      });

      wx.hideLoading();

      // 显示成功信息并跳转
      wx.showModal({
        title: '🎉 流程准备完成',
        content: `剧本: ${scriptResult.scriptData.storyInfo.title}\n角色: ${testRole.name} (${testRole.title})\n\n即将跳转到线索收集页面体验完整流程`,
        showCancel: true,
        cancelText: '取消',
        confirmText: '开始体验',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: `/pages/clues-collection/clues-collection?roomId=${testRoomId}`
            });
          }
        }
      });

    } catch (error) {
      wx.hideLoading();
      console.error('完整流程测试失败:', error);
      wx.showModal({
        title: '流程测试失败',
        content: `错误: ${error.message}\n\n将使用默认数据进行测试`,
        showCancel: true,
        cancelText: '取消',
        confirmText: '使用默认',
        success: (res) => {
          if (res.confirm) {
            this.setupRoleAndTest();
          }
        }
      });
    }
  },

  // 测试剧本生成页面
  testScriptGeneration() {
    const testRoomId = 'script_test_' + Date.now();

    wx.navigateTo({
      url: `/pages/ai-script-generation/ai-script-generation?roomId=${testRoomId}`,
      success: () => {
        console.log('✅ 跳转到剧本生成测试页面成功');
      },
      fail: (error) => {
        console.error('❌ 跳转失败:', error);
        wx.showToast({
          title: '跳转失败',
          icon: 'error'
        });
      }
    });
  },

  // 快速模拟测试
  quickMockTest() {
    const testRoomId = 'quick_mock_' + Date.now();

    wx.showModal({
      title: '⚡ 快速模拟测试',
      content: '将使用模拟服务快速生成剧本，避免API限制，体验完整流程',
      showCancel: true,
      cancelText: '取消',
      confirmText: '开始',
      success: (res) => {
        if (res.confirm) {
          // 直接跳转到AI剧本生成页面
          wx.navigateTo({
            url: `/pages/ai-script-generation/ai-script-generation?roomId=${testRoomId}`,
            success: () => {
              console.log('✅ 跳转到快速模拟测试成功');
            },
            fail: (error) => {
              console.error('❌ 跳转失败:', error);
              wx.showToast({
                title: '跳转失败',
                icon: 'error'
              });
            }
          });
        }
      }
    });
  },

  // 强制AI测试
  forceAITest() {
    wx.showModal({
      title: '🤖 强制AI测试',
      content: '将强制使用真实AI服务生成剧本\n\n⚠️ 注意：可能遇到API限制\n✅ 可以体验真正的AI生成效果',
      showCancel: true,
      cancelText: '取消',
      confirmText: '开始测试',
      success: (res) => {
        if (res.confirm) {
          this.startForceAITest();
        }
      }
    });
  },

  // 开始强制AI测试
  async startForceAITest() {
    wx.showLoading({
      title: '🤖 强制使用AI服务...'
    });

    try {
      const aiService = require('../../utils/ai-service-simple');

      // 强制使用AI服务
      aiService.forceUseAI();

      console.log('🤖 开始强制AI剧本生成测试...');

      const scriptResult = await aiService.generateScript({
        storyType: 'mystery',
        playerCount: 6,
        difficulty: 'medium',
        theme: '现代都市'
      });

      wx.hideLoading();

      console.log('🤖 AI剧本生成结果:', scriptResult);

      if (scriptResult.success) {
        const sourceText = scriptResult.source === 'ai' ? '🤖 真实AI生成' :
                          scriptResult.source === 'mock' ? '🎭 模拟服务' :
                          scriptResult.source === 'mock_fallback' ? '🔄 AI失败回退' : '❓ 未知来源';

        wx.showModal({
          title: `${sourceText}成功`,
          content: `剧本标题: ${scriptResult.scriptData?.storyInfo?.title || '未知'}\n角色数量: ${scriptResult.scriptData?.characters?.length || 0}个\n来源: ${scriptResult.source}`,
          showCancel: true,
          cancelText: '关闭',
          confirmText: '继续流程',
          success: (res) => {
            if (res.confirm) {
              // 跳转到角色分配
              const testRoomId = 'force_ai_test_' + Date.now();
              wx.setStorageSync(`script_${testRoomId}`, scriptResult.scriptData);

              wx.navigateTo({
                url: `/pages/role-assignment/role-assignment?roomId=${testRoomId}`
              });
            }
          }
        });
      } else {
        wx.showModal({
          title: '❌ AI生成失败',
          content: `错误: ${scriptResult.error}\n来源: ${scriptResult.source}`,
          showCancel: false
        });
      }

    } catch (error) {
      wx.hideLoading();
      console.error('❌ 强制AI测试失败:', error);
      wx.showModal({
        title: '测试失败',
        content: `错误详情: ${error.message}\n\n可能是API配置问题或网络问题`,
        showCancel: false
      });
    }
  },

  // 测试线索收集页面
  testCluesOnly() {
    wx.showModal({
      title: '🔍 线索收集测试',
      content: '将创建测试数据并直接跳转到线索收集页面\n\n✅ 包含剧本数据\n✅ 包含角色信息\n✅ 测试AI线索生成',
      showCancel: true,
      cancelText: '取消',
      confirmText: '开始测试',
      success: (res) => {
        if (res.confirm) {
          this.setupCluesTest();
        }
      }
    });
  },

  // 设置线索收集测试
  setupCluesTest() {
    const testRoomId = 'clues_test_' + Date.now();

    // 创建测试剧本数据
    const testScript = {
      storyInfo: {
        title: '神秘庄园谋杀案',
        background: '在一个风雨交加的夜晚，庄园主人神秘死亡，所有人都有嫌疑...',
        setting: '19世纪末的英国乡村庄园',
        coreEvent: '庄园主人的神秘死亡'
      },
      characters: [
        {
          id: 'char_001',
          name: '艾米丽·哈特',
          title: '庄园女主人',
          background: '优雅的庄园女主人，与死者有着复杂的关系'
        }
      ]
    };

    // 创建测试角色分配
    const testRoleAssignment = {
      myRole: {
        id: 'char_001',
        name: '艾米丽·哈特',
        title: '庄园女主人',
        background: '优雅的庄园女主人，与死者有着复杂的关系',
        objectives: ['找出真相', '保护庄园的秘密'],
        scriptTitle: testScript.storyInfo.title
      },
      timestamp: new Date().toISOString()
    };

    // 保存测试数据
    wx.setStorageSync(`script_${testRoomId}`, testScript);
    wx.setStorageSync(`role_assignment_${testRoomId}`, testRoleAssignment);

    console.log('✅ 测试数据已创建:', {
      roomId: testRoomId,
      script: testScript.storyInfo.title,
      role: testRoleAssignment.myRole.name
    });

    // 跳转到线索收集页面
    wx.navigateTo({
      url: `/pages/clues-collection/clues-collection?roomId=${testRoomId}`,
      success: () => {
        console.log('✅ 跳转到线索收集页面成功');
        wx.showToast({
          title: '测试数据已准备',
          icon: 'success'
        });
      },
      fail: (error) => {
        console.error('❌ 跳转失败:', error);
        wx.showToast({
          title: '跳转失败',
          icon: 'error'
        });
      }
    });
  }
});
