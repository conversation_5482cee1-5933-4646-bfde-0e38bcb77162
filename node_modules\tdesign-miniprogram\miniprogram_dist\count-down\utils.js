export const TimeDataUnit={DD:"天",HH:"时",mm:"分",ss:"秒",SSS:"毫秒"};const SECOND=1e3,MINUTE=6e4,HOUR=36e5,DAY=24*HOUR;export const parseTimeData=function(t){return{DD:Math.floor(t/DAY),HH:Math.floor(t%DAY/HOUR),mm:Math.floor(t%HOUR/6e4),ss:Math.floor(t%6e4/1e3),SSS:Math.floor(t%1e3)}};export const isSameSecond=function(t,e){return Math.floor(t/1e3)===Math.floor(e/1e3)};export const parseFormat=function(t,e){const o={"D+":Math.floor(t/864e5),"H+":Math.floor(t%864e5/36e5),"m+":Math.floor(t%36e5/6e4),"s+":Math.floor(t%6e4/1e3),"S+":Math.floor(t%1e3)},r=[];let n=e;return Object.keys(o).forEach(t=>{new RegExp(`(${t})`).test(n)&&(n=n.replace(RegExp.$1,(e,n,s)=>{const a=`${o[t]}`;let l=a;e.length>1&&(l=(e.replace(new RegExp(e[0],"g"),"0")+a).substr(a.length));const h=s.substr(n+e.length),c=r[r.length-1];if(c){const t=c.unit.indexOf(e);-1!==t&&(c.unit=c.unit.substr(0,t))}return r.push({digit:l,unit:h,match:e}),l}))}),{timeText:n,timeList:r}};