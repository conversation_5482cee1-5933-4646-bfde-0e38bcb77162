const props={disabled:{type:<PERSON><PERSON><PERSON>,value:!1},enableBackToTop:{type:Boolean,value:!0},enablePassive:{type:Boolean,value:!1},externalClasses:{type:Array},loadingBarHeight:{type:null,value:50},loadingProps:{type:Object},loadingTexts:{type:Array,value:[]},lowerThreshold:{type:null,value:50},maxBarHeight:{type:null,value:80},refreshTimeout:{type:Number,value:3e3},scrollIntoView:{type:String,value:""},showScrollbar:{type:Boolean,value:!0},upperThreshold:{type:null,value:50},usingCustomNavbar:{type:Boolean,value:!1},value:{type:Boolean,value:null},defaultValue:{type:Boolean,value:!1}};export default props;