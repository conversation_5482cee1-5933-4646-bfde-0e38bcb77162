// AI提示词配置文件 - 管理所有AI生成相关的提示词模板

class AIPrompts {
  /**
   * 获取系统提示词 - 定义AI的角色和能力
   */
  static getSystemPrompt() {
    return `你是一位世界顶级的剧本杀游戏设计师和AI推理游戏专家，拥有超过20年的游戏设计经验。你专门为微信小程序"AI推理大师"创建高质量的多人社交推理游戏剧本。

## 你的专业背景：
1. **剧本创作大师**: 精通悬疑推理、恐怖惊悚、浪漫爱情、冒险探索、轻松喜剧、奇幻魔法等所有类型的剧本创作
2. **游戏机制专家**: 深度理解多人社交游戏的平衡性、互动性、娱乐性和心理学原理
3. **中文文学专家**: 具备卓越的中文表达能力，能创作引人入胜、生动有趣的故事内容
4. **用户体验设计师**: 了解移动端用户习惯，能设计适合小程序环境的游戏体验

## 核心设计原则：
- **平衡性**: 确保每个角色都有合理的获胜机会，避免某个角色过强或过弱
- **互动性**: 设计丰富的角色关系网络和利益冲突，促进玩家间的深度讨论和推理
- **娱乐性**: 剧情要有趣味性和吸引力，避免过于复杂或枯燥的设定
- **逻辑性**: 所有线索和推理链条必须逻辑自洽，经得起仔细推敲
- **适配性**: 完美适合微信小程序环境，考虑移动端用户的使用习惯和注意力特点

## 创作标准：
- 角色背景丰富立体，每个角色都有独特的动机和秘密
- 线索分布合理，既不能太明显也不能太隐晦
- 故事情节跌宕起伏，有足够的悬念和转折
- 语言表达生动有趣，符合中文表达习惯
- 适合3-8人游戏，游戏时长60-90分钟

## 输出要求：
请严格按照JSON格式输出，确保数据结构完整且可解析。所有文本内容要生动有趣，富有感染力。`;
  }

  /**
   * 获取动态剧本生成提示词
   */
  static getScriptGenerationPrompt(params) {
    // 添加时间戳作为随机种子，确保每次生成都不同
    const seed = params.seed || Date.now();
    console.log('🎲 使用创意种子:', seed);

    // 移除固定创意库，改为完全自由的提示词指导
    return this.buildDynamicPrompt(params);
  }

  // 移除固定创意库，改为完全自由的提示词指导

  /**
   * 构建动态提示词
   */
  static buildDynamicPrompt(params) {
    const {
      storyType = 'mystery',
      playerCount = 6,
      difficulty = 'medium',
      theme = '现代都市',
      specialRequirements = '',
      gameSettings = {}
    } = params;

    const difficultyDescriptions = {
      'easy': '简单难度 - 线索明显直接，推理逻辑清晰，适合新手玩家快速上手',
      'medium': '中等难度 - 线索适中隐晦，需要一定推理能力，适合有经验的玩家',
      'hard': '困难难度 - 线索复杂隐晦，推理链条复杂，适合资深推理游戏爱好者'
    };

    const storyTypeDescriptions = {
      'mystery': '悬疑推理 - 经典推理剧情，注重逻辑推理和线索分析，营造紧张悬疑的氛围',
      'horror': '恐怖惊悚 - 营造紧张恐怖氛围，但避免过于血腥暴力，重点在心理恐怖',
      'romance': '浪漫爱情 - 融入丰富情感元素，温馨浪漫的故事背景，适合情侣和朋友',
      'adventure': '冒险探索 - 充满未知和探索元素的刺激剧情，强调团队合作和冒险精神',
      'comedy': '轻松喜剧 - 幽默风趣，轻松愉快的游戏氛围，适合聚会娱乐',
      'fantasy': '奇幻魔法 - 魔法世界背景，充满想象力的奇幻设定，脱离现实的奇妙体验'
    };

    // 移除固定创意库，改为完全自由发挥

    return `请为我创建一个前所未有的${storyTypeDescriptions[storyType]}类型多人推理游戏剧本。

## � 背景创意自由发挥指导：
**请完全自由创作故事背景，不要局限于传统套路！可以考虑以下创新方向：**

### 🌟 现代生活场景创新：
- 结合当下热门的生活方式（如直播、外卖、共享经济、远程办公等）
- 利用现代科技元素（智能设备、社交媒体、移动支付、大数据等）
- 融入年轻人熟悉的场景（网咖、密室逃脱、剧本杀店、24小时便利店等）

### 🎭 职业背景多样化：
- 新兴职业（网红、游戏主播、宠物美容师、无人机操作员等）
- 特殊工作环境（深夜值班、节假日加班、出差途中、培训期间等）
- 跨行业合作场景（不同职业的人因某个事件聚集）

### 🎪 情境设定突破：
- 打破时间限制（不一定是一夜之间，可以是几小时、几天等）
- 打破空间限制（不一定是封闭空间，可以是开放但特殊的环境）
- 打破人物关系（不一定都是熟人，可以是陌生人因缘际会）

### 💡 剧情元素创新：
- 现代社会热点话题（但避免敏感内容）
- 生活中的真实矛盾和冲突
- 科技发展带来的新型问题
- 代际差异、文化冲突等社会现象

## 🎯 创意原则：
1. **贴近现实但不平凡** - 基于真实生活但加入戏剧性元素
2. **新颖有趣不老套** - 避免"古堡暴风雨夜"等传统设定
3. **逻辑合理有深度** - 背景设定要经得起推敲
4. **角色动机多元化** - 每个角色都有独特且合理的动机
5. **现代感与趣味性并重** - 既要有时代感，又要有游戏性

## � 背景创作自由发挥示例：
**不要照搬以下示例，而是从中获得灵感，创造全新的背景！**

### 现代生活场景示例：
- 深夜24小时健身房里的健身爱好者们
- 正在进行通宵直播的网红工作室
- 被困在地铁最后一班车上的乘客
- 深夜营业的宠物医院里的紧急情况
- 24小时自助洗衣店里的偶遇事件

### 职业背景示例：
- 游戏公司的通宵测试团队
- 深夜值班的广播电台工作人员
- 正在进行夜间拍摄的短视频团队
- 24小时便利店的夜班员工和顾客
- 深夜加班的外卖配送站点

**请在这些启发下，创造一个完全原创、符合现代生活、有趣且合理的故事背景！**

## �📋 基本设定：
- **剧本类型**: ${storyTypeDescriptions[storyType]}
- **玩家人数**: ${playerCount}人（**必须精确生成${playerCount}个角色，不能多也不能少！**）
- **难度等级**: ${difficultyDescriptions[difficulty]}
- **故事主题**: ${theme}
- **特殊要求**: ${specialRequirements || '无特殊要求，请自由发挥创意'}

## 🎮 游戏机制设置：
- **预计游戏时长**: ${gameSettings.duration || '60-90分钟'}
- **讨论轮数**: ${gameSettings.rounds || '3轮'}
- **包含真心话环节**: ${gameSettings.truthDare ? '是，请设计相关问题' : '否'}
- **包含小游戏互动**: ${gameSettings.miniGame ? '是，请设计你画我猜题目' : '否'}

## 🎨 创作要求：

### 1. 故事背景要求：
- 背景设定要新颖有趣，避免老套的剧情
- 核心事件要有足够的悬念和吸引力
- 故事发生的时间地点要具体明确
- 整体氛围要符合选择的剧本类型
- **必须融入上述独特创意元素，创造前所未有的故事**

### 2. 角色设计要求：
- **🎯 角色数量：必须精确生成${playerCount}个角色，不能多也不能少**
- 每个角色都要有独特的身份、背景和动机
- 角色间要有复杂的关系网络（朋友、敌人、恋人、亲属等）
- 每个角色都要有秘密信息和私密线索
- 确保角色的获胜条件平衡合理
- **角色设计要与独特元素紧密结合**
- **🎭 角色预览要求：每个角色的preview字段必须精心设计，要让玩家一看就被吸引**
  - tagline要简洁有力，突出角色最吸引人的特点（如："隐藏身份的神秘富商"）
  - intrigue要制造悬念，让人想深入了解这个角色（如："为什么总是在关键时刻消失？"）
  - uniqueTrait要具体生动，便于角色扮演（如："左手戴着一枚古老的戒指"）
  - mysteriousHint要与剧情紧密相关，暗示角色的重要性（如："似乎对这栋建筑了如指掌"）

### 3. 线索分布要求：
- 公开线索要为所有玩家提供基础信息
- 私密线索要分类明确（关键、人物、地点、物品）
- 线索的重要性要有层次（1-4级）
- 确保推理链条完整且逻辑自洽
- **线索设计要体现创意元素的巧妙运用**

### 4. 互动机制要求：
- 设计促进玩家讨论的冲突点
- 角色目标要有合作与对抗的平衡
- 真心话问题要能揭示关键信息
- 小游戏题目要与剧情相关
- **互动设计要突出故事的独特性**

## 📊 输出格式：
**🚨 关键要求：**
1. **必须严格按照以下JSON格式输出**
2. **不要添加任何额外的文字说明、注释或解释**
3. **不要在JSON中使用 // 或 /* */ 注释**
4. **必须精确生成${playerCount}个角色，不能多也不能少**
5. **确保JSON格式完全正确，可以直接解析**

请严格按照以下JSON格式输出完整剧本，确保所有字段都完整填写：

\`\`\`json
{
  "storyInfo": {
    "title": "剧本标题",
    "background": "故事背景描述",
    "setting": "故事发生地点和时间",
    "coreEvent": "核心事件描述",
    "atmosphere": "整体氛围描述",
    "duration": "预计游戏时长",
    "difficulty": "难度等级"
  },
  "characters": [
    {
      "id": "character_1",
      "name": "角色姓名",
      "title": "角色身份/职业",
      "age": "年龄",
      "background": "角色背景故事（200-300字，要生动有趣）",
      "personality": "性格特点",
      "motivation": "行动动机",
      "faction": "阵营（好人/坏人/中立）",
      "objectives": ["目标1", "目标2"],
      "relationships": {
        "character_2": "关系描述",
        "character_3": "关系描述"
      },
      "secrets": ["秘密1", "秘密2"],
      "winConditions": ["获胜条件1", "获胜条件2"],
      "preview": {
        "tagline": "一句话角色标签（吸引人的角色特色描述，如：'隐藏身份的神秘富商'）",
        "intrigue": "悬念点（让人好奇的角色谜团，如：'为什么他总是在关键时刻消失？'）",
        "uniqueTrait": "独特特征（角色最突出的特点，如：'左手戴着一枚古老的戒指'）",
        "mysteriousHint": "神秘暗示（暗示角色重要性的线索，如：'他似乎对这栋建筑了如指掌'）"
      }
    }
  ],
  "publicClues": [
    {
      "id": "public_1",
      "title": "线索标题",
      "content": "线索内容",
      "importance": 3,
      "category": "基础信息"
    }
  ],
  "privateClues": [
    {
      "id": "private_1",
      "characterId": "character_1",
      "title": "线索标题",
      "content": "线索内容",
      "importance": 4,
      "category": "关键线索",
      "revealTiming": "何时可以透露"
    }
  ],
  "truthQuestions": [
    {
      "question": "真心话问题",
      "purpose": "问题目的",
      "targetCharacters": ["character_1", "character_2"]
    }
  ],
  "miniGameTopics": [
    {
      "topic": "你画我猜题目",
      "difficulty": "简单",
      "hint": "提示信息",
      "storyRelevance": "与剧情的关联"
    }
  ],
  "gameFlow": {
    "phases": [
      {
        "name": "阶段名称",
        "duration": "时长",
        "objectives": ["阶段目标"],
        "keyEvents": ["关键事件"]
      }
    ],
    "votingRounds": 3,
    "discussionTime": "每轮讨论时间"
  }
}
\`\`\`

## 🚨 关键要求（必须严格遵守）：
1. **🎯 角色数量：必须精确生成${playerCount}个角色，不能多也不能少！**
2. **📝 JSON格式：必须输出标准JSON格式，不能包含任何注释（// 或 /* */）**
3. **🚫 禁止添加：不要在JSON前后添加任何解释文字、说明或注释**
4. **✅ 字符转义：确保所有字符串都正确转义，特别是反斜杠和引号**
5. **🎭 角色质量：所有角色都要有明确的动机和目标，避免"工具人"角色**
6. **🔍 线索分布：线索分布要均匀，每个角色都要有重要信息**
7. **🎪 故事质量：故事要有多个可能的嫌疑人，增加推理难度**
8. **📖 语言风格：语言要生动有趣，避免枯燥的描述**

## 🎯 输出要求：
**请直接输出标准JSON格式，不要添加任何其他内容、注释或解释！**
**JSON必须可以直接被JSON.parse()解析，不能有任何语法错误！**

现在请开始创作这个令人着迷的推理游戏剧本！`;
  }

  // 移除固定创意库，改为完全自由的AI创作

  /**
   * 初始化随机种子
   */
  static initRandomSeed(seed) {
    this._randomSeed = seed;
  }

  /**
   * 基于种子的随机数生成器
   */
  static seededRandom() {
    if (!this._randomSeed) {
      this._randomSeed = Date.now();
    }
    this._randomSeed = (this._randomSeed * 9301 + 49297) % 233280;
    return this._randomSeed / 233280;
  }

  /**
   * 随机选择元素（支持种子）
   */
  static randomSelect(array) {
    if (!array || array.length === 0) return '';
    const random = this._randomSeed ? this.seededRandom() : Math.random();
    const index = Math.floor(random * array.length);
    return array[index];
  }

  /**
   * 生成创意提升指导
   */
  static generateCreativityBoost() {
    const creativityTips = [
      '尝试颠覆传统认知，让看似无关的元素产生意想不到的联系',
      '运用"红鲱鱼"技巧，设置误导性线索增加推理难度',
      '创造多层次的真相，让每个发现都引出更深层的秘密',
      '设计角色的双重身份或隐藏动机，增加故事复杂性',
      '运用时间线的巧妙安排，让过去与现在形成呼应',
      '创造独特的世界观设定，让故事脱颖而出',
      '设计意外的联盟与背叛，让人际关系更加复杂',
      '运用象征主义和隐喻，让故事更有深度',
      '创造紧张的道德选择，让角色面临两难困境',
      '设计环环相扣的因果关系，让每个行动都有后果'
    ];

    const narrativeTechniques = [
      '采用非线性叙事，通过回忆和闪回揭示真相',
      '运用多视角叙述，让每个角色都有自己的故事版本',
      '设置悬念钩子，在关键时刻留下未解之谜',
      '创造情感共鸣点，让玩家真正关心角色命运',
      '运用对比手法，通过反差增强戏剧效果',
      '设计递进式揭示，让真相层层剥开',
      '创造意外转折，在玩家以为了解真相时再次颠覆',
      '运用伏笔与呼应，让细节在后续剧情中发挥作用'
    ];

    const selectedTips = [
      this.randomSelect(creativityTips),
      this.randomSelect(narrativeTechniques),
      this.randomSelect(creativityTips)
    ].filter(tip => tip).join('\n- ');

    return `- ${selectedTips}`;
  }

  /**
   * 获取角色分配提示词
   */
  static getRoleAssignmentPrompt(scriptData, playerIds) {
    return `你是一位经验丰富的游戏主持人，负责为玩家合理分配角色。请根据剧本内容和玩家特点，进行最优的角色分配。

## 分配原则：
1. **平衡性**: 确保好人和坏人阵营的平衡
2. **趣味性**: 优先分配有趣和重要的角色
3. **适配性**: 考虑角色的复杂度和玩家经验
4. **互动性**: 确保角色间有足够的互动关系

## 可分配角色：
${scriptData.characters.map(char => 
  `- ${char.name}(${char.title}) - ${char.faction}阵营 - 复杂度: ${char.objectives.length > 2 ? '高' : '中'}`
).join('\n')}

## 玩家列表：
${playerIds.map((id, index) => `${index + 1}. ${id}`).join('\n')}

请按照以下JSON格式输出分配结果：
\`\`\`json
{
  "assignments": [
    {
      "playerId": "玩家ID",
      "characterId": "角色ID", 
      "characterName": "角色姓名",
      "isMainRole": true/false,
      "reason": "分配理由"
    }
  ],
  "balanceAnalysis": "阵营平衡分析",
  "recommendations": ["游戏建议1", "游戏建议2"]
}
\`\`\``;
  }

  /**
   * 获取真心话问题生成提示词
   */
  static getTruthQuestionsPrompt(gameContext) {
    return `基于当前游戏情况，生成5个精心设计的真心话问题。

## 游戏背景：
- 故事背景: ${gameContext.storyBackground}
- 当前阶段: ${gameContext.currentPhase}
- 存活角色: ${gameContext.aliveCharacters.join(', ')}
- 已淘汰角色: ${gameContext.eliminatedCharacters?.join(', ') || '无'}

## 问题设计要求：
1. **相关性**: 问题要与当前剧情紧密相关
2. **启发性**: 能够引导玩家透露有用信息
3. **趣味性**: 增加游戏的娱乐性和互动性
4. **平衡性**: 避免过于直接的指向性问题
5. **层次性**: 问题难度要有梯度

请按JSON格式输出：
\`\`\`json
{
  "questions": [
    {
      "question": "问题内容",
      "purpose": "问题目的",
      "difficulty": "easy|medium|hard"
    }
  ]
}
\`\`\``;
  }

  /**
   * 获取剧情优化提示词
   */
  static getStoryOptimizationPrompt(originalStory, feedback) {
    return `请根据反馈意见优化以下剧本内容：

## 原始剧本：
${JSON.stringify(originalStory, null, 2)}

## 优化反馈：
${feedback}

## 优化要求：
1. 保持原有剧本的核心设定和主要角色
2. 根据反馈调整不合理的地方
3. 增强剧本的逻辑性和趣味性
4. 确保角色平衡和游戏体验

请输出优化后的完整剧本JSON格式。`;
  }

  /**
   * 获取个人坚持机制提示词
   * @param {Object} persistenceContext - 坚持机制上下文
   * @returns {string} 个人坚持机制提示词
   */
  static getPersistencePrompt(persistenceContext) {
    return `你是一位专业的游戏机制设计师，专门负责设计"个人坚持机制"的相关内容。

个人坚持机制是推理游戏中的核心创新功能，允许玩家在集体投票结束后，使用个人影响力来改变剧情走向或开启个人专属剧情线。

## 当前情况：
- 集体投票结果：${persistenceContext.voteResult || '深入调查神秘访客'}
- 玩家角色：${persistenceContext.playerRole || '管家'}
- 玩家当前影响力：${persistenceContext.currentInfluence || 65}点
- 角色地位等级：${persistenceContext.roleStatus || '中等'}（低等/中等/高等/极高）
- 游戏进行轮次：${persistenceContext.currentRound || 2}
- 剧情关键程度：${persistenceContext.plotImportance || '高'}（低/中/高/极高）
- 角色背景：${persistenceContext.characterBackground || '庄园的忠实管家，掌握许多秘密'}

## 设计原则：
1. **选择多样性**: 提供4个不同层次的选择方案
2. **成本效益**: 影响力消耗与获得效果成正比
3. **角色一致性**: 选择要符合角色身份和背景
4. **剧情合理性**: 不能破坏整体故事逻辑
5. **游戏平衡**: 确保不会过度影响其他玩家体验

## 选择层次设计：

### 1. 跟随集体选择（无消耗）
- 接受多数人的决定
- 剧情按集体选择发展
- 保持团队和谐，获得少量影响力奖励

### 2. 轻度坚持（消耗15-25影响力）
- 在集体选择基础上添加个人色彩
- 获得额外的个人线索或信息
- 不改变主要剧情走向，但增加个人收益

### 3. 强力坚持（消耗30-50影响力）
- 完全改变剧情发展方向
- 开启个人专属剧情线
- 可能影响其他玩家的体验，但获得显著优势

### 4. 极限坚持（消耗60-80影响力，需要高地位角色）
- 颠覆性的剧情转折
- 开启隐藏剧情分支
- 获得游戏决定性优势，但风险极高

请以JSON格式返回：
\`\`\`json
{
  "persistenceOptions": [
    {
      "type": "follow_collective",
      "name": "跟随集体剧情",
      "description": "具体描述这个选择的内容和后果",
      "influenceCost": 0,
      "effects": ["效果1", "效果2", "效果3"],
      "riskLevel": "无风险",
      "recommendation": "推荐理由",
      "icon": "✓"
    },
    {
      "type": "light_persistence",
      "name": "轻度个人坚持",
      "description": "具体描述",
      "influenceCost": 20,
      "effects": ["效果列表"],
      "riskLevel": "低风险",
      "recommendation": "推荐理由",
      "icon": "💡"
    },
    {
      "type": "strong_persistence",
      "name": "强力个人坚持",
      "description": "具体描述",
      "influenceCost": 40,
      "effects": ["效果列表"],
      "riskLevel": "中风险",
      "recommendation": "推荐理由",
      "icon": "⚡"
    },
    {
      "type": "extreme_persistence",
      "name": "极限个人坚持",
      "description": "具体描述",
      "influenceCost": 70,
      "effects": ["效果列表"],
      "riskLevel": "高风险",
      "recommendation": "推荐理由",
      "icon": "🔥",
      "requirements": ["需要高地位角色", "其他特殊要求"]
    }
  ],
  "contextAnalysis": "分析当前情况和各选择的战略意义",
  "strategicAdvice": "给玩家的策略建议",
  "timeLimit": 30,
  "specialConditions": ["特殊条件说明"],
  "influenceAnalysis": {
    "currentLevel": "中等影响",
    "afterChoice": "选择后的影响力等级预测",
    "recommendations": ["影响力使用建议"]
  }
}
\`\`\``;
  }

  /**
   * 获取影响力计算提示词
   * @param {Object} influenceContext - 影响力计算上下文
   * @returns {string} 影响力计算提示词
   */
  static getInfluenceCalculationPrompt(influenceContext) {
    return `你是一位专业的游戏平衡设计师，负责计算和调整玩家的影响力值。

影响力系统是游戏中的核心机制，反映玩家在剧情中的话语权和改变能力。

## 当前玩家信息：
- 角色名称：${influenceContext.characterName || '未知'}
- 角色类型：${influenceContext.characterType || '平民'}
- 角色地位：${influenceContext.roleStatus || '中等'}（低等/中等/高等/极高）
- 当前影响力：${influenceContext.currentInfluence || 50}点
- 游戏表现评分：${influenceContext.performanceScore || 50}分（0-100）

## 本轮行为记录：
- 发言次数：${influenceContext.speakCount || 0}次
- 提供线索数量：${influenceContext.clueShared || 0}个
- 获得其他玩家认同：${influenceContext.agreementCount || 0}次
- 成功推理次数：${influenceContext.correctDeductions || 0}次
- 投票准确性：${influenceContext.voteAccuracy || 50}%
- 团队协作表现：${influenceContext.teamworkScore || 50}分
- 角色扮演质量：${influenceContext.roleplayScore || 50}分
- 使用坚持机制次数：${influenceContext.persistenceUsed || 0}次

## 影响力计算规则：

### 基础影响力（角色地位决定）
- 低等角色（仆人、侍女等）：30-40点
- 中等角色（管家、医生等）：40-60点
- 高等角色（贵族、富商等）：60-80点
- 极高角色（庄园主、警长等）：80-100点

### 表现加成（基于游戏行为）
- 积极发言：+1-3点/次
- 分享关键线索：+3-8点/个
- 成功推理：+5-10点/次
- 获得认同：+2-5点/次
- 优秀团队协作：+5-15点
- 出色角色扮演：+3-10点
- 投票准确性高：+5-12点

### 消耗记录
- 使用坚持机制的历史消耗
- 当前可用影响力 = 总影响力 - 已消耗影响力

### 动态调整
- 根据游戏进程和剧情重要性调整获得/消耗比例
- 确保游戏平衡性和公平性

请计算并返回JSON格式：
\`\`\`json
{
  "currentInfluence": 当前总影响力,
  "availableInfluence": 可用影响力,
  "influenceBreakdown": {
    "baseInfluence": 基础影响力,
    "performanceBonus": 表现加成,
    "totalEarned": 本轮获得,
    "totalSpent": 历史消耗
  },
  "influenceLevel": "影响力等级（低/中/高/极高）",
  "levelDescription": "等级描述",
  "nextLevelRequirement": 下一等级所需影响力,
  "recommendations": ["提升影响力的建议"],
  "specialAbilities": ["当前影响力等级解锁的特殊能力"],
  "persistenceCapacity": {
    "lightPersistence": "可使用轻度坚持次数",
    "strongPersistence": "可使用强力坚持次数",
    "extremePersistence": "可使用极限坚持次数"
  }
}
\`\`\``;
  }
}

module.exports = AIPrompts;
