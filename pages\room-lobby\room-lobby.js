// 房间大厅页面逻辑
const roomManager = require('../../utils/room-manager');

Page({
  data: {
    roomId: '',
    isReady: false,
    canReady: true,
    canStart: false,
    isHost: false,
    gameStarted: false,
    currentPhase: 'waiting', // waiting, discussion, voting
    showMenu: false,
    onlineCount: 0,
    waitingTime: '00:00',
    roomInfo: {
      roomId: '',
      gameMode: '',
      timeLimit: 0,
      rounds: 0
    },

    // 游戏阶段状态
    phaseStatus: {
      roleAssignment: false,
      privateClues: false,
      discussion: false,
      voting: false
    },
    playerSlots: [],

    // 测试功能面板
    showTestPanel: false,
    testMode: {
      singlePlayer: false,  // 单人测试模式
      quickStart: false,    // 快速开始模式
      aiScript: true        // AI剧本生成
    }
  },

  onLoad(options) {
    const { roomId, gameStarted, testMode } = options;
    console.log('🏠 房间大厅页面加载:', roomId);
    console.log('🧪 测试模式参数:', testMode);

    if (!roomId) {
      wx.showModal({
        title: '错误',
        content: '房间ID不存在',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }

    try {
      // 从房间管理器获取房间信息
      const room = roomManager.getRoomInfo(roomId);
      const currentUser = roomManager.getCurrentUser();

      console.log('✅ 房间信息:', room);
      console.log('✅ 当前用户:', currentUser);

      // 检查当前用户是否为房主
      const isHost = currentUser.id === room.hostId;

      // 检查是否可以开始游戏
      const canStart = roomManager.canStartGame(roomId);

      this.setData({
        roomId,
        isHost,
        canStart,
        gameStarted: room.gameStarted,
        roomInfo: {
          roomId: room.id,
          gameMode: room.gameMode,
          timeLimit: room.timeLimit,
          rounds: room.rounds
        },
        playerSlots: room.playerSlots,
        onlineCount: room.players.size
      });

      // 如果是从快速单人测试进入，自动开启单人测试模式
      if (testMode === 'single') {
        console.log('🚀 自动开启单人测试模式');
        this.setData({
          'testMode.singlePlayer': true,
          showTestPanel: true  // 自动显示测试面板
        });

        wx.showToast({
          title: '单人测试模式已开启',
          icon: 'success'
        });
      }

      // 开始等待时间计时
      this.startWaitingTimer();

    } catch (error) {
      console.error('❌ 加载房间信息失败:', error);
      wx.showModal({
        title: '错误',
        content: error.message || '房间不存在',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
    }
  },

  onShow() {
    // 页面显示时刷新房间状态
    this.refreshRoomStatus();
  },

  onUnload() {
    // 清理定时器
    this.clearWaitingTimer();
  },

  // 加载房间信息
  async loadRoomInfo() {
    try {
      // 这里应该调用API获取房间信息
      // const res = await api.getRoomInfo(this.data.roomId);
      // this.setData({ roomInfo: res.data });
      
      console.log('加载房间信息:', this.data.roomId);
    } catch (error) {
      console.error('加载房间信息失败:', error);
      this.showToast('加载房间信息失败');
    }
  },

  // 刷新房间状态
  async refreshRoomStatus() {
    try {
      // 这里应该调用API刷新房间状态
      // const res = await api.getRoomStatus(this.data.roomId);
      // this.setData({ 
      //   playerSlots: res.data.players,
      //   onlineCount: res.data.onlineCount 
      // });
      
      console.log('刷新房间状态');
    } catch (error) {
      console.error('刷新房间状态失败:', error);
    }
  },

  // 开始等待时间计时
  startWaitingTimer() {
    let seconds = 45;
    this.waitingTimer = setInterval(() => {
      seconds++;
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      const timeString = `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
      this.setData({ waitingTime: timeString });
    }, 1000);
  },

  // 清理等待时间计时器
  clearWaitingTimer() {
    if (this.waitingTimer) {
      clearInterval(this.waitingTimer);
      this.waitingTimer = null;
    }
  },

  // 返回上一页
  goBack() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出房间吗？',
      success: (res) => {
        if (res.confirm) {
          this.leaveRoom();
        }
      }
    });
  },

  // 显示房间菜单
  showRoomMenu() {
    this.setData({ showMenu: true });
  },

  // 菜单显示状态变化
  onMenuVisibleChange(e) {
    this.setData({ showMenu: e.detail.visible });
  },

  // 准备状态切换
  async onReadyToggle() {
    try {
      const currentUser = roomManager.getCurrentUser();
      const room = roomManager.togglePlayerReady(this.data.roomId, currentUser.id);

      // 更新页面数据
      const player = room.players.get(currentUser.id);
      const canStart = roomManager.canStartGame(this.data.roomId);

      this.setData({
        isReady: player.isReady,
        playerSlots: room.playerSlots,
        canStart: canStart
      });

      this.showToast(player.isReady ? '已准备' : '取消准备');

      console.log('✅ 准备状态切换:', player.isReady);

    } catch (error) {
      console.error('❌ 准备状态切换失败:', error);
      this.showToast(error.message || '操作失败，请重试');
    }
  },



  // 开始游戏
  startGame() {
    try {
      // 如果是单人测试模式，直接开始测试游戏
      if (this.data.testMode.singlePlayer) {
        console.log('🧪 单人测试模式，直接开始测试游戏');
        this.startTestGame();
        return;
      }

      // 检查是否可以开始游戏
      if (!roomManager.canStartGame(this.data.roomId)) {
        wx.showToast({
          title: '玩家未全部准备',
          icon: 'error'
        });
        return;
      }

      // 标记游戏开始
      roomManager.startGame(this.data.roomId);

      // 直接跳转到AI剧本生成页面
      const playerCount = this.data.playerSlots.filter(slot => slot.status === 'occupied').length;

      console.log('🎮 开始游戏，跳转到AI剧本生成页面，玩家数量:', playerCount);

      wx.navigateTo({
        url: `/pages/ai-story-generator/ai-story-generator?roomId=${this.data.roomId}&maxPlayers=${playerCount}`,
        success: () => {
          console.log('✅ 跳转AI剧本生成页面成功');
        },
        fail: (err) => {
          console.error('❌ 跳转AI剧本生成页面失败:', err);
          wx.showToast({
            title: '跳转失败',
            icon: 'error'
          });
        }
      });

    } catch (error) {
      console.error('❌ 开始游戏失败:', error);
      wx.showToast({
        title: error.message || '开始游戏失败',
        icon: 'error'
      });
    }
  },

  // 邀请好友
  inviteFriends() {
    const dialog = this.selectComponent('#invite-dialog');
    dialog.showDialog();
  },

  // 分享房间号
  shareRoomCode() {
    wx.setClipboardData({
      data: `房间号：${this.data.roomInfo.roomId}`,
      success: () => {
        this.showToast('房间号已复制到剪贴板');
      }
    });
  },

  // 玩家位置点击
  onPlayerSlotTap(e) {
    const slotId = parseInt(e.currentTarget.dataset.slotId);
    const slot = this.data.playerSlots.find(s => s.id === slotId);
    const currentUser = roomManager.getCurrentUser();

    console.log('🎯 点击座位:', slotId, slot);

    if (slot.status === 'empty') {
      // 空座位，检查当前用户是否已经在房间中
      const currentUserSlot = this.data.playerSlots.find(s =>
        s.status === 'occupied' && s.player.id === currentUser.id
      );

      if (currentUserSlot) {
        // 当前用户已在房间中，移动座位
        this.moveToSlot(slotId);
      } else {
        // 当前用户不在房间中，邀请好友
        this.inviteFriends();
      }
    } else if (slot.player.id === currentUser.id) {
      // 点击自己的座位，显示个人信息
      this.showPlayerInfo(slot.player);
    } else {
      // 其他玩家，显示玩家信息
      this.showPlayerInfo(slot.player);
    }
  },

  // 移动到指定座位
  moveToSlot(newSlotIndex) {
    try {
      const currentUser = roomManager.getCurrentUser();
      const room = roomManager.movePlayerSlot(this.data.roomId, currentUser.id, newSlotIndex);

      // 更新页面数据
      this.setData({
        playerSlots: room.playerSlots
      });

      wx.showToast({
        title: '座位移动成功',
        icon: 'success'
      });

      console.log('✅ 座位移动成功');
    } catch (error) {
      console.error('❌ 座位移动失败:', error);
      wx.showToast({
        title: error.message || '移动失败',
        icon: 'error'
      });
    }
  },

  // 显示玩家信息
  showPlayerInfo(player) {
    wx.showModal({
      title: player.nickname,
      content: `状态：${player.isReady ? '已准备' : '未准备'}\n${player.isHost ? '房主' : '普通玩家'}`,
      showCancel: false
    });
  },

  // 打开房间设置
  openRoomSettings() {
    this.setData({ showMenu: false });
    wx.navigateTo({
      url: `/pages/game-settings/game-settings?roomId=${this.data.roomId}`
    });
  },

  // 分享房间
  shareRoom() {
    this.setData({ showMenu: false });
    this.shareRoomCode();
  },

  // 举报房间
  reportRoom() {
    this.setData({ showMenu: false });
    wx.showModal({
      title: '举报房间',
      content: '请选择举报原因',
      confirmText: '确认举报',
      success: (res) => {
        if (res.confirm) {
          this.showToast('举报已提交');
        }
      }
    });
  },

  // 退出房间
  leaveRoom() {
    this.setData({ showMenu: false });
    wx.navigateBack();
  },

  // 显示提示
  showToast(message, theme = 'success') {
    const toast = this.selectComponent('#t-toast');
    toast.showToast({
      theme,
      message,
      duration: 2000
    });
  },

  // 继续游戏
  continueGame() {
    // 根据当前阶段跳转到对应页面
    if (this.data.currentPhase === 'discussion') {
      this.goToDiscussion();
    } else if (this.data.currentPhase === 'voting') {
      this.goToVoting();
    } else {
      this.goToRoleAssignment();
    }
  },

  // 跳转到角色分配页面
  goToRoleAssignment() {
    wx.navigateTo({
      url: `/pages/role-assignment/role-assignment?roomId=${this.data.roomId}`
    });
  },

  // 跳转到私人线索页面
  goToPrivateClues() {
    wx.navigateTo({
      url: `/pages/private-clues/private-clues?roomId=${this.data.roomId}`
    });
  },

  // 跳转到讨论阶段页面
  goToDiscussion() {
    wx.navigateTo({
      url: `/pages/discussion/discussion?roomId=${this.data.roomId}`
    });
  },

  // 跳转到投票页面
  goToVoting() {
    wx.navigateTo({
      url: `/pages/voting/voting?roomId=${this.data.roomId}`
    });
  },

  // 显示游戏菜单
  showGameMenu() {
    wx.showActionSheet({
      itemList: ['返回大厅', '游戏设置', '退出游戏'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 返回大厅 - 当前页面就是大厅
            break;
          case 1:
            this.openRoomSettings();
            break;
          case 2:
            this.leaveRoom();
            break;
        }
      }
    });
  },

  // ==================== 测试功能相关方法 ====================

  // 打开测试功能面板
  openTestPanel() {
    console.log('🧪 打开测试功能面板');
    this.setData({
      showTestPanel: true,
      showMenu: false  // 关闭房间菜单
    });
  },

  // 关闭测试功能面板
  closeTestPanel() {
    this.setData({ showTestPanel: false });
  },

  // 测试面板显示状态变化
  onTestPanelVisibleChange(e) {
    this.setData({ showTestPanel: e.detail.visible });
  },

  // 单人测试模式切换
  onSinglePlayerModeChange(e) {
    const enabled = e.detail.value;
    console.log('👤 单人测试模式:', enabled);

    this.setData({
      'testMode.singlePlayer': enabled
    });

    if (enabled) {
      // 开启单人模式时，自动添加AI测试玩家
      this.addAITestPlayers();
      wx.showToast({
        title: '单人测试模式已开启',
        icon: 'success'
      });
    } else {
      wx.showToast({
        title: '单人测试模式已关闭',
        icon: 'none'
      });
    }
  },

  // 快速开始模式切换
  onQuickStartModeChange(e) {
    const enabled = e.detail.value;
    console.log('⚡ 快速开始模式:', enabled);

    this.setData({
      'testMode.quickStart': enabled
    });

    wx.showToast({
      title: enabled ? '快速开始模式已开启' : '快速开始模式已关闭',
      icon: 'none'
    });
  },

  // AI剧本生成模式切换
  onAIScriptModeChange(e) {
    const enabled = e.detail.value;
    console.log('🤖 AI剧本生成模式:', enabled);

    this.setData({
      'testMode.aiScript': enabled
    });

    wx.showToast({
      title: enabled ? 'AI剧本生成已开启' : 'AI剧本生成已关闭',
      icon: 'none'
    });
  },

  // 添加AI测试玩家
  addAITestPlayers() {
    try {
      const aiPlayers = [
        { id: 'ai_player_2', nickname: 'AI-艾米丽', avatar: '', isReady: true },
        { id: 'ai_player_3', nickname: 'AI-詹姆斯', avatar: '', isReady: true },
        { id: 'ai_player_4', nickname: 'AI-莉莉安', avatar: '', isReady: true },
        { id: 'ai_player_5', nickname: 'AI-维克多', avatar: '', isReady: true },
        { id: 'ai_player_6', nickname: 'AI-索菲亚', avatar: '', isReady: true }
      ];

      // 添加AI玩家到房间
      for (const aiPlayer of aiPlayers) {
        try {
          roomManager.joinRoom(this.data.roomId, aiPlayer);
        } catch (error) {
          console.log('AI玩家已存在或房间已满:', aiPlayer.nickname);
        }
      }

      // 更新房间信息显示
      this.refreshRoomInfo();

      console.log('✅ AI测试玩家添加完成');

    } catch (error) {
      console.error('❌ 添加AI测试玩家失败:', error);
    }
  },

  // 重置测试模式
  resetTestMode() {
    this.setData({
      testMode: {
        singlePlayer: false,
        quickStart: false,
        aiScript: true
      }
    });

    wx.showToast({
      title: '测试设置已重置',
      icon: 'success'
    });
  },

  // 开始测试游戏
  async startTestGame() {
    if (!this.data.testMode.singlePlayer) {
      wx.showToast({
        title: '请先开启单人测试模式',
        icon: 'none'
      });
      return;
    }

    try {
      console.log('🚀 开始测试游戏...');

      // 关闭测试面板
      this.setData({ showTestPanel: false });

      // 如果开启了AI剧本生成，先生成剧本
      if (this.data.testMode.aiScript) {
        wx.showLoading({ title: '生成剧本中...' });

        const aiService = require('../../utils/ai-service-simple');
        const scriptData = await aiService.generateScript({
          storyType: 'mystery',
          playerCount: 6,
          difficulty: 'medium',
          theme: '神秘庄园'
        });

        // 保存剧本到本地存储
        wx.setStorageSync(`script_${this.data.roomId}`, scriptData);

        wx.hideLoading();
        console.log('✅ 剧本生成完成:', scriptData.storyInfo?.title);
      }

      // 跳转到角色分配页面
      wx.redirectTo({
        url: `/pages/role-assignment/role-assignment?roomId=${this.data.roomId}`
      });

    } catch (error) {
      wx.hideLoading();
      console.error('❌ 开始测试游戏失败:', error);
      wx.showToast({
        title: '开始游戏失败',
        icon: 'error'
      });
    }
  },

  // 刷新房间信息
  refreshRoomInfo() {
    try {
      const room = roomManager.getRoomInfo(this.data.roomId);
      this.setData({
        playerSlots: room.playerSlots,
        onlineCount: room.players.size,
        canStart: roomManager.canStartGame(this.data.roomId)
      });
    } catch (error) {
      console.error('刷新房间信息失败:', error);
    }
  }
});
