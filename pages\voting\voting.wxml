<!-- 投票页面 -->
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="nav-left" bindtap="goBack">
        <text class="nav-icon">←</text>
      </view>
      <view class="nav-title">🗳️ 投票阶段 - 第{{currentRound}}轮</view>
      <view class="nav-right">
        <text class="timer {{timeLeft <= 30 ? 'urgent' : ''}}">{{formatTime(timeLeft)}}</text>
      </view>
    </view>
  </view>

  <!-- 页面内容 -->
  <view class="page-content">
    <!-- 投票说明卡片 -->
    <view class="instruction-card artistic-card animate-fadeIn">
      <view class="section-header">
        <text class="section-icon">🗳️</text>
        <text class="section-title text-3xl font-bold text-glow">{{votingPhase.title}}</text>
      </view>
      <view class="instruction-content mt-4">
        <text class="instruction-text text-lg text-gray-100 text-shadow">{{votingPhase.description}}</text>
      </view>
    </view>

    <!-- 投票选项卡片 -->
    <view class="voting-card artistic-card">
      <view class="section-header">
        <text class="section-icon">👥</text>
        <text class="section-title">选择投票对象</text>
      </view>
      
      <view class="voting-options">
        <view 
          class="vote-option {{selectedOption === item.id ? 'selected' : ''}}"
          wx:for="{{votingOptions}}" 
          wx:key="id"
          bindtap="selectOption"
          data-option-id="{{item.id}}"
        >
          <view class="option-avatar">
            <view class="avatar-circle">{{item.name.charAt(0)}}</view>
          </view>
          
          <view class="option-info">
            <view class="option-name">{{item.name}}</view>
            <view class="option-role">{{item.role}}</view>
            <view class="suspicion-meter">
              <text class="suspicion-label">嫌疑度：</text>
              <view class="suspicion-bar">
                <view class="suspicion-fill" style="width: {{item.suspicion}}%"></view>
              </view>
              <text class="suspicion-value">{{item.suspicion}}%</text>
            </view>
          </view>
          
          <view class="option-votes" wx:if="{{showVoteCount}}">
            <view class="vote-count">{{item.voteCount || 0}}</view>
            <view class="vote-label">票</view>
          </view>
          
          <view class="selection-indicator" wx:if="{{selectedOption === item.id}}">
            <text class="check-icon">✓</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 投票理由 -->
    <view class="reason-card artistic-card" wx:if="{{selectedOption && !hasVoted}}">
      <view class="section-header">
        <text class="section-icon">📝</text>
        <text class="section-title">投票理由（可选）</text>
      </view>
      <view class="reason-input">
        <textarea 
          class="reason-field"
          placeholder="说明你的投票理由..." 
          value="{{voteReason}}"
          bindinput="onReasonChange"
          maxlength="100"
          auto-height
        ></textarea>
        <view class="reason-counter">{{voteReason.length}}/100</view>
      </view>
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <button class="action-btn secondary" bindtap="testJump">
      <text class="btn-icon">🧪</text>
      <text>测试跳转</text>
    </button>
    <button
      class="action-btn primary"
      bindtap="submitVote"
      disabled="{{!selectedOption || hasVoted || submitting}}"
      loading="{{submitting}}"
    >
      <text class="btn-icon">🗳️</text>
      <text>{{hasVoted ? '已投票' : (submitting ? '提交中...' : '确认投票')}}</text>
    </button>
  </view>
</view>

<!-- 投票结果弹窗 -->
<view class="modal-overlay" wx:if="{{showVoteResult}}" bindtap="closeVoteResult">
  <view class="modal-content" catchtap="">
    <view class="modal-header">
      <text class="modal-title">投票结果</text>
      <text class="modal-close" bindtap="closeVoteResult">×</text>
    </view>
    <view class="modal-body">
      <view class="result-summary">
        <text class="result-text">{{voteResult.description}}</text>
      </view>
      <view class="result-details">
        <view class="result-item" wx:for="{{voteResult.details}}" wx:key="index">
          <view class="result-player">{{item.name}}</view>
          <view class="result-votes">{{item.votes}}票</view>
        </view>
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-btn primary" bindtap="continueGame">
        继续游戏
      </button>
    </view>
  </view>
</view>
