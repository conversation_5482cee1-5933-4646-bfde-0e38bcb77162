import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import{getCharacterLength}from"../common/utils";const{prefix:prefix}=config,name=`${prefix}-textarea`;let Textarea=class extends SuperComponent{constructor(){super(...arguments),this.options={multipleSlots:!0},this.behaviors=["wx://form-field"],this.externalClasses=[`${prefix}-class`,`${prefix}-class-textarea`,`${prefix}-class-label`,`${prefix}-class-indicator`],this.properties=props,this.data={prefix:prefix,classPrefix:name,count:0},this.observers={value(e){this.updateCount(null!=e?e:this.properties.defaultValue)}},this.lifetimes={ready(){var e;const{value:t,defaultValue:a}=this.properties;this.updateValue(null!==(e=null!=t?t:a)&&void 0!==e?e:"")}},this.methods={updateCount(e){const{maxcharacter:t,maxlength:a}=this.properties,{count:r}=this.calculateValue(e,t,a);this.setData({count:r})},updateValue(e){const{maxcharacter:t,maxlength:a}=this.properties,{value:r,count:s}=this.calculateValue(e,t,a);this.setData({value:r,count:s})},calculateValue(e,t,a){const{allowInputOverMax:r}=this.properties;if(t>0&&!Number.isNaN(t)){const{length:a,characters:s}=getCharacterLength("maxcharacter",e,r?1/0:t);return{value:s,count:a}}if(a>0&&!Number.isNaN(a)){const{length:t,characters:s}=getCharacterLength("maxlength",e,r?1/0:a);return{value:s,count:t}}return{value:e,count:e?String(e).length:0}},onInput(e){const{value:t,cursor:a}=e.detail;this.updateValue(t),this.triggerEvent("change",{value:this.data.value,cursor:a})},onFocus(e){this.triggerEvent("focus",Object.assign({},e.detail))},onBlur(e){this.triggerEvent("blur",Object.assign({},e.detail))},onConfirm(e){this.triggerEvent("enter",Object.assign({},e.detail))},onLineChange(e){this.triggerEvent("line-change",Object.assign({},e.detail))},onKeyboardHeightChange(e){this.triggerEvent("keyboardheightchange",e.detail)}}}};Textarea=__decorate([wxComponent()],Textarea);export default Textarea;