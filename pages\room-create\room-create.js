// 创建房间页面逻辑
const api = require('../../utils/api');
const common = require('../../utils/common');
const errorHandler = require('../../utils/error-handler');
const roomManager = require('../../utils/room-manager');

Page({
  data: {
    creating: false,
    formData: {
      roomName: '',
      password: '',
      maxPlayers: 6,
      gameStyle: 'classic',
      difficulty: 'medium'
    },

    styleOptions: [
      { value: 'classic', label: '经典推理 - 传统剧本杀模式' },
      { value: 'social', label: '社交互动 - 真心话大冒险增强' },
      { value: 'puzzle', label: '解谜挑战 - 逻辑推理为主' },
      { value: 'roleplay', label: '角色扮演 - 沉浸式体验' }
    ],
    styleSelectedIndex: 0,

    difficultyOptions: [
      { value: 'easy', label: '简单 - 新手友好，线索明显' },
      { value: 'medium', label: '中等 - 适度挑战，需要推理' },
      { value: 'hard', label: '困难 - 高难度，复杂剧情' }
    ],
    difficultySelectedIndex: 1,

    // 游戏特色（全部开启）
    gameFeatures: [
      { key: 'voice', label: '语音聊天', icon: 'sound', color: '#1890ff' },
      { key: 'truthDare', label: '真心话环节', icon: 'chat', color: '#52c41a' },
      { key: 'personalPlot', label: '个人剧情线', icon: 'user', color: '#ff6b6b' },
      { key: 'aiEnhanced', label: 'AI智能推演', icon: 'lightbulb', color: '#722ed1' },
      { key: 'realtime', label: '实时同步', icon: 'refresh', color: '#13c2c2' }
    ]
  },

  onLoad() {
    // 生成默认房间名称
    this.generateDefaultRoomName();
  },

  // 生成默认房间名称
  generateDefaultRoomName() {
    const themes = ['神秘庄园', '古堡疑云', '校园悬案', '都市迷案', '海岛密室'];
    const randomTheme = themes[Math.floor(Math.random() * themes.length)];
    const timestamp = Date.now().toString().slice(-4);

    this.setData({
      'formData.roomName': `${randomTheme}${timestamp}`
    });
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },

  // 房间名称变化
  onRoomNameChange(e) {
    this.setData({
      'formData.roomName': e.detail.value
    });
  },

  // 密码变化
  onPasswordChange(e) {
    this.setData({
      'formData.password': e.detail.value
    });
  },

  // 最大人数变化
  onMaxPlayersChange(e) {
    this.setData({
      'formData.maxPlayers': e.detail.value
    });
  },

  // 游戏风格变化
  onGameStyleChange(e) {
    const index = e.detail.value;
    this.setData({
      styleSelectedIndex: index,
      'formData.gameStyle': this.data.styleOptions[index].value
    });
  },

  // 难度等级变化
  onDifficultyChange(e) {
    const index = e.detail.value;
    this.setData({
      difficultySelectedIndex: index,
      'formData.difficulty': this.data.difficultyOptions[index].value
    });
  },



  // 创建房间
  createRoom() {
    // 验证表单
    if (!this.validateForm()) {
      return;
    }

    this.setData({ creating: true });

    try {
      console.log('🏠 开始创建房间...', this.data.formData);

      // 使用房间管理器创建房间
      const result = roomManager.createRoom({
        gameMode: this.data.formData.gameMode || '经典推理模式',
        maxPlayers: this.data.formData.maxPlayers || 6,
        timeLimit: this.data.formData.timeLimit || 600,
        rounds: this.data.formData.rounds || 3
      });

      console.log('🏠 房间创建结果:', result);

      const { roomId, room } = result;
      console.log('✅ 房间创建成功:', roomId);

      this.showToast('房间创建成功！', 'success');

      // 跳转到房间大厅页面
      wx.redirectTo({
        url: `/pages/room-lobby/room-lobby?roomId=${roomId}`,
        success: (res) => {
          console.log('✅ 跳转房间大厅成功', res);
          this.setData({ creating: false });
        },
        fail: (err) => {
          console.error('❌ 跳转房间大厅失败:', err);
          this.setData({ creating: false });
          wx.showToast({
            title: '跳转失败',
            icon: 'error'
          });
        }
      });

    } catch (error) {
      console.error('❌ 创建房间失败:', error);
      this.showToast(error.message || '创建失败，请重试', 'error');
      this.setData({ creating: false });
    }
  },

  // 验证表单
  validateForm() {
    const { roomName } = this.data.formData;
    
    if (!roomName.trim()) {
      this.showToast('请输入房间名称');
      return false;
    }

    if (roomName.trim().length < 2) {
      this.showToast('房间名称至少2个字符');
      return false;
    }

    return true;
  },

  // 生成房间ID
  generateRoomId() {
    return Math.floor(100000 + Math.random() * 900000).toString();
  },

  // 显示提示
  showToast(message, theme = 'warning') {
    // 使用原生wx.showToast，避免组件相关的Promise问题
    wx.showToast({
      title: message,
      icon: theme === 'success' ? 'success' : theme === 'error' ? 'error' : 'none',
      duration: 2000
    });
  }
});
