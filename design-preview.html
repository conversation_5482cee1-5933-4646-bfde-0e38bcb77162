<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI推理游戏 - 现代设计预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg,
                #87CEEB 0%,     /* 天蓝色 */
                #98D8E8 15%,    /* 浅蓝 */
                #F4D03F 35%,    /* 金黄 */
                #F39C12 50%,    /* 橙色 */
                #E67E22 65%,    /* 深橙 */
                #F1948A 80%,    /* 浅粉 */
                #D7BDE2 95%,    /* 淡紫 */
                #E8DAEF 100%    /* 浅紫 */
            );
            min-height: 100vh;
            color: #2c3e50;
            overflow-x: hidden;
        }

        .phone-frame {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            background: #000;
            border-radius: 40px;
            padding: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.5);
            position: relative;
        }

        .screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg,
                #87CEEB 0%,     /* 天蓝色 */
                #98D8E8 15%,    /* 浅蓝 */
                #F4D03F 35%,    /* 金黄 */
                #F39C12 50%,    /* 橙色 */
                #E67E22 65%,    /* 深橙 */
                #F1948A 80%,    /* 浅粉 */
                #D7BDE2 95%,    /* 淡紫 */
                #E8DAEF 100%    /* 浅紫 */
            );
            border-radius: 30px;
            overflow: hidden;
            position: relative;
        }

        /* 背景效果 */
        .stars-layer {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(1px 1px at 40px 60px, rgba(255,255,255,0.6), transparent),
                radial-gradient(1px 1px at 120px 100px, rgba(255,255,255,0.4), transparent),
                radial-gradient(1px 1px at 200px 40px, rgba(255,255,255,0.5), transparent),
                radial-gradient(1px 1px at 280px 120px, rgba(255,255,255,0.3), transparent),
                radial-gradient(1px 1px at 180px 200px, rgba(120,119,198,0.4), transparent);
            animation: twinkle 6s ease-in-out infinite alternate;
        }

        .warm-glow {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 30%, rgba(120,119,198,0.08) 0%, transparent 60%),
                radial-gradient(circle at 80% 70%, rgba(120,119,198,0.06) 0%, transparent 60%);
            animation: warmGlow 8s ease-in-out infinite alternate;
        }

        /* 主要内容 */
        .content {
            position: relative;
            z-index: 10;
            padding: 60px 20px;
            text-align: center;
        }

        .welcome-badge {
            margin-bottom: 24px;
        }

        .badge-text {
            color: rgba(0,0,0,0.6);
            font-size: 12px;
            font-weight: 500;
            letter-spacing: 3px;
            text-transform: uppercase;
            text-shadow: 0 1px 2px rgba(255,255,255,0.8);
        }

        .main-title {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 40px;
        }

        .logo-circle {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #7c77c6 0%, #5a67d8 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            box-shadow: 0 4px 16px rgba(120,119,198,0.3);
            position: relative;
        }

        .logo-text {
            color: white;
            font-size: 12px;
            font-weight: 600;
        }

        .logo-decoration {
            position: absolute;
            top: -2px;
            right: -2px;
            width: 6px;
            height: 6px;
            background: #00d4aa;
            border-radius: 50%;
            border: 1px solid white;
        }

        .title-text {
            color: #2c3e50;
            font-size: 28px;
            font-weight: 700;
            letter-spacing: -0.5px;
            text-shadow: 0 2px 4px rgba(255,255,255,0.8);
        }

        .subtitle-container {
            margin-bottom: 40px;
            max-width: 300px;
            margin-left: auto;
            margin-right: auto;
        }

        .subtitle-main,
        .subtitle-highlight,
        .subtitle-end {
            display: block;
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 6px;
        }

        .subtitle-main {
            color: rgba(255,255,255,0.8);
        }

        .subtitle-highlight {
            color: #7c77c6;
            font-weight: 500;
        }

        .subtitle-end {
            color: rgba(255,255,255,0.7);
        }

        /* 特色展示 */
        .feature-showcase {
            display: flex;
            justify-content: center;
            gap: 16px;
            margin-bottom: 40px;
            flex-wrap: wrap;
        }

        .feature-item {
            background: rgba(255,255,255,0.03);
            border: 1px solid rgba(255,255,255,0.08);
            border-radius: 12px;
            padding: 16px 12px;
            min-width: 90px;
            transition: all 0.3s ease;
        }

        .feature-item:hover {
            background: rgba(120,119,198,0.08);
            transform: translateY(-2px);
        }

        .feature-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .feature-text {
            color: white;
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .feature-desc {
            color: rgba(255,255,255,0.6);
            font-size: 10px;
        }

        /* 场景 */
        .scene-container {
            margin-bottom: 40px;
        }

        .scene-title {
            color: rgba(255,255,255,0.6);
            font-size: 12px;
            margin-bottom: 24px;
        }

        .table-3d {
            width: 160px;
            height: 100px;
            margin: 0 auto;
            position: relative;
        }

        .table-surface {
            position: absolute;
            top: 40px;
            left: 20px;
            right: 20px;
            height: 60px;
            background: linear-gradient(135deg, #2a2a2a 0%, #3a3a3a 50%, #2a2a2a 100%);
            border-radius: 50%;
            box-shadow: 0 8px 24px rgba(0,0,0,0.4);
            transform: perspective(200px) rotateX(35deg);
        }

        .mystery-light {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(120,119,198,0.4) 0%, transparent 100%);
            animation: mysteryPulse 4s ease-in-out infinite;
        }

        /* 按钮 */
        .main-actions {
            display: flex;
            flex-direction: column;
            gap: 12px;
            padding: 0 20px;
        }

        /* 内凹按钮效果 - 模拟图片中的凹陷效果 */
        .artistic-btn {
            border: none;
            border-radius: 40px;
            width: 160px;
            height: 48px;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: row;
            gap: 8px;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
            /* 强烈的内凹效果 - 模拟按钮被按下的状态 */
            background: linear-gradient(145deg, #d0d0d0, #f0f0f0);
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'PingFang SC', sans-serif;
            box-shadow:
                /* 强烈的内部阴影 - 创造深度凹陷感 */
                inset 6px 6px 12px #b8b8b8,
                inset -6px -6px 12px #ffffff,
                /* 轻微的外部阴影 - 保持边界清晰 */
                1px 1px 4px rgba(0,0,0,0.1);
            border: 0.5px solid rgba(0,0,0,0.1);
        }

        .create-btn, .join-btn {
            /* 继承基础的内凹效果 */
            color: #333333;
        }

        .artistic-btn:hover {
            transform: translateY(-0.5px);
            /* 悬停时稍微减少内凹效果 */
            box-shadow:
                inset 4px 4px 8px #b8b8b8,
                inset -4px -4px 8px #ffffff,
                2px 2px 6px rgba(0,0,0,0.15);
        }

        .artistic-btn:active {
            transform: translateY(0.5px) scale(0.99);
            /* 按下时进一步加深内凹效果 */
            box-shadow:
                /* 更深的内部阴影 - 模拟按钮被进一步按下 */
                inset 8px 8px 16px #a8a8a8,
                inset -8px -8px 16px #ffffff,
                /* 几乎消除外部阴影 */
                0.5px 0.5px 2px rgba(0,0,0,0.05);
        }

        .btn-icon {
            font-size: 16px;
            opacity: 0.8;
            filter: drop-shadow(0 0.5px 0.5px rgba(255,255,255,0.8));
        }

        .btn-main-text {
            color: #333333;
            text-shadow: 0 0.5px 0.5px rgba(255,255,255,0.8);
        }

        .btn-sub-text {
            color: #666666;
            text-shadow: 0 0.5px 0.5px rgba(255,255,255,0.8);
        }

        /* 动画 */
        @keyframes twinkle {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.7; }
        }

        @keyframes warmGlow {
            0% { opacity: 0.6; }
            100% { opacity: 0.8; }
        }

        @keyframes mysteryPulse {
            0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(1); }
            50% { opacity: 0.5; transform: translate(-50%, -50%) scale(1.05); }
        }

        .comparison {
            display: flex;
            gap: 40px;
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
        }

        .comparison-item {
            flex: 1;
            text-align: center;
        }

        .comparison-title {
            font-size: 24px;
            margin-bottom: 20px;
            color: white;
        }

        .old-design {
            color: #ff6b6b;
        }

        .new-design {
            color: #00d4aa;
        }
    </style>
</head>
<body>
    <div class="comparison">
        <div class="comparison-item">
            <h2 class="comparison-title old-design">❌ 原设计问题</h2>
            <ul style="text-align: left; color: rgba(255,255,255,0.7); line-height: 1.8;">
                <li>刺眼的金黄色过于浮夸</li>
                <li>过多的光效和动画干扰</li>
                <li>字体过重，缺乏层次感</li>
                <li>布局拥挤，缺少留白</li>
                <li>整体风格过于"土味"</li>
            </ul>
        </div>
        <div class="comparison-item">
            <h2 class="comparison-title new-design">✅ 现代化改进</h2>
            <ul style="text-align: left; color: rgba(255,255,255,0.7); line-height: 1.8;">
                <li>克制的蓝紫色调，更现代</li>
                <li>微妙的动画效果，不干扰</li>
                <li>精致的字体层次，易读性强</li>
                <li>合理的留白，视觉呼吸感</li>
                <li>苹果风格+游戏感结合</li>
            </ul>
        </div>
    </div>

    <div class="phone-frame">
        <div class="screen">
            <div class="stars-layer"></div>
            <div class="warm-glow"></div>
            
            <div class="content">
                <div class="welcome-badge">
                    <div class="badge-text">WELCOME TO</div>
                </div>

                <div class="main-title">
                    <div class="logo-circle">
                        <div class="logo-text">AI</div>
                        <div class="logo-decoration"></div>
                    </div>
                    <div class="title-text">AI推理大师</div>
                </div>

                <div class="subtitle-container">
                    <div class="subtitle-main">智能剧本生成</div>
                    <div class="subtitle-highlight">沉浸式推理体验</div>
                    <div class="subtitle-end">与好友共同揭开真相</div>
                </div>

                <div class="feature-showcase">
                    <div class="feature-item">
                        <div class="feature-icon">🧠</div>
                        <div class="feature-text">AI智能</div>
                        <div class="feature-desc">智能剧本生成</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">🎪</div>
                        <div class="feature-text">沉浸体验</div>
                        <div class="feature-desc">角色深度扮演</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">⚡</div>
                        <div class="feature-text">实时互动</div>
                        <div class="feature-desc">多人协作推理</div>
                    </div>
                </div>

                <div class="scene-container">
                    <div class="scene-title">推理现场</div>
                    <div class="table-3d">
                        <div class="table-surface"></div>
                        <div class="mystery-light"></div>
                    </div>
                </div>

                <div class="main-actions">
                    <button class="artistic-btn create-btn">
                        <span class="btn-icon">👑</span>
                        创建房间
                    </button>
                    <button class="artistic-btn join-btn">
                        <span class="btn-icon">🎯</span>
                        加入游戏
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
