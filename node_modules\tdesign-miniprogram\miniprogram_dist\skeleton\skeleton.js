import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import{classNames}from"../common/utils";import{isNumber}from"../common/validator";const{prefix:prefix}=config,name=`${prefix}-skeleton`,ThemeMap={avatar:[{type:"circle",size:"96rpx"}],image:[{type:"rect",size:"144rpx"}],text:[[{width:"24%",height:"32rpx",marginRight:"32rpx"},{width:"76%",height:"32rpx"}],1],paragraph:[1,1,1,{width:"55%"}]};let Skeleton=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`,`${prefix}-class-col`,`${prefix}-class-row`],this.properties=props,this.data={prefix:prefix,classPrefix:name,parsedRowcols:[]},this.observers={rowCol(){this.init()},"loading, delay"(){this.isShowSkeleton()}},this.lifetimes={attached(){this.init(),this.isShowSkeleton()}},this.methods={init(){const{theme:t,rowCol:e}=this.properties,s=[];e.length?s.push(...e):s.push(...ThemeMap[t||"text"]);const i=s.map(t=>{if(isNumber(t))return[{class:this.getColItemClass({type:"text"}),style:{}}];if(Array.isArray(t))return t.map(t=>Object.assign(Object.assign({},t),{class:this.getColItemClass(t),style:this.getColItemStyle(t)}));const e=t;return[Object.assign(Object.assign({},e),{class:this.getColItemClass(e),style:this.getColItemStyle(e)})]});this.setData({parsedRowcols:i})},getColItemClass(t){return classNames([`${name}__col`,`${name}--type-${t.type||"text"}`,`${name}--animation-${this.properties.animation}`])},getColItemStyle(t){const e={};return["width","height","marginRight","marginLeft","margin","size","background","backgroundColor","borderRadius"].forEach(s=>{if(s in t){const i=isNumber(t[s])?`${t[s]}px`:t[s];"size"===s?[e.width,e.height]=[i,i]:e[s]=i}}),e},isShowSkeleton(){const{loading:t,delay:e}=this.properties;t&&0!==e?setTimeout(()=>{this.setData({isShow:t})},e):this.setData({isShow:t})}}}};Skeleton=__decorate([wxComponent()],Skeleton);export default Skeleton;