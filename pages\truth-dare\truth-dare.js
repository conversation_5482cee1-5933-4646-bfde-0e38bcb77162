// 真心话时间页面逻辑
Page({
  data: {
    roomId: '',
    timeLeft: '05:00',
    currentPlayerIndex: 0,
    isMyTurn: true,
    answerText: '',
    currentQuestion: '在这个案件中，你最怀疑谁？为什么？',
    currentPlayer: {
      name: '玩家1',
      role: '侦探',
      avatar: '🕵️'
    },
    players: [
      { name: '玩家1', role: '侦探', avatar: '🕵️' },
      { name: '玩家2', role: '凶手', avatar: '😈' },
      { name: '玩家3', role: '平民', avatar: '👤' },
      { name: '玩家4', role: '平民', avatar: '👤' },
      { name: '玩家5', role: '共犯', avatar: '😰' },
      { name: '玩家6', role: '平民', avatar: '👤' }
    ],
    questions: [
      '在这个案件中，你最怀疑谁？为什么？',
      '你认为凶手的动机是什么？',
      '如果你是凶手，你会怎么做？',
      '你觉得哪个线索最重要？',
      '你有什么秘密没有告诉大家？',
      '你认为谁在说谎？'
    ],
    answerHistory: [
      {
        player: { name: '玩家2', avatar: '😈' },
        question: '你认为凶手的动机是什么？',
        answer: '我觉得可能是为了钱，毕竟死者很富有...'
      }
    ]
  },

  onLoad(options) {
    console.log('真心话时间页面加载', options);
    if (options.roomId) {
      this.setData({ roomId: options.roomId });
    }

    // 开始倒计时
    this.startTimer();

    // 初始化当前玩家
    this.updateCurrentPlayer();
  },

  // 开始倒计时
  startTimer() {
    let totalSeconds = 5 * 60; // 5分钟

    this.timer = setInterval(() => {
      totalSeconds--;

      if (totalSeconds <= 0) {
        this.clearTimer();
        this.timeUp();
        return;
      }

      const minutes = Math.floor(totalSeconds / 60);
      const seconds = totalSeconds % 60;
      const timeLeft = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

      this.setData({ timeLeft });
    }, 1000);
  },

  // 清除计时器
  clearTimer() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },

  // 时间到
  timeUp() {
    wx.showModal({
      title: '真心话时间结束',
      content: '真心话环节已结束，即将进入最终投票',
      showCancel: false,
      success: () => {
        this.proceedToNextStage();
      }
    });
  },

  // 更新当前玩家
  updateCurrentPlayer() {
    const currentPlayer = this.data.players[this.data.currentPlayerIndex];
    const currentQuestion = this.data.questions[this.data.currentPlayerIndex % this.data.questions.length];
    const isMyTurn = this.data.currentPlayerIndex === 0; // 假设第一个是当前用户

    this.setData({
      currentPlayer,
      currentQuestion,
      isMyTurn
    });
  },

  // 回答输入
  onAnswerInput(e) {
    this.setData({ answerText: e.detail.value });
  },

  // 提交回答
  submitAnswer() {
    if (!this.data.answerText.trim()) return;

    const newAnswer = {
      player: this.data.currentPlayer,
      question: this.data.currentQuestion,
      answer: this.data.answerText
    };

    const answerHistory = [...this.data.answerHistory, newAnswer];
    this.setData({
      answerHistory,
      answerText: ''
    });

    wx.showToast({
      title: '回答已提交',
      icon: 'success'
    });

    // 自动进入下一位
    setTimeout(() => {
      this.nextPlayer();
    }, 1500);
  },

  // 跳过问题
  skipQuestion() {
    wx.showModal({
      title: '跳过问题',
      content: '确定要跳过这个问题吗？',
      success: (res) => {
        if (res.confirm) {
          this.nextPlayer();
        }
      }
    });
  },

  // 下一位玩家
  nextPlayer() {
    const nextIndex = this.data.currentPlayerIndex + 1;

    if (nextIndex >= this.data.players.length) {
      // 所有玩家都回答完了，进入下一阶段
      wx.showModal({
        title: '真心话环节结束',
        content: '所有玩家都已回答完毕，即将进入游戏结果环节',
        showCancel: false,
        success: () => {
          this.proceedToNextStage();
        }
      });
    } else {
      // 切换到下一位玩家
      this.setData({
        currentPlayerIndex: nextIndex,
        answerText: ''
      });
      this.updateCurrentPlayer();
    }
  },

  // 进入下一阶段
  proceedToNextStage() {
    this.clearTimer();

    // 跳转到游戏结果页面
    wx.redirectTo({
      url: `/pages/game-result/game-result?roomId=${this.data.roomId}`
    });
  },

  onUnload() {
    this.clearTimer();
  }
})