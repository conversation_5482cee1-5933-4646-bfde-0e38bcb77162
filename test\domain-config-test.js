// test/domain-config-test.js
// 域名配置验证测试

/**
 * 域名配置测试
 */
class DomainConfigTest {
  /**
   * 测试域名配置是否生效
   */
  static async testDomainConfig() {
    console.log('🔍 测试微信小程序域名配置...');
    
    return new Promise((resolve) => {
      // 测试简单的GET请求到API域名
      wx.request({
        url: 'https://api.moonshot.cn',
        method: 'GET',
        timeout: 10000,
        success: (res) => {
          console.log('✅ 域名配置测试成功');
          console.log('响应状态:', res.statusCode);
          console.log('响应头:', res.header);
          
          resolve({
            success: true,
            message: '域名配置正确，可以访问API服务器',
            statusCode: res.statusCode,
            responseTime: Date.now()
          });
        },
        fail: (error) => {
          console.log('❌ 域名配置测试失败');
          console.log('错误信息:', error);
          
          let message = '域名配置可能有问题';
          
          if (error.errMsg.includes('不在以下 request 合法域名列表中')) {
            message = '域名未添加到微信小程序合法域名列表中';
          } else if (error.errMsg.includes('网络')) {
            message = '网络连接问题，请检查网络状态';
          } else if (error.errMsg.includes('超时')) {
            message = '请求超时，可能是网络延迟问题';
          }
          
          resolve({
            success: false,
            message: message,
            error: error.errMsg
          });
        }
      });
    });
  }

  /**
   * 测试API端点访问
   */
  static async testApiEndpoint() {
    console.log('🔌 测试API端点访问...');
    
    return new Promise((resolve) => {
      wx.request({
        url: 'https://api.moonshot.cn/v1/chat/completions',
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-key'
        },
        data: {
          model: 'kimi-k2-0711-preview',
          messages: [{ role: 'user', content: 'test' }]
        },
        timeout: 15000,
        success: (res) => {
          console.log('✅ API端点访问成功');
          console.log('响应状态:', res.statusCode);
          
          let message = 'API端点可以访问';
          if (res.statusCode === 401) {
            message = 'API端点正常，认证失败（这是预期的，因为使用了测试密钥）';
          }
          
          resolve({
            success: true,
            message: message,
            statusCode: res.statusCode
          });
        },
        fail: (error) => {
          console.log('❌ API端点访问失败');
          console.log('错误信息:', error);
          
          resolve({
            success: false,
            message: 'API端点访问失败',
            error: error.errMsg
          });
        }
      });
    });
  }

  /**
   * 运行完整的域名配置测试
   */
  static async runFullTest() {
    console.log('🚀 开始域名配置完整测试...\n');
    
    const results = {
      domainTest: await this.testDomainConfig(),
      apiTest: await this.testApiEndpoint(),
      timestamp: new Date().toISOString()
    };
    
    console.log('\n📊 测试结果总结:');
    console.log('================');
    
    console.log('\n1. 域名配置测试:');
    console.log(`   状态: ${results.domainTest.success ? '✅ 通过' : '❌ 失败'}`);
    console.log(`   信息: ${results.domainTest.message}`);
    if (results.domainTest.error) {
      console.log(`   错误: ${results.domainTest.error}`);
    }
    
    console.log('\n2. API端点测试:');
    console.log(`   状态: ${results.apiTest.success ? '✅ 通过' : '❌ 失败'}`);
    console.log(`   信息: ${results.apiTest.message}`);
    if (results.apiTest.error) {
      console.log(`   错误: ${results.apiTest.error}`);
    }
    
    // 生成建议
    console.log('\n💡 建议:');
    if (results.domainTest.success && results.apiTest.success) {
      console.log('   🎉 域名配置完全正确！可以正常使用AI功能。');
    } else if (!results.domainTest.success) {
      console.log('   ⚠️  请检查微信小程序后台的域名配置：');
      console.log('   1. 登录微信小程序后台');
      console.log('   2. 进入"开发" → "开发管理" → "开发设置"');
      console.log('   3. 在"request合法域名"中添加: https://api.moonshot.ai');
      console.log('   4. 保存配置并等待生效（通常几分钟内）');
    } else {
      console.log('   ✅ 域名配置正确，但API访问有问题，可能是网络或API服务问题。');
    }
    
    return results;
  }
}

module.exports = DomainConfigTest;
