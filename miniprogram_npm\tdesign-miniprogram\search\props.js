const props={action:{type:String,value:""},adjustPosition:{type:Boolean,value:!0},alwaysEmbed:{type:Boolean,value:!1},center:{type:Boolean,value:!1},clearTrigger:{type:String,value:"always"},clearable:{type:Boolean,value:!0},confirmHold:{type:Boolean,value:!1},confirmType:{type:String,value:"search"},cursor:{type:Number,value:-1,required:!0},cursorSpacing:{type:Number,value:0},disabled:{type:Boolean,value:!1},focus:{type:Boolean,value:!1},holdKeyboard:{type:Boolean,value:!1},leftIcon:{type:String,value:"search"},maxcharacter:{type:Number},maxlength:{type:Number,value:-1},placeholder:{type:String,value:""},placeholderClass:{type:String,value:"input-placeholder"},placeholderStyle:{type:String,value:"",required:!0},readonly:{type:null,value:void 0},resultList:{type:Array,value:[]},selectionEnd:{type:Number,value:-1},selectionStart:{type:Number,value:-1},shape:{type:String,value:"square"},type:{type:String,value:"text"},value:{type:String,value:""}};export default props;