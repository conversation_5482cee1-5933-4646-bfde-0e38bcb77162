<!--pages/cloud-test/cloud-test.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <view class="title">☁️ 云开发测试</view>
    <view class="subtitle">测试云AI服务连接状态</view>
  </view>

  <!-- 云开发状态 -->
  <view class="status-section">
    <view class="section-title">云开发状态</view>
    <view class="status-card">
      <view class="status-item">
        <text class="status-label">云开发:</text>
        <text class="status-value {{cloudStatus.cloudEnabled ? 'success' : 'error'}}">
          {{cloudStatus.cloudEnabled ? '✅ 已启用' : '❌ 未启用'}}
        </text>
      </view>
      <view class="status-item">
        <text class="status-label">微信云:</text>
        <text class="status-value {{cloudStatus.hasWxCloud ? 'success' : 'error'}}">
          {{cloudStatus.hasWxCloud ? '✅ 可用' : '❌ 不可用'}}
        </text>
      </view>
      <view class="status-item">
        <text class="status-label">当前模式:</text>
        <text class="status-value {{cloudStatus.currentMode === 'cloud' ? 'success' : 'warning'}}">
          {{cloudStatus.currentMode === 'cloud' ? '☁️ 云服务' : '🎭 模拟服务'}}
        </text>
      </view>
    </view>
  </view>

  <!-- 测试按钮 -->
  <view class="test-section">
    <view class="section-title">功能测试</view>
    
    <button class="test-btn primary" bindtap="testCloudConnection" disabled="{{isLoading}}">
      <text class="btn-icon">🔍</text>
      <text class="btn-text">测试连接</text>
    </button>
    
    <button class="test-btn secondary" bindtap="testScriptGeneration" disabled="{{isLoading}}">
      <text class="btn-icon">📝</text>
      <text class="btn-text">测试剧本生成</text>
    </button>
    
    <button class="test-btn secondary" bindtap="testCharacterGeneration" disabled="{{isLoading}}">
      <text class="btn-icon">👤</text>
      <text class="btn-text">测试角色生成</text>
    </button>
  </view>

  <!-- 测试结果 -->
  <view class="results-section" wx:if="{{testResults.cloudService || testResults.mockService}}">
    <view class="section-title">测试结果</view>
    
    <!-- 云服务结果 -->
    <view class="result-card" wx:if="{{testResults.cloudService}}">
      <view class="result-header">
        <text class="result-title">☁️ 云AI服务</text>
        <text class="result-status {{testResults.cloudService.available ? 'success' : 'error'}}">
          {{testResults.cloudService.available ? '✅ 可用' : '❌ 不可用'}}
        </text>
      </view>
      <view class="result-message">{{testResults.cloudService.message}}</view>
    </view>
    
    <!-- 模拟服务结果 -->
    <view class="result-card" wx:if="{{testResults.mockService}}">
      <view class="result-header">
        <text class="result-title">🎭 模拟AI服务</text>
        <text class="result-status {{testResults.mockService.available ? 'success' : 'error'}}">
          {{testResults.mockService.available ? '✅ 可用' : '❌ 不可用'}}
        </text>
      </view>
      <view class="result-message">{{testResults.mockService.message}}</view>
    </view>
    
    <!-- 推荐方案 -->
    <view class="recommendation">
      <text class="rec-label">推荐使用:</text>
      <text class="rec-value {{testResults.recommendation === 'cloud' ? 'success' : 'warning'}}">
        {{testResults.recommendation === 'cloud' ? '☁️ 云AI服务' : '🎭 模拟AI服务'}}
      </text>
    </view>
  </view>

  <!-- 控制按钮 -->
  <view class="control-section">
    <view class="section-title">服务控制</view>
    
    <button class="control-btn" bindtap="forceUseMockService">
      <text class="btn-icon">🎭</text>
      <text class="btn-text">强制使用模拟服务</text>
    </button>
    
    <button class="control-btn" bindtap="tryEnableCloudService">
      <text class="btn-icon">☁️</text>
      <text class="btn-text">尝试启用云服务</text>
    </button>
  </view>

  <!-- 帮助信息 -->
  <view class="help-section">
    <view class="section-title">帮助信息</view>
    
    <button class="help-btn" bindtap="openCloudConsole">
      <text class="btn-icon">🔧</text>
      <text class="btn-text">云开发控制台</text>
    </button>
    
    <button class="help-btn" bindtap="viewSetupGuide">
      <text class="btn-icon">📖</text>
      <text class="btn-text">设置指南</text>
    </button>
  </view>

  <!-- 测试历史 -->
  <view class="history-section" wx:if="{{testHistory.length > 0}}">
    <view class="section-header">
      <text class="section-title">测试历史</text>
      <button class="clear-btn" bindtap="clearTestHistory">清除</button>
    </view>
    
    <view class="history-list">
      <view class="history-item" wx:for="{{testHistory}}" wx:key="id" 
            bindtap="viewTestDetails" data-index="{{index}}">
        <view class="history-header">
          <text class="history-type">{{item.type}}</text>
          <text class="history-status {{item.success ? 'success' : 'error'}}">
            {{item.success ? '✅' : '❌'}}
          </text>
        </view>
        <view class="history-time">{{item.timestamp}}</view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{isLoading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">测试中...</text>
    </view>
  </view>

  <!-- 返回按钮 -->
  <view class="footer">
    <button class="back-btn" bindtap="goBack">
      <text class="btn-icon">←</text>
      <text class="btn-text">返回</text>
    </button>
  </view>
</view>
