// utils/cloud-env-checker.js
// 云开发环境检查工具

class CloudEnvChecker {
  constructor() {
    this.currentAppId = 'wxe5962f9a3d2cdd06';
    this.availableEnvs = [
      'cloud1-7ggoe4se871c08ae',
      'cloud1-1g88tqlcd2222f2e',
      'party-game-ai-7g9k8m2h0c8e5f3a'
    ];
  }

  /**
   * 检查云开发基础环境
   */
  checkBasicEnvironment() {
    const result = {
      hasWxCloud: !!wx.cloud,
      libVersion: wx.getSystemInfoSync().SDKVersion,
      appId: this.currentAppId,
      timestamp: new Date().toISOString()
    };

    console.log('🔍 云开发基础环境检查:', result);
    return result;
  }

  /**
   * 测试单个环境
   */
  async testSingleEnvironment(envId) {
    console.log(`🧪 测试环境: ${envId}`);
    
    try {
      // 尝试初始化
      wx.cloud.init({
        env: envId,
        traceUser: true
      });

      // 测试基本功能
      const testResult = await this.performBasicTest(envId);
      
      return {
        envId: envId,
        success: true,
        message: '环境可用',
        details: testResult
      };
      
    } catch (error) {
      console.error(`❌ 环境 ${envId} 测试失败:`, error);
      
      return {
        envId: envId,
        success: false,
        message: this.parseError(error),
        error: {
          code: error.errCode || error.code,
          message: error.errMsg || error.message
        }
      };
    }
  }

  /**
   * 执行基本测试
   */
  async performBasicTest(envId) {
    try {
      // 测试云函数调用（即使函数不存在也能测试连接）
      const result = await wx.cloud.callFunction({
        name: 'test-connection',
        data: { test: true }
      });

      return {
        type: 'function_call',
        success: true,
        result: result
      };

    } catch (error) {
      // 检查各种"正常"的错误情况
      const errorMsg = error.errMsg || '';

      if (errorMsg.includes('FunctionNotFound') ||
          errorMsg.includes('FunctionName parameter could not be found')) {
        // 函数不存在，但环境连接正常
        return {
          type: 'function_call',
          success: true,
          message: '环境连接正常（云函数未部署）',
          note: '这是正常情况，说明云开发环境可用'
        };
      }

      // 其他错误需要抛出
      throw error;
    }
  }

  /**
   * 解析错误信息
   */
  parseError(error) {
    const errorCode = error.errCode || error.code;
    
    switch (errorCode) {
      case -501000:
        return `环境参数无效 - 环境可能未与小程序 ${this.currentAppId} 关联`;
      case -501001:
        return '环境不存在或已删除';
      case -501002:
        return '环境未开通或已停用';
      case -501003:
        return '环境权限不足';
      default:
        return error.errMsg || error.message || '未知错误';
    }
  }

  /**
   * 测试所有可用环境
   */
  async testAllEnvironments() {
    console.log('🔍 开始测试所有云开发环境...');
    
    const results = [];
    
    for (const envId of this.availableEnvs) {
      const result = await this.testSingleEnvironment(envId);
      results.push(result);
      
      // 如果找到可用环境，可以提前返回
      if (result.success) {
        console.log(`✅ 找到可用环境: ${envId}`);
      }
    }
    
    return {
      timestamp: new Date().toISOString(),
      appId: this.currentAppId,
      totalTested: results.length,
      successCount: results.filter(r => r.success).length,
      results: results,
      recommendation: this.getRecommendation(results)
    };
  }

  /**
   * 获取推荐方案
   */
  getRecommendation(results) {
    const successfulEnvs = results.filter(r => r.success);
    
    if (successfulEnvs.length === 0) {
      return {
        action: 'create_new_env',
        message: '建议创建新的云开发环境',
        steps: [
          '1. 在微信开发者工具中点击"云开发"',
          '2. 点击"新建环境"',
          '3. 确保环境与当前小程序关联',
          '4. 更新代码中的环境ID'
        ]
      };
    }
    
    const bestEnv = successfulEnvs[0];
    return {
      action: 'use_existing_env',
      message: `推荐使用环境: ${bestEnv.envId}`,
      envId: bestEnv.envId,
      steps: [
        `1. 确认代码中使用环境ID: ${bestEnv.envId}`,
        '2. 部署云函数到该环境',
        '3. 配置环境变量'
      ]
    };
  }

  /**
   * 生成诊断报告
   */
  async generateDiagnosticReport() {
    console.log('📊 生成云开发诊断报告...');
    
    const basicEnv = this.checkBasicEnvironment();
    const envTests = await this.testAllEnvironments();
    
    const report = {
      timestamp: new Date().toISOString(),
      basicEnvironment: basicEnv,
      environmentTests: envTests,
      summary: {
        cloudSupported: basicEnv.hasWxCloud,
        availableEnvironments: envTests.successCount,
        totalEnvironments: envTests.totalTested,
        recommendation: envTests.recommendation
      }
    };
    
    console.log('📊 诊断报告:', report);
    return report;
  }

  /**
   * 显示友好的错误信息
   */
  showUserFriendlyError(error) {
    const errorCode = error.errCode || error.code;
    let title = '云开发连接失败';
    let content = '';
    
    switch (errorCode) {
      case -501000:
        title = '环境配置错误';
        content = `云开发环境未与当前小程序关联。\n\n请检查：\n1. 环境ID是否正确\n2. 环境是否与小程序 ${this.currentAppId} 关联\n3. 环境是否已开通`;
        break;
      case -501001:
        title = '环境不存在';
        content = '指定的云开发环境不存在或已被删除。\n\n请在微信开发者工具中创建新的云开发环境。';
        break;
      default:
        content = `错误信息: ${error.errMsg || error.message}\n错误代码: ${errorCode}`;
    }
    
    return { title, content };
  }
}

module.exports = CloudEnvChecker;
