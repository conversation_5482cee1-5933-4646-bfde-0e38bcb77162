<!-- 真心话时间页面 -->
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="nav-left"></view>
      <view class="nav-title">💕 真心话时间</view>
      <view class="nav-right">
        <text class="timer">{{timeLeft}}</text>
      </view>
    </view>
  </view>

  <!-- 页面内容 -->
  <view class="page-content">
    <!-- 环节说明卡片 -->
    <view class="intro-card artistic-card">
      <view class="section-header">
        <text class="section-icon">💝</text>
        <text class="section-title">真心话环节</text>
      </view>
      <view class="intro-content">
        <text class="intro-text">在这个特殊的环节中，每位玩家都需要诚实回答一个问题。这不仅能增进彼此了解，也可能揭露重要线索。请保持真诚，这对破案很重要！</text>
      </view>
    </view>

    <!-- 当前问题卡片 -->
    <view class="question-card artistic-card">
      <view class="section-header">
        <text class="section-icon">❓</text>
        <text class="section-title">当前问题</text>
      </view>
      <view class="question-content">
        <view class="current-player">
          <view class="player-avatar">{{currentPlayer.avatar}}</view>
          <view class="player-info">
            <view class="player-name">{{currentPlayer.name}}</view>
            <view class="player-role">{{currentPlayer.role}}</view>
          </view>
        </view>
        <view class="question-text">{{currentQuestion}}</view>
      </view>
    </view>

    <!-- 回答区域 -->
    <view class="answer-area artistic-card" wx:if="{{isMyTurn}}">
      <view class="section-header">
        <text class="section-icon">💬</text>
        <text class="section-title">你的回答</text>
      </view>
      <view class="answer-input">
        <textarea
          class="answer-field"
          placeholder="请诚实回答这个问题..."
          value="{{answerText}}"
          bindinput="onAnswerInput"
          maxlength="200"
          auto-height
        ></textarea>
        <view class="answer-counter">{{answerText.length}}/200</view>
      </view>
    </view>

    <!-- 回答历史 -->
    <view class="history-card artistic-card">
      <view class="section-header">
        <text class="section-icon">📝</text>
        <text class="section-title">回答记录</text>
      </view>
      <view class="answer-history">
        <view class="history-item" wx:for="{{answerHistory}}" wx:key="index">
          <view class="history-player">
            <view class="history-avatar">{{item.player.avatar}}</view>
            <view class="history-name">{{item.player.name}}</view>
          </view>
          <view class="history-qa">
            <view class="history-question">Q: {{item.question}}</view>
            <view class="history-answer">A: {{item.answer}}</view>
          </view>
        </view>
        <view class="no-history" wx:if="{{answerHistory.length === 0}}">
          暂无回答记录
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <button class="action-btn secondary" bindtap="skipQuestion" wx:if="{{isMyTurn}}">
      <text class="btn-icon">⏭️</text>
      <text>跳过问题</text>
    </button>
    <button
      class="action-btn primary"
      bindtap="{{isMyTurn ? 'submitAnswer' : 'nextPlayer'}}"
      disabled="{{isMyTurn && !answerText.trim()}}"
    >
      <text class="btn-icon">{{isMyTurn ? '✅' : '👉'}}</text>
      <text>{{isMyTurn ? '提交回答' : '下一位'}}</text>
    </button>
  </view>
</view>