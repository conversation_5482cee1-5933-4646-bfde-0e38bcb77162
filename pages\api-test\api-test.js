// pages/api-test/api-test.js
const AIService = require('../../utils/ai-service');
const apiConfig = require('../../config/api-config');
const AccountChecker = require('../../utils/account-checker');

Page({
  data: {
    testResult: '',
    isLoading: false,
    apiKeyStatus: '',
    connectionStatus: 'unknown' // unknown, success, failed
  },

  onLoad() {
    this.checkApiKeyStatus();
  },

  /**
   * 检查API密钥状态
   */
  checkApiKeyStatus() {
    const config = apiConfig.getCurrentConfig();
    const validation = apiConfig.validateApiKey(config.apiKey);
    
    this.setData({
      apiKeyStatus: validation.valid ? '已配置' : '未配置',
      connectionStatus: validation.valid ? 'unknown' : 'failed'
    });
  },

  /**
   * 测试简单API调用
   */
  async testSimpleApi() {
    this.setData({ 
      isLoading: true, 
      testResult: '正在测试连接...',
      connectionStatus: 'unknown'
    });

    try {
      const aiService = new AIService();
      const result = await aiService.generateStoryPrompt({
        theme: '测试',
        playerCount: 3,
        difficulty: 'easy'
      });

      this.setData({
        testResult: `✅ API连接成功！\n\n生成的测试内容：\n${JSON.stringify(result, null, 2)}`,
        connectionStatus: 'success'
      });

      wx.showToast({
        title: 'API连接成功',
        icon: 'success'
      });

    } catch (error) {
      console.error('API测试失败:', error);
      
      let errorMessage = '❌ API连接失败\n\n';
      if (error.message) {
        errorMessage += `错误信息: ${error.message}\n`;
      }
      if (error.statusCode) {
        errorMessage += `状态码: ${error.statusCode}\n`;
      }
      if (error.data) {
        errorMessage += `详细信息: ${JSON.stringify(error.data, null, 2)}`;
      }

      this.setData({
        testResult: errorMessage,
        connectionStatus: 'failed'
      });

      wx.showToast({
        title: 'API连接失败',
        icon: 'error'
      });
    } finally {
      this.setData({ isLoading: false });
    }
  },

  /**
   * 测试网络连接
   */
  async testNetworkConnection() {
    this.setData({ 
      isLoading: true, 
      testResult: '正在测试网络连接...',
      connectionStatus: 'unknown'
    });

    try {
      const config = apiConfig.getCurrentConfig();
      
      const result = await new Promise((resolve, reject) => {
        wx.request({
          url: `${config.baseUrl}/chat/completions`,
          method: 'POST',
          header: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${config.apiKey}`
          },
          data: {
            model: config.model,
            messages: [
              { role: 'user', content: 'Hello, this is a connection test.' }
            ],
            max_tokens: 10
          },
          timeout: 15000,
          success: (res) => {
            if (res.statusCode === 200) {
              resolve({
                success: true,
                data: res.data,
                statusCode: res.statusCode
              });
            } else {
              resolve({
                success: false,
                statusCode: res.statusCode,
                data: res.data
              });
            }
          },
          fail: (error) => {
            reject(error);
          }
        });
      });

      if (result.success) {
        this.setData({
          testResult: `✅ 网络连接成功！\n\n状态码: ${result.statusCode}\n响应数据: ${JSON.stringify(result.data, null, 2)}`,
          connectionStatus: 'success'
        });
      } else {
        this.setData({
          testResult: `❌ 网络连接失败\n\n状态码: ${result.statusCode}\n错误信息: ${JSON.stringify(result.data, null, 2)}`,
          connectionStatus: 'failed'
        });
      }

    } catch (error) {
      console.error('网络测试失败:', error);
      
      this.setData({
        testResult: `❌ 网络连接失败\n\n错误信息: ${error.errMsg || error.message}`,
        connectionStatus: 'failed'
      });
    } finally {
      this.setData({ isLoading: false });
    }
  },

  /**
   * 清除测试结果
   */
  clearResult() {
    this.setData({
      testResult: '',
      connectionStatus: 'unknown'
    });
  },

  /**
   * 复制测试结果
   */
  copyResult() {
    if (!this.data.testResult) {
      wx.showToast({
        title: '没有可复制的内容',
        icon: 'none'
      });
      return;
    }

    wx.setClipboardData({
      data: this.data.testResult,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });
      }
    });
  },

  /**
   * 前往API设置页面
   */
  goToApiSettings() {
    wx.navigateTo({
      url: '/pages/api-settings/api-settings'
    });
  }
});
