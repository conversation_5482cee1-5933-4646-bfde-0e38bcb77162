var THRESHOLD = 0.3;
var MIN_DISTANCE = 10;
var owner;
var state;

var getState = function (ownerInstance) {
  owner = ownerInstance;
  state = owner.getState();
  state.leftWidth = state.leftWidth || 0;
  state.rightWidth = state.rightWidth || 0;
  state.offset = state.offset || 0;
  state.startOffset = state.startOffset || 0;
  state.opened = state.opened || false;
};

var initRightWidth = function (newVal, oldVal, ownerInstance) {
  getState(ownerInstance);
  state.rightWidth = newVal;
  initOpen(ownerInstance);
};

var initLeftWidth = function (newVal, oldVal, ownerInstance) {
  getState(ownerInstance);
  state.leftWidth = newVal;
  initOpen(ownerInstance);
};

var initOpen = function (ownerInstance) {
  getState(ownerInstance);
  if (state.opened.constructor === 'Boolean') {
    // opened为boolean类型，判断默认打开
    if (state.opened && state.rightWidth > 0) {
      swipeMove(-state.rightWidth);
    } else if (state.opened && state.leftWidth > 0) {
      swipeMove(state.leftWidth);
    }
  }

  if (state.opened.constructor === 'Array') {
    // opened为array类型，判断默认打开，同时设定左右action时优先打开右边
    if (state.opened[1] && state.rightWidth > 0) {
      swipeMove(-state.rightWidth);
    } else if (state.opened[0] && state.leftWidth > 0) {
      swipeMove(state.leftWidth);
    }
  }
};

var resetTouchStatus = function () {
  state.direction = '';
  state.deltaX = 0;
  state.deltaY = 0;
  state.offsetX = 0;
  state.offsetY = 0;
};

var touchMove = function (event) {
  var touchPoint = event.touches[0];
  state.deltaX = touchPoint.clientX - state.startX;
  state.deltaY = touchPoint.clientY - state.startY;
  state.offsetX = Math.abs(state.deltaX);
  state.offsetY = Math.abs(state.deltaY);
  state.direction = state.direction || getDirection(state.offsetX, state.offsetY);
};

var getDirection = function (x, y) {
  if (x > y && x > MIN_DISTANCE) {
    return 'horizontal';
  }
  if (y > x && y > MIN_DISTANCE) {
    return 'vertical';
  }
  return '';
};

var range = function (num, min, max) {
  return Math.min(Math.max(num, min), max);
};

var swipeMove = function (_offset) {
  if (_offset === undefined) _offset = 0;
  state.offset = range(_offset, -state.rightWidth, +state.leftWidth);
  var transform = 'translate3d(' + state.offset + 'px, 0, 0)';
  var transition = state.dragging ? 'none' : 'transform .6s cubic-bezier(0.18, 0.89, 0.32, 1)';
  owner.selectComponent('#wrapper').setStyle({
    '-webkit-transform': transform,
    '-webkit-transition': transition,
    transform: transform,
    transition: transition,
  });
};

var close = function () {
  swipeMove(0);
};

var onCloseChange = function (newVal, oldVal, ownerInstance) {
  getState(ownerInstance);
  if (newVal === oldVal) return;
  if (newVal) {
    close();
  }
};

var onOpenedChange = function (newVal, oldVal, ownerInstance) {
  getState(ownerInstance);
  state.opened = newVal;
  if (newVal === oldVal) return;
  if (!newVal) {
    close();
  }
};

var touchStart = function (event) {
  resetTouchStatus();
  state.startOffset = state.offset;
  var touchPoint = event.touches[0];
  state.startX = touchPoint.clientX;
  state.startY = touchPoint.clientY;
  owner.callMethod('closeOther');
};

var startDrag = function (event, ownerInstance) {
  ownerInstance.callMethod('catchMove');
  getState(ownerInstance);
  touchStart(event);
};

var onDrag = function (event, ownerInstance) {
  getState(ownerInstance);
  touchMove(event);
  if (state.direction === 'vertical') {
    ownerInstance.callMethod('skipMove');
  }
  if (state.direction !== 'horizontal') {
    return;
  }
  if (!state.dragging) {
    ownerInstance.triggerEvent('dragstart');
  }
  state.dragging = true;
  swipeMove(state.startOffset + state.deltaX);
  return false;
};

var open = function (position) {
  var _offset = position === 'left' ? +state.leftWidth : -state.rightWidth;
  owner.callMethod('open', { position: position });
  swipeMove(_offset);
};

var endDrag = function (event, ownerInstance) {
  getState(ownerInstance);
  state.dragging = false;
  // 左/右侧有可滑动区域，且当前不是已open状态，且滑动幅度超过阈值时open左/右侧（滚动到该侧的最边上）
  if (
    +state.rightWidth > 0 &&
    -state.startOffset < +state.rightWidth &&
    -state.offset > +state.rightWidth * THRESHOLD
  ) {
    open('right');
  } else if (
    +state.leftWidth > 0 &&
    state.startOffset < +state.leftWidth &&
    state.offset > +state.leftWidth * THRESHOLD
  ) {
    open('left');
  } else {
    // 仅在有发生侧滑的情况下自动关闭（由js控制是否异步关闭）
    if (state.startOffset !== state.offset) {
      close();
    }
  }
  ownerInstance.triggerEvent('dragend');
};

module.exports = {
  initLeftWidth: initLeftWidth,
  initRightWidth: initRightWidth,
  startDrag: startDrag,
  onDrag: onDrag,
  endDrag: endDrag,
  onCloseChange: onCloseChange,
  onOpenedChange: onOpenedChange,
};
