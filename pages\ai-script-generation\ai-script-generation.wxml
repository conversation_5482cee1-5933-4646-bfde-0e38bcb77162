<!--AI剧本生成页面-->
<view class="container">
  <!-- 顶部导航 -->
  <view class="nav-bar">
    <button class="back-btn" bind:tap="goBack" wx:if="{{!generating}}">
      ← 返回
    </button>
    <text class="nav-title">🤖 AI剧本生成</text>
  </view>

  <!-- 生成进度区域 -->
  <view class="generation-area">
    <!-- 进度环 -->
    <view class="progress-circle">
      <view class="progress-ring">
        <view class="progress-fill" style="transform: rotate({{progress * 3.6}}deg);"></view>
      </view>
      <view class="progress-content">
        <text class="progress-number">{{progress}}%</text>
        <text class="progress-label">{{generating ? '生成中' : '已完成'}}</text>
      </view>
    </view>

    <!-- 当前步骤 -->
    <view class="current-step">
      <text class="step-title">{{currentStep}}</text>
      <text class="step-desc" wx:if="{{generating && generationSteps[currentStepIndex]}}">
        {{generationSteps[currentStepIndex].desc}}
      </text>
    </view>
  </view>

  <!-- 生成步骤列表 -->
  <view class="steps-list" wx:if="{{generating}}">
    <view class="steps-title">生成步骤</view>
    <view class="steps-container">
      <view 
        class="step-item {{index <= currentStepIndex ? 'completed' : ''}} {{index === currentStepIndex ? 'active' : ''}}"
        wx:for="{{generationSteps}}" 
        wx:key="id"
      >
        <view class="step-indicator">
          <view class="step-number">{{item.id}}</view>
          <view class="step-status">
            <text wx:if="{{index < currentStepIndex}}">✅</text>
            <text wx:elif="{{index === currentStepIndex}}">🔄</text>
            <text wx:else>⏳</text>
          </view>
        </view>
        <view class="step-content">
          <text class="step-name">{{item.name}}</text>
          <text class="step-description">{{item.desc}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 生成完成后的操作 -->
  <view class="completion-actions" wx:if="{{!generating && scriptData}}">
    <view class="script-preview">
      <text class="preview-title">📖 {{scriptData.storyInfo.title}}</text>
      <text class="preview-desc">{{scriptData.storyInfo.background}}</text>
      <view class="preview-stats">
        <text class="stat-item">👥 {{scriptData.characters.length}}个角色</text>
        <text class="stat-item">🎭 {{scriptData.storyInfo.theme || '悬疑推理'}}</text>
      </view>
    </view>

    <view class="action-buttons">
      <button class="action-btn secondary" bind:tap="viewScriptDetails">
        📋 查看详情
      </button>
      <button class="action-btn secondary" bind:tap="regenerateScript">
        🔄 重新生成
      </button>
      <button class="action-btn primary" bind:tap="goToRoleAssignment">
        🎭 开始分配角色
      </button>
    </view>
  </view>

  <!-- 加载状态的装饰元素 -->
  <view class="decoration-elements" wx:if="{{generating}}">
    <view class="floating-icon icon-1">📝</view>
    <view class="floating-icon icon-2">🎭</view>
    <view class="floating-icon icon-3">🔍</view>
    <view class="floating-icon icon-4">💡</view>
  </view>
</view>
