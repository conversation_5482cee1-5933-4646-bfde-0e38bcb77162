// utils/api-key-updater.js
// API密钥更新工具

const apiConfig = require('../config/api-config');

class ApiKeyUpdater {
  constructor() {
    this.newApiKey = 'sk-rFun7AywY7jUUdJAtUBbFDxhaVTI5okdFpL3mSSeLfOmKCmP';
  }

  /**
   * 更新API密钥
   */
  updateApiKey() {
    try {
      console.log('🔄 开始更新API密钥...');
      
      // 1. 清除旧的本地存储
      this.clearOldStorage();
      
      // 2. 设置新的API密钥
      const success = apiConfig.setApiKey(this.newApiKey);
      
      if (success) {
        console.log('✅ API密钥更新成功');
        
        // 3. 验证新密钥
        this.validateNewKey();
        
        return {
          success: true,
          message: 'API密钥已成功更新'
        };
      } else {
        throw new Error('保存新密钥失败');
      }
    } catch (error) {
      console.error('❌ API密钥更新失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 清除旧的本地存储
   */
  clearOldStorage() {
    try {
      // 清除可能存在的旧密钥
      wx.removeStorageSync('moonshot_api_key');
      wx.removeStorageSync('api_key'); // 可能的旧存储键
      wx.removeStorageSync('openai_api_key'); // 可能的旧存储键
      
      console.log('🧹 已清除旧的本地存储');
    } catch (error) {
      console.warn('清除本地存储时出错:', error);
    }
  }

  /**
   * 验证新密钥
   */
  validateNewKey() {
    const config = apiConfig.getCurrentConfig();
    const validation = apiConfig.validateApiKey(config.apiKey);
    
    console.log('🔍 密钥验证结果:', validation);
    
    if (validation.valid) {
      console.log('✅ 新密钥格式正确');
      console.log('🔑 当前使用的密钥:', apiConfig.getSafeKeyDisplay(config.apiKey));
    } else {
      console.error('❌ 新密钥验证失败:', validation.message);
    }
    
    return validation;
  }

  /**
   * 测试API连接
   */
  async testApiConnection() {
    console.log('🧪 开始测试API连接...');
    
    try {
      const result = await new Promise((resolve, reject) => {
        const config = apiConfig.getCurrentConfig();
        
        wx.request({
          url: 'https://api.moonshot.cn/v1/chat/completions',
          method: 'POST',
          header: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${config.apiKey}`
          },
          data: {
            model: 'kimi-k2-0711-preview',
            messages: [
              { role: 'user', content: '你好，这是一个连接测试。请简单回复"连接成功"。' }
            ],
            max_tokens: 50
          },
          timeout: 15000,
          success: (res) => {
            console.log('📡 API响应:', res);
            if (res.statusCode === 200) {
              resolve({
                success: true,
                message: '连接测试成功',
                response: res.data
              });
            } else {
              reject({
                success: false,
                statusCode: res.statusCode,
                error: res.data
              });
            }
          },
          fail: (error) => {
            console.error('📡 API请求失败:', error);
            reject({
              success: false,
              error: error
            });
          }
        });
      });
      
      console.log('✅ API连接测试成功:', result);
      return result;
      
    } catch (error) {
      console.error('❌ API连接测试失败:', error);
      
      // 分析错误原因
      if (error.statusCode === 401) {
        return {
          success: false,
          error: 'API密钥无效或已被禁用',
          suggestions: [
            '1. 检查密钥是否正确复制',
            '2. 确认密钥在Moonshot AI控制台中有效',
            '3. 检查账户余额是否充足',
            '4. 确认密钥没有被自动禁用'
          ]
        };
      } else if (error.statusCode === 402) {
        return {
          success: false,
          error: '账户余额不足',
          suggestions: [
            '1. 前往Moonshot AI控制台充值',
            '2. 检查账户余额状态'
          ]
        };
      } else {
        return {
          success: false,
          error: error.error || '网络连接失败',
          suggestions: [
            '1. 检查网络连接',
            '2. 确认API服务可用',
            '3. 稍后重试'
          ]
        };
      }
    }
  }

  /**
   * 完整的密钥更新和测试流程
   */
  async performFullUpdate() {
    console.log('🚀 开始完整的API密钥更新流程...');
    
    // 1. 更新密钥
    const updateResult = this.updateApiKey();
    if (!updateResult.success) {
      return updateResult;
    }
    
    // 2. 测试连接
    const testResult = await this.testApiConnection();
    
    return {
      updateResult,
      testResult,
      overall: testResult.success ? 'success' : 'failed'
    };
  }
}

module.exports = ApiKeyUpdater;
