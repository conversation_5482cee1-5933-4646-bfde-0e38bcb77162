// AI服务类 - 处理所有AI相关功能
// 备份版本 - 如果加载失败，请使用简化版本
const errorHandler = require('./error-handler');
const AIPrompts = require('./ai-prompts');
const NetworkDiagnostic = require('./network-diagnostic');
const apiConfig = require('../config/api-config');
const MockAIService = require('./mock-ai-service');
// const RateLimitHelper = require('./rate-limit-helper'); // 暂时注释

class AIService {
  constructor() {
    // 从安全配置获取API设置
    const config = apiConfig.getCurrentConfig();
    this.apiKey = config.apiKey;
    this.baseUrl = config.baseUrl;
    this.model = config.model;

    // 初始化模拟服务作为回退
    this.mockService = new MockAIService();
    this.useMockService = false;

    // 初始化频率限制帮助工具
    // this.rateLimitHelper = new RateLimitHelper(); // 暂时注释

    // 请求配置
    this.requestConfig = {
      timeout: 120000, // 增加到120秒超时（AI生成需要更长时间）
      maxRetries: 2, // 减少重试次数，避免过长等待
      retryDelay: 3000 // 增加重试延迟到3秒
    };

    // 请求频率控制
    this.lastRequestTime = 0;
    this.minRequestInterval = 2000; // 最小请求间隔2秒
    this.requestQueue = [];
    this.isProcessingQueue = false;
  }

  /**
   * 发送请求到Moonshot AI API
   * @param {Array} messages - 消息数组
   * @param {Object} options - 可选参数
   * @returns {Promise} API响应
   */
  async sendRequest(messages, options = {}) {
    const requestData = {
      model: this.model,
      messages: messages,
      temperature: options.temperature || 0.3, // 降低temperature提高稳定性
      max_tokens: options.maxTokens || 1200, // 进一步减少token数量
      top_p: options.topP || 0.9,
      stream: false // 确保不使用流式响应
    };

    try {
      const response = await this.makeHttpRequest('/chat/completions', requestData);
      return response.choices[0].message.content;
    } catch (error) {
      console.error('AI API请求失败:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * 请求频率控制 - 确保请求间隔
   */
  async waitForRateLimit() {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    if (timeSinceLastRequest < this.minRequestInterval) {
      const waitTime = this.minRequestInterval - timeSinceLastRequest;
      console.log(`⏱️ 请求频率控制，等待 ${waitTime}ms...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    this.lastRequestTime = Date.now();
  }

  /**
   * 发送HTTP请求（带重试机制和频率控制）
   * @param {string} endpoint - API端点
   * @param {Object} data - 请求数据
   * @param {number} retryCount - 当前重试次数
   * @returns {Promise} 响应数据
   */
  async makeHttpRequest(endpoint, data, retryCount = 0) {
    // 应用请求频率控制
    await this.waitForRateLimit();
    return new Promise((resolve, reject) => {
      console.log(`发送AI请求 (尝试 ${retryCount + 1}/${this.requestConfig.maxRetries + 1}):`, {
        url: `${this.baseUrl}${endpoint}`,
        model: data.model,
        messagesCount: data.messages?.length
      });

      wx.request({
        url: `${this.baseUrl}${endpoint}`,
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        data: data,
        timeout: this.requestConfig.timeout,
        success: (res) => {
          console.log('AI请求响应:', {
            statusCode: res.statusCode,
            hasData: !!res.data,
            hasChoices: !!res.data?.choices
          });

          if (res.statusCode === 200 && res.data) {
            resolve(res.data);
          } else if (res.statusCode === 429) {
            // 处理频率限制错误
            const errorMsg = '请求频率过高，请稍后再试';
            console.warn('⚠️ API频率限制:', errorMsg);
            // this.rateLimitHelper.recordRateLimit(); // 记录频率限制 - 暂时注释
            reject(new Error(`API请求失败: ${errorMsg}`));
          } else {
            const errorMsg = res.data?.error?.message || `HTTP ${res.statusCode}`;
            reject(new Error(`API请求失败: ${errorMsg}`));
          }
        },
        fail: (error) => {
          console.error('AI请求失败:', error);

          // 检查是否是429错误
          const isRateLimit = error.errMsg && error.errMsg.includes('429');
          const retryDelay = isRateLimit ? 10000 : this.requestConfig.retryDelay; // 429错误等待10秒

          // 如果还有重试次数，则重试
          if (retryCount < this.requestConfig.maxRetries) {
            if (isRateLimit) {
              console.log(`⚠️ 遇到频率限制，${retryDelay}ms后重试...`);
            } else {
              console.log(`${retryDelay}ms后重试...`);
            }
            setTimeout(() => {
              this.makeHttpRequest(endpoint, data, retryCount + 1)
                .then(resolve)
                .catch(reject);
            }, retryDelay);
          } else {
            if (isRateLimit) {
              reject(new Error('请求频率过高，请稍后再试'));
            } else {
              reject(new Error(`网络请求失败: ${error.errMsg || '连接超时'}`));
            }
          }
        }
      });
    });
  }

  /**
   * 测试API连接
   * @returns {Promise<boolean>} 连接是否成功
   */
  async testConnection() {
    try {
      // 检查是否处于频率限制状态
      const now = Date.now();
      const timeSinceLastRequest = now - this.lastRequestTime;

      if (timeSinceLastRequest < 120000) { // 如果距离上次请求不到2分钟
        console.log('⚠️ 检测到可能的频率限制，跳过API测试');
        throw new Error('API频率限制中，请等待2分钟后再试');
      }

      const testMessages = [
        { role: 'user', content: '测试连接，请回复"连接成功"' }
      ];

      const response = await this.sendRequest(testMessages, {
        temperature: 0.1,
        maxTokens: 50
      });

      console.log('API连接测试成功:', response);
      return true;
    } catch (error) {
      console.error('API连接测试失败:', error);

      // 如果是频率限制错误，提供更友好的提示
      if (error.message.includes('频率') || error.message.includes('429')) {
        throw new Error('API请求频率过高，请等待2分钟后重试');
      }

      return false;
    }
  }

  /**
   * 运行网络诊断
   * @returns {Promise<Object>} 诊断结果
   */
  async runNetworkDiagnostic() {
    const diagnostic = new NetworkDiagnostic();
    const results = await diagnostic.runFullDiagnostic();
    diagnostic.printDiagnosticReport(results);
    return results;
  }

  /**
   * 处理API错误
   * @param {Error} error - 原始错误
   * @returns {Error} 处理后的错误
   */
  handleApiError(error) {
    console.error('处理API错误:', error);

    const errorMsg = error.message || '';

    if (errorMsg.includes('网络') || errorMsg.includes('连接') || errorMsg.includes('timeout')) {
      return new Error('网络连接失败，请检查网络设置');
    } else if (errorMsg.includes('401') || errorMsg.includes('认证') || errorMsg.includes('Invalid Authentication')) {
      if (errorMsg.includes('Invalid Authentication')) {
        return new Error('API密钥无效或已被禁用 - 请前往控制台检查密钥状态和账户余额');
      } else {
        return new Error('AI服务认证失败，请检查API密钥和账户余额');
      }
    } else if (errorMsg.includes('402') || errorMsg.includes('余额不足')) {
      return new Error('账户余额不足 - 请前往Moonshot AI控制台充值');
    } else if (errorMsg.includes('429') || errorMsg.includes('频繁')) {
      return new Error('AI服务请求过于频繁，请稍后重试');
    } else if (errorMsg.includes('500') || errorMsg.includes('502') || errorMsg.includes('503')) {
      return new Error('AI服务暂时不可用，请稍后重试');
    } else if (errorMsg.includes('400')) {
      return new Error('请求参数错误，请检查输入内容');
    } else {
      return new Error(`AI服务异常: ${errorMsg}`);
    }
  }

  /**
   * 检查是否应该使用模拟服务
   */
  shouldUseMockService() {
    // 如果API密钥无效或者是默认值，使用模拟服务
    const validation = apiConfig.validateApiKey(this.apiKey);

    // 临时启用模拟服务，直到API问题解决
    if (!validation.valid || this.useMockService) {
      console.log('🎭 使用模拟AI服务 - 原因:', validation.valid ? '手动启用' : validation.message);
      return true;
    }

    return false;
  }

  /**
   * 生成故事背景和设定（兼容旧接口）
   * @param {Object} params - 生成参数
   * @returns {Promise<Object>} 生成的故事内容
   */
  async generateStoryPrompt(params) {
    return await this.generateScript(params);
  }

  /**
   * 生成剧本故事
   * @param {Object} params - 生成参数
   * @returns {Promise<Object>} 生成的剧本内容
   */
  async generateScript(params) {
    // 如果应该使用模拟服务，直接返回模拟结果
    if (this.shouldUseMockService()) {
      console.log('使用模拟AI服务生成故事');
      return await this.mockService.generateStoryPrompt(params);
    }
    const {
      storyType = 'mystery',
      playerCount = 6,
      difficulty = 'medium',
      theme = '现代都市',
      specialRequirements = '',
      gameSettings = {}
    } = params;

    const systemPrompt = AIPrompts.getSystemPrompt();
    const userPrompt = AIPrompts.getScriptGenerationPrompt(params);

    const messages = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt }
    ];

    try {
      const response = await this.sendRequest(messages, {
        temperature: 0.8,
        maxTokens: 3000
      });

      // 预处理响应内容
      const processedResponse = this.preprocessAIResponse(response);
      return this.parseScriptResponse(processedResponse, playerCount);
    } catch (error) {
      console.log('AI服务失败，切换到模拟服务:', error.message);
      this.useMockService = true;
      return await this.mockService.generateStoryPrompt(params);
    }
  }

  /**
   * 优化剧本内容
   * @param {Object} originalStory - 原始剧本
   * @param {string} feedback - 优化反馈
   * @returns {Promise<Object>} 优化后的剧本
   */
  async optimizeScript(originalStory, feedback) {
    const systemPrompt = AIPrompts.getSystemPrompt();
    const userPrompt = AIPrompts.getStoryOptimizationPrompt(originalStory, feedback);

    const messages = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt }
    ];

    try {
      const response = await this.sendRequest(messages, {
        temperature: 0.7,
        maxTokens: 3000
      });

      // 预处理响应内容
      const processedResponse = this.preprocessAIResponse(response);
      // 从原始剧本中获取角色数量作为期望值
      const expectedPlayerCount = originalStory.characters ? originalStory.characters.length : null;
      return this.parseScriptResponse(processedResponse, expectedPlayerCount);
    } catch (error) {
      console.error('剧本优化失败:', error);
      throw error;
    }
  }

  /**
   * 生成游戏中的动态内容
   * @param {string} contentType - 内容类型
   * @param {Object} context - 上下文信息
   * @returns {Promise<Object>} 生成的内容
   */
  async generateDynamicContent(contentType, context) {
    let systemPrompt = AIPrompts.getSystemPrompt();
    let userPrompt = '';

    switch (contentType) {
      case 'truthQuestions':
        userPrompt = AIPrompts.getTruthQuestionsPrompt(context);
        break;
      case 'roleAssignment':
        userPrompt = AIPrompts.getRoleAssignmentPrompt(context.scriptData, context.playerIds);
        break;
      default:
        throw new Error(`不支持的内容类型: ${contentType}`);
    }

    const messages = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt }
    ];

    try {
      const response = await this.sendRequest(messages, {
        temperature: 0.7,
        maxTokens: 1500
      });

      return this.parseJsonResponse(response);
    } catch (error) {
      console.error(`生成${contentType}失败:`, error);
      throw error;
    }
  }

  /**
   * 预处理AI响应，提取有效内容
   * @param {Object} response - AI API响应对象
   * @returns {string} 处理后的响应文本
   */
  preprocessAIResponse(response) {
    try {
      // 从API响应中提取文本内容
      let content = '';

      if (typeof response === 'string') {
        content = response;
      } else if (response && response.choices && response.choices.length > 0) {
        content = response.choices[0].message.content;
      } else if (response && response.content) {
        content = response.content;
      } else {
        console.warn('⚠️ 无法从响应中提取内容:', response);
        return '';
      }

      console.log('📝 原始AI响应长度:', content.length);
      console.log('📝 响应开头:', content.substring(0, 200));

      // 移除可能的前缀说明文字
      content = content.replace(/^[^{]*(?=\{)/, '');

      // 移除可能的后缀说明文字
      content = content.replace(/\}[^}]*$/, '}');

      console.log('🔧 预处理后长度:', content.length);

      return content;

    } catch (error) {
      console.error('❌ 预处理AI响应失败:', error);
      return response;
    }
  }

  /**
   * 解析剧本生成响应
   * @param {string} response - AI响应内容
   * @param {number} expectedPlayerCount - 期望的玩家数量
   * @returns {Object} 解析后的剧本数据
   */
  parseScriptResponse(response, expectedPlayerCount = null) {
    try {
      console.log('🔍 开始解析AI响应...');
      console.log('响应长度:', response.length);

      // 🚨 关键修复：超强力预处理，移除所有可能导致JSON解析失败的内容
      console.log('🔧 执行超强力预处理...');
      response = response
        // 移除所有类型的注释（这是导致间歇性失败的主要原因！）
        .replace(/\/\/.*$/gm, '')  // 单行注释
        .replace(/\/\*[\s\S]*?\*\//g, '')  // 多行注释
        .replace(/^\s*\/\/.*$/gm, '')  // 行首注释
        .replace(/\s+\/\/.*$/gm, '')  // 行尾注释
        // 🚨 超强力反斜杠清理 - 这是导致position 18错误的主要原因
        .replace(/\\(?!["\\/bfnrtu])/g, '')  // 移除所有无效的反斜杠转义
        .replace(/\\\\/g, '\\')  // 修复双反斜杠
        .replace(/\\b/g, '')  // 退格字符
        .replace(/\\f/g, '')  // 换页字符
        .replace(/\\v/g, '')  // 垂直制表符
        .replace(/\\0/g, '')  // 空字符
        .replace(/\\a/g, '')  // 响铃字符
        .replace(/\\e/g, '')  // 转义字符
        .replace(/\\\s/g, ' ')  // 反斜杠+空格
        .replace(/\\$/gm, '')  // 行尾反斜杠
        // 额外的安全清理
        .replace(/[\x00-\x1F\x7F]/g, '')  // 移除控制字符
        .replace(/\uFEFF/g, '');  // 移除BOM字符

      console.log('预处理后长度:', response.length);

      // 首先尝试提取JSON代码块
      const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        console.log('✅ 找到JSON代码块');
        const jsonStr = this.superCleanJsonString(jsonMatch[1].trim());
        console.log('JSON字符串长度:', jsonStr.length);
        console.log('JSON开头:', jsonStr.substring(0, 100));
        const parsedData = JSON.parse(jsonStr);
        return this.validateAndFixCharacterCount(parsedData, expectedPlayerCount);
      }

      // 尝试查找其他格式的代码块
      const codeMatch = response.match(/```\s*([\s\S]*?)\s*```/);
      if (codeMatch) {
        console.log('✅ 找到代码块（非JSON）');
        const codeStr = this.superCleanJsonString(codeMatch[1].trim());
        // 尝试解析为JSON
        if (codeStr.startsWith('{') || codeStr.startsWith('[')) {
          const parsedData = JSON.parse(codeStr);
          return this.validateAndFixCharacterCount(parsedData, expectedPlayerCount);
        }
      }

      // 尝试查找JSON对象（以{开头，以}结尾）
      const jsonObjectMatch = response.match(/\{[\s\S]*\}/);
      if (jsonObjectMatch) {
        console.log('✅ 找到JSON对象');
        const jsonStr = jsonObjectMatch[0];
        const parsedData = JSON.parse(jsonStr);
        return this.validateAndFixCharacterCount(parsedData, expectedPlayerCount);
      }

      // 如果都没找到，尝试直接解析整个响应
      console.log('⚠️ 未找到明确的JSON格式，尝试直接解析');
      const parsedData = JSON.parse(response.trim());
      return this.validateAndFixCharacterCount(parsedData, expectedPlayerCount);

    } catch (error) {
      console.error('❌ 解析AI响应失败:', error);
      console.log('错误位置:', error.message);
      console.log('错误类型:', error.name);
      console.log('响应内容预览:', response.substring(0, 500));

      // 🔍 执行详细的JSON错误诊断
      this.diagnoseJsonError(error, response);

      // 详细分析错误位置
      if (error.message.includes('position')) {
        const position = error.message.match(/position (\d+)/);
        if (position) {
          const pos = parseInt(position[1]);
          console.log('错误位置字符:', response.charAt(pos));
          console.log('错误位置前后文:', response.substring(Math.max(0, pos - 20), pos + 20));
        }
      }

      // 尝试修复常见的JSON错误
      const fixedResponse = this.tryFixJsonResponse(response);
      if (fixedResponse) {
        try {
          console.log('🔧 尝试修复后的JSON解析...');
          const result = JSON.parse(fixedResponse);
          console.log('✅ 修复成功！');
          return this.validateAndFixCharacterCount(result, expectedPlayerCount);
        } catch (fixError) {
          console.error('修复后仍然失败:', fixError);
          console.log('修复后的内容预览:', fixedResponse.substring(0, 200));
        }
      }

      // 最后尝试：逐步清理和解析
      try {
        console.log('🔄 尝试逐步清理解析...');
        const stepByStepFixed = this.stepByStepJsonFix(response);
        if (stepByStepFixed) {
          const result = JSON.parse(stepByStepFixed);
          console.log('✅ 逐步修复成功！');
          return this.validateAndFixCharacterCount(result, expectedPlayerCount);
        }
      } catch (stepError) {
        console.error('逐步修复也失败:', stepError);

        // 终极修复：简单粗暴的字符替换
        try {
          console.log('🚨 尝试终极修复...');
          const bruteForceFixed = this.bruteForceJsonFix(response);
          if (bruteForceFixed) {
            const result = JSON.parse(bruteForceFixed);
            console.log('✅ 终极修复成功！');
            return this.validateAndFixCharacterCount(result, expectedPlayerCount);
          }
        } catch (bruteError) {
          console.error('终极修复也失败:', bruteError);

          // 最后的最后：超级修复
          try {
            console.log('💥 尝试超级修复...');
            const superFixed = this.superJsonFix(response);
            if (superFixed) {
              const result = JSON.parse(superFixed);
              console.log('✅ 超级修复成功！');
              return this.validateAndFixCharacterCount(result, expectedPlayerCount);
            }
          } catch (superError) {
            console.error('超级修复也失败:', superError);
          }
        }
      }

      // 如果所有修复尝试都失败，切换到模拟服务
      console.error('🚨 所有JSON解析尝试都失败，切换到模拟服务');
      console.log('🎭 启用模拟服务作为最后的备选方案');
      this.useMockService = true;

      // 返回模拟服务的结果
      try {
        return await this.mockService.generateStoryPrompt({
          storyType: 'mystery',
          playerCount: expectedPlayerCount || 6,
          difficulty: 'medium',
          theme: '现代都市'
        });
      } catch (mockError) {
        console.error('模拟服务也失败了:', mockError);
        // 最后的备选方案
        return this.getDefaultScriptStructure();
      }
    }
  }

  /**
   * 验证并修复角色数量
   * @param {Object} scriptData - 解析后的剧本数据
   * @param {number} expectedPlayerCount - 期望的玩家数量
   * @returns {Object} 修复后的剧本数据
   */
  validateAndFixCharacterCount(scriptData, expectedPlayerCount) {
    if (!expectedPlayerCount || !scriptData.characters) {
      return scriptData;
    }

    const currentCount = scriptData.characters.length;
    console.log(`🎭 角色数量检查: 期望${expectedPlayerCount}个，实际${currentCount}个`);

    if (currentCount === expectedPlayerCount) {
      console.log('✅ 角色数量正确');
      return scriptData;
    }

    if (currentCount < expectedPlayerCount) {
      console.log(`⚠️ 角色数量不足，需要补充${expectedPlayerCount - currentCount}个角色`);
      // 补充角色
      for (let i = currentCount; i < expectedPlayerCount; i++) {
        const newCharacter = this.generateAdditionalCharacter(i + 1, scriptData);
        scriptData.characters.push(newCharacter);
      }
      console.log(`✅ 已补充角色，当前数量: ${scriptData.characters.length}`);
    } else if (currentCount > expectedPlayerCount) {
      console.log(`⚠️ 角色数量过多，需要移除${currentCount - expectedPlayerCount}个角色`);
      // 移除多余角色（保留前N个）
      scriptData.characters = scriptData.characters.slice(0, expectedPlayerCount);
      console.log(`✅ 已移除多余角色，当前数量: ${scriptData.characters.length}`);
    }

    return scriptData;
  }

  /**
   * 生成额外的角色
   * @param {number} index - 角色索引
   * @param {Object} scriptData - 现有剧本数据
   * @returns {Object} 新角色数据
   */
  generateAdditionalCharacter(index, scriptData) {
    const characterNames = ['李明', '王芳', '张伟', '刘丽', '陈强', '赵敏', '孙涛', '周雪'];
    const characterTitles = ['律师', '医生', '教师', '商人', '记者', '艺术家', '工程师', '设计师'];
    const factions = ['好人', '坏人', '中立'];

    // 创意预览模板
    const creativeTaglines = [
      '隐藏身份的神秘人物',
      '掌握关键信息的知情者',
      '身怀绝技的专业人士',
      '有着复杂过去的局外人',
      '看似无害的危险角色',
      '关键时刻的意外变数',
      '深藏不露的智者',
      '命运交织的关键人物'
    ];

    const intrigueQuestions = [
      '为什么总是在关键时刻保持沉默？',
      '那个神秘的电话是谁打来的？',
      '为什么对某些细节了如指掌？',
      '隐藏的真实身份是什么？',
      '那个不为人知的秘密是什么？',
      '为什么总是避开某些话题？',
      '那个古老的物件有什么含义？',
      '为什么对这里如此熟悉？'
    ];

    const uniqueTraits = [
      '左手戴着一枚古老的戒指',
      '说话时总是避免直视他人',
      '随身携带一本神秘的笔记本',
      '对某种香水味道异常敏感',
      '习惯性地触摸胸前的吊坠',
      '总是在特定时间查看手表',
      '对古典音乐有着超乎寻常的了解',
      '手上有一道神秘的疤痕'
    ];

    const mysteriousHints = [
      '似乎对这栋建筑了如指掌',
      '与某个重要人物有着不明关系',
      '掌握着改变一切的关键信息',
      '过去的经历与当前事件息息相关',
      '拥有别人不知道的特殊技能',
      '与案件核心有着微妙的联系',
      '身上藏着足以震惊所有人的秘密',
      '是解开谜团的关键钥匙'
    ];

    const name = characterNames[index % characterNames.length] || `角色${index}`;
    const title = characterTitles[index % characterTitles.length] || '神秘人';

    return {
      id: `character_${index}`,
      name: name,
      title: title,
      age: `${25 + (index * 3)}岁`,
      background: `${name}是一位经验丰富的${title}，在专业领域享有声誉。然而，在光鲜的外表下，隐藏着不为人知的过去和复杂的内心世界。这次聚会对${name}来说意义非凡，因为这里有着与过去紧密相连的秘密。`,
      personality: '表面沉稳内敛，实则心思缜密，善于观察他人的细微变化',
      motivation: '寻找真相的同时保护自己的秘密不被发现',
      faction: factions[index % factions.length],
      objectives: ['生存到最后', '找出真相', '保护重要秘密'],
      relationships: {},
      secrets: ['有一个改变一切的重要秘密', '与案件核心人物有着不为人知的联系'],
      winConditions: ['成功推理出真相', '保护自己不被淘汰', '完成隐藏任务'],
      preview: {
        tagline: creativeTaglines[index % creativeTaglines.length],
        intrigue: intrigueQuestions[index % intrigueQuestions.length],
        uniqueTrait: uniqueTraits[index % uniqueTraits.length],
        mysteriousHint: mysteriousHints[index % mysteriousHints.length]
      }
    };
  }

  /**
   * 超强力清理JSON字符串 - 专门解决反斜杠问题
   * @param {string} jsonStr - 原始JSON字符串
   * @returns {string} 清理后的JSON字符串
   */
  superCleanJsonString(jsonStr) {
    console.log('🧹 执行超强力JSON清理...');

    // 第一步：基础清理
    let cleaned = jsonStr
      .trim()
      // 移除BOM和控制字符
      .replace(/\uFEFF/g, '')
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')  // 保留\n\r\t
      // 🚨 关键：处理所有反斜杠问题
      .replace(/\\(?!["\\/bfnrtu])/g, '')  // 移除无效反斜杠
      .replace(/\\\\/g, '\\')  // 修复双反斜杠
      .replace(/\\\s/g, ' ')  // 反斜杠+空格
      .replace(/\\\n/g, '\n')  // 反斜杠+换行符
      .replace(/\\\r/g, '\r')  // 反斜杠+回车符
      .replace(/\\$/gm, '')  // 行尾反斜杠
      // 移除注释
      .replace(/\/\/.*$/gm, '')
      .replace(/\/\*[\s\S]*?\*\//g, '')
      // 🚨 重要：正确处理换行符 - 不要转义JSON结构中的换行
      .replace(/\n\s*/g, ' ')  // 将换行符替换为空格
      .replace(/\r\s*/g, ' ')  // 将回车符替换为空格
      .replace(/\t/g, ' ');    // 将制表符替换为空格

    console.log('第一步清理完成，长度:', cleaned.length);

    // 第二步：验证并修复JSON结构
    try {
      // 尝试解析，如果失败则进行更深度的修复
      JSON.parse(cleaned);
      console.log('✅ JSON结构验证通过');
      return cleaned;
    } catch (e) {
      console.log('⚠️ JSON结构需要修复:', e.message);
      return this.deepFixJsonStructure(cleaned);
    }
  }

  /**
   * 深度修复JSON结构
   * @param {string} jsonStr - 需要修复的JSON字符串
   * @returns {string} 修复后的JSON字符串
   */
  deepFixJsonStructure(jsonStr) {
    console.log('🔧 执行深度JSON结构修复...');

    let fixed = jsonStr
      // 🚨 首先处理残留的反斜杠问题
      .replace(/\\"/g, '"')  // 处理转义引号
      .replace(/\\[^"\\\/bfnrtu]/g, '')  // 移除无效反斜杠
      // 修复属性名缺少引号
      .replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g, '$1"$2":')
      // 修复单引号
      .replace(/'/g, '"')
      // 修复尾随逗号
      .replace(/,(\s*[}\]])/g, '$1')
      // 修复多余引号
      .replace(/"{2,}/g, '"')
      // 清理多余空格
      .replace(/\s+/g, ' ')
      .trim();

    // 最后验证修复结果
    try {
      JSON.parse(fixed);
      console.log('✅ 深度修复成功');
      return fixed;
    } catch (e) {
      console.log('⚠️ 深度修复仍有问题，尝试最后的清理:', e.message);
      // 最后的尝试：移除所有可能的问题字符
      return fixed.replace(/[^\x20-\x7E\u4e00-\u9fff]/g, '');
    }
  }

  /**
   * 清理JSON字符串，移除常见的格式问题
   * @param {string} jsonStr - 原始JSON字符串
   * @returns {string} 清理后的JSON字符串
   */
  cleanJsonString(jsonStr) {
    try {
      console.log('🧹 开始清理JSON字符串...');
      console.log('原始长度:', jsonStr.length);
      console.log('原始开头:', jsonStr.substring(0, 50));

      let cleaned = jsonStr
        // 第一步：强力移除所有注释（关键修复！）
        .replace(/\/\/.*$/gm, '')  // 移除单行注释
        .replace(/\/\*[\s\S]*?\*\//g, '')  // 移除多行注释
        .replace(/^\s*\/\/.*$/gm, '')  // 移除行首注释
        .replace(/\s+\/\/.*$/gm, '')  // 移除行尾注释
        // 第二步：移除BOM和不可见字符
        .replace(/^\uFEFF/, '')
        .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
        // 第三步：修复常见的引号问题
        .replace(/[""]/g, '"')
        .replace(/['']/g, "'")
        // 第四步：处理反斜杠问题 - 这是关键修复
        .replace(/\\\\/g, '\\')  // 双反斜杠变单反斜杠
        .replace(/\\(?!["\\/bfnrt])/g, '\\\\')  // 非法的单反斜杠转义
        // 第五步：修复字符串内的特殊字符
        .replace(/(?<!\\)"/g, '\\"')  // 未转义的引号
        .replace(/\\"/g, '"')  // 先还原，再重新处理
        .replace(/"([^"\\]*(\\.[^"\\]*)*)"(?=\s*:)/g, '"$1"')  // 确保属性名正确
        .replace(/"([^"\\]*(\\.[^"\\]*)*)"(?!\s*:)/g, '"$1"')  // 确保字符串值正确
        // 第六步：移除多余的空白字符
        .trim();

      console.log('清理后长度:', cleaned.length);
      console.log('清理后开头:', cleaned.substring(0, 50));

      return cleaned;
    } catch (error) {
      console.error('JSON清理过程出错:', error);
      return jsonStr.trim();
    }
  }

  /**
   * 逐步修复JSON字符串
   * @param {string} response - 原始响应
   * @returns {string|null} 修复后的JSON字符串
   */
  stepByStepJsonFix(response) {
    try {
      console.log('🔄 开始逐步修复JSON...');

      // 步骤1: 提取JSON部分
      let jsonStr = response;

      // 尝试多种JSON提取方式
      const extractMethods = [
        // 方法1: 标准JSON代码块
        /```json\s*([\s\S]*?)\s*```/,
        // 方法2: 任意代码块
        /```\s*([\s\S]*?)\s*```/,
        // 方法3: 大括号包围的内容
        /\{[\s\S]*\}/,
        // 方法4: 从第一个{到最后一个}
        /\{[\s\S]*\}$/m
      ];

      for (let i = 0; i < extractMethods.length; i++) {
        const match = response.match(extractMethods[i]);
        if (match) {
          jsonStr = match[1] || match[0];
          console.log(`✅ 提取方法${i + 1}成功`);
          break;
        }
      }

      // 步骤2: 基础清理
      jsonStr = jsonStr.trim();

      // 步骤3: 处理特殊字符
      jsonStr = jsonStr
        // 移除BOM
        .replace(/^\uFEFF/, '')
        // 处理引号
        .replace(/[""]/g, '"')
        .replace(/['']/g, "'")
        // 处理字符串内的换行符（在引号内）
        .replace(/"([^"]*)\n([^"]*)"/g, '"$1\\n$2"')
        .replace(/"([^"]*)\r([^"]*)"/g, '"$1\\r$2"')
        .replace(/"([^"]*)\t([^"]*)"/g, '"$1\\t$2"')
        // 处理反斜杠 - 关键修复
        .replace(/\\b/g, '')  // 移除退格字符
        .replace(/\\f/g, '')  // 移除换页字符
        .replace(/\\v/g, '')  // 移除垂直制表符
        .replace(/\\0/g, '')  // 移除空字符
        .replace(/\\\\/g, '\\')
        .replace(/\\(?!["\\/nrt])/g, '')  // 移除非法反斜杠
        // 处理JSON结构外的换行符（保留格式）
        .replace(/\r\n/g, '\n')
        .replace(/\r/g, '\n');

      // 步骤4: 验证基本结构
      if (!jsonStr.startsWith('{') || !jsonStr.endsWith('}')) {
        console.log('⚠️ JSON结构不完整，尝试修复...');
        if (!jsonStr.startsWith('{')) {
          const firstBrace = jsonStr.indexOf('{');
          if (firstBrace !== -1) {
            jsonStr = jsonStr.substring(firstBrace);
          }
        }
        if (!jsonStr.endsWith('}')) {
          const lastBrace = jsonStr.lastIndexOf('}');
          if (lastBrace !== -1) {
            jsonStr = jsonStr.substring(0, lastBrace + 1);
          }
        }
      }

      // 步骤5: 最终的字符串修复
      jsonStr = this.fixJsonStrings(jsonStr);

      console.log('逐步修复后的JSON预览:', jsonStr.substring(0, 100));
      return jsonStr;

    } catch (error) {
      console.error('逐步修复失败:', error);
      return null;
    }
  }

  /**
   * 超级JSON修复方法 - 处理最复杂的情况
   * @param {string} response - 原始响应
   * @returns {string|null} 修复后的JSON字符串
   */
  superJsonFix(response) {
    try {
      console.log('💥 开始超级JSON修复...');

      // 提取JSON部分
      let jsonStr = response;

      // 尝试提取JSON代码块
      const codeBlockMatch = response.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
      if (codeBlockMatch) {
        jsonStr = codeBlockMatch[1];
      } else {
        // 提取大括号内容
        const braceMatch = response.match(/\{[\s\S]*\}/);
        if (braceMatch) {
          jsonStr = braceMatch[0];
        }
      }

      console.log('提取的JSON原始内容:', jsonStr.substring(0, 200));

      // 超级强力清理
      jsonStr = jsonStr
        .trim()
        // 第零步：强力移除所有类型的注释（这是关键！）
        .replace(/\/\/.*$/gm, '')  // 移除单行注释
        .replace(/\/\*[\s\S]*?\*\//g, '')  // 移除多行注释
        .replace(/^\s*\/\/.*$/gm, '')  // 移除行首注释
        .replace(/\s+\/\/.*$/gm, '')  // 移除行尾注释
        // 第一步：移除所有非法转义字符
        .replace(/\\[^"\\\/nrt]/g, '')  // 移除所有非法转义
        .replace(/\\b/g, '')  // 特别处理退格字符
        .replace(/\\f/g, '')  // 特别处理换页字符
        .replace(/\\v/g, '')  // 特别处理垂直制表符
        .replace(/\\0/g, '')  // 特别处理空字符
        .replace(/\\a/g, '')  // 移除响铃字符
        .replace(/\\e/g, '')  // 移除转义字符
        // 第二步：处理双反斜杠
        .replace(/\\\\/g, '\\')
        // 第三步：修复引号问题
        .replace(/[""]/g, '"')
        .replace(/['']/g, "'")
        // 第四步：处理换行符和制表符（在字符串内）
        .replace(/"([^"]*)\n([^"]*)"/g, '"$1\\n$2"')
        .replace(/"([^"]*)\r([^"]*)"/g, '"$1\\r$2"')
        .replace(/"([^"]*)\t([^"]*)"/g, '"$1\\t$2"')
        // 第五步：修复JSON结构
        .replace(/,(\s*[}\]])/g, '$1')  // 移除尾随逗号
        .replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g, '$1"$2":');  // 修复属性名

      console.log('超级修复后预览:', jsonStr.substring(0, 200));

      // 验证基本结构
      if (!jsonStr.startsWith('{')) {
        const firstBrace = jsonStr.indexOf('{');
        if (firstBrace !== -1) {
          jsonStr = jsonStr.substring(firstBrace);
        }
      }

      if (!jsonStr.endsWith('}')) {
        const lastBrace = jsonStr.lastIndexOf('}');
        if (lastBrace !== -1) {
          jsonStr = jsonStr.substring(0, lastBrace + 1);
        }
      }

      return jsonStr;

    } catch (error) {
      console.error('超级修复失败:', error);
      return null;
    }
  }

  /**
   * 终极JSON修复方法 - 简单粗暴但有效
   * @param {string} response - 原始响应
   * @returns {string|null} 修复后的JSON字符串
   */
  bruteForceJsonFix(response) {
    try {
      console.log('🚨 开始终极JSON修复...');

      // 提取JSON部分
      let jsonStr = response;

      // 尝试提取JSON代码块
      const codeBlockMatch = response.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
      if (codeBlockMatch) {
        jsonStr = codeBlockMatch[1];
      } else {
        // 提取大括号内容
        const braceMatch = response.match(/\{[\s\S]*\}/);
        if (braceMatch) {
          jsonStr = braceMatch[0];
        }
      }

      // 简单粗暴的修复
      jsonStr = jsonStr
        .trim()
        // 移除所有非法的转义字符（包括\b退格字符）
        .replace(/\\b/g, '')  // 移除退格字符
        .replace(/\\f/g, '')  // 移除换页字符
        .replace(/\\v/g, '')  // 移除垂直制表符
        .replace(/\\0/g, '')  // 移除空字符
        // 移除所有单独的反斜杠（不在有效转义序列中的）
        .replace(/\\(?!["\\/nrt])/g, '')
        // 修复双反斜杠
        .replace(/\\\\/g, '\\')
        // 处理换行符（确保在字符串内正确转义）
        .replace(/\n/g, '\\n')
        .replace(/\r/g, '\\r')
        .replace(/\t/g, '\\t')
        // 修复中文引号
        .replace(/[""]/g, '"')
        .replace(/['']/g, "'")
        // 移除注释
        .replace(/\/\/.*$/gm, '')
        .replace(/\/\*[\s\S]*?\*\//g, '')
        // 修复尾随逗号
        .replace(/,(\s*[}\]])/g, '$1');

      console.log('终极修复后预览:', jsonStr.substring(0, 100));
      return jsonStr;

    } catch (error) {
      console.error('终极修复失败:', error);
      return null;
    }
  }

  /**
   * 修复JSON字符串中的特殊字符
   * @param {string} jsonStr - JSON字符串
   * @returns {string} 修复后的JSON字符串
   */
  fixJsonStrings(jsonStr) {
    try {
      console.log('🔧 修复JSON字符串中的特殊字符...');

      // 使用正则表达式找到所有字符串值并修复
      return jsonStr.replace(/"([^"\\]*(\\.[^"\\]*)*)"/g, (match, content) => {
        // 修复字符串内容
        let fixed = content
          // 移除非法的转义字符
          .replace(/\\b/g, '')  // 移除退格字符
          .replace(/\\f/g, '')  // 移除换页字符
          .replace(/\\v/g, '')  // 移除垂直制表符
          .replace(/\\0/g, '')  // 移除空字符
          // 处理未转义的反斜杠（只保留有效的转义序列）
          .replace(/\\(?!["\\/nrt])/g, '')
          // 处理控制字符
          .replace(/\n/g, '\\n')
          .replace(/\r/g, '\\r')
          .replace(/\t/g, '\\t');

        return `"${fixed}"`;
      });

    } catch (error) {
      console.error('字符串修复失败:', error);
      return jsonStr;
    }
  }

  /**
   * 尝试修复JSON响应中的常见错误
   * @param {string} response - 原始响应
   * @returns {string|null} 修复后的JSON字符串，如果无法修复则返回null
   */
  tryFixJsonResponse(response) {
    try {
      console.log('🔧 尝试修复JSON格式错误...');

      // 提取可能的JSON部分
      let jsonStr = response;

      // 查找JSON对象
      const jsonMatch = jsonStr.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        jsonStr = jsonMatch[0];
      }

      // 首先清理基本格式
      jsonStr = this.cleanJsonString(jsonStr);

      // 修复常见问题 - 更强力的修复逻辑
      jsonStr = jsonStr
        // 🚨 首先处理最常见的反斜杠问题
        .replace(/\\(?!["\\/bfnrtu])/g, '')  // 移除无效的反斜杠转义
        .replace(/\\\\/g, '\\')  // 修复双反斜杠
        .replace(/\\"/g, '"')  // 处理转义引号
        // 移除多余的引号
        .replace(/"{2,}/g, '"')
        // 修复字符串内的换行符（保持JSON有效性）
        .replace(/\n/g, '\\n')
        .replace(/\r/g, '\\r')
        .replace(/\t/g, '\\t')
        // 修复尾随逗号
        .replace(/,(\s*[}\]])/g, '$1')
        // 修复缺失的引号（属性名）
        .replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g, '$1"$2":')
        // 修复单引号为双引号（但要小心字符串内容）
        .replace(/'/g, '"')
        // 移除注释
        .replace(/\/\*[\s\S]*?\*\//g, '')
        .replace(/\/\/.*$/gm, '')
        // 修复可能的Unicode转义问题
        .replace(/\\u([0-9a-fA-F]{4})/g, (match, hex) => {
          try {
            return String.fromCharCode(parseInt(hex, 16));
          } catch (e) {
            return match;
          }
        })
        // 🚨 额外的反斜杠清理
        .replace(/\\[^"\\\/bfnrtu]/g, '')  // 移除所有无效的反斜杠序列
        .replace(/\\\s/g, ' ')  // 移除反斜杠+空格的组合
        .replace(/\\$/g, '');  // 移除行尾的反斜杠

      console.log('修复后的JSON预览:', jsonStr.substring(0, 200));
      return jsonStr;

    } catch (error) {
      console.error('JSON修复失败:', error);
      return null;
    }
  }

  /**
   * 获取默认剧本结构
   * @returns {Object} 默认剧本数据
   */
  getDefaultScriptStructure() {
    return {
      storyInfo: {
        title: "神秘庄园案件",
        background: "在一个风雨交加的夜晚，古老的布莱克庄园发生了一起离奇的案件...",
        setting: "布莱克庄园，现代",
        coreEvent: "庄园主人神秘失踪",
        winConditions: "找出真相，完成各自的目标"
      },
      characters: [],
      timeline: [],
      truthQuestions: [
        "你认为谁最可疑？",
        "你有什么秘密？",
        "你的真实动机是什么？"
      ],
      miniGameTopics: ["神秘钥匙", "古老书籍", "隐藏房间"],
      solution: {
        truth: "案件真相将在游戏中揭晓",
        culprit: "",
        motive: "",
        method: "",
        evidence: ""
      }
    };
  }

  /**
   * 生成角色分配
   * @param {Object} scriptData - 剧本数据
   * @param {Array} playerIds - 玩家ID列表
   * @returns {Object} 角色分配结果
   */
  async generateRoleAssignment(scriptData, playerIds) {
    try {
      const result = await this.generateDynamicContent('roleAssignment', {
        scriptData,
        playerIds
      });

      return result;
    } catch (error) {
      console.error('AI角色分配失败:', error);
      // 返回随机分配作为备选方案
      return this.generateRandomAssignment(scriptData.characters, playerIds);
    }
  }

  /**
   * 通用JSON响应解析方法
   * @param {string} response - AI响应
   * @returns {Object} 解析后的JSON对象
   */
  parseJsonResponse(response) {
    try {
      // 提取JSON内容
      const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        const jsonStr = jsonMatch[1];
        return JSON.parse(jsonStr);
      } else {
        // 如果没有代码块，尝试直接解析
        return JSON.parse(response);
      }
    } catch (error) {
      console.error('解析AI JSON响应失败:', error);
      throw new Error('AI响应格式错误，无法解析JSON');
    }
  }

  /**
   * 生成随机角色分配（备选方案）
   * @param {Array} characters - 角色列表
   * @param {Array} playerIds - 玩家ID列表
   * @returns {Object} 随机分配结果
   */
  generateRandomAssignment(characters, playerIds) {
    const shuffledChars = [...characters].sort(() => Math.random() - 0.5);
    const assignments = playerIds.map((playerId, index) => {
      const character = shuffledChars[index % shuffledChars.length];
      return {
        playerId,
        characterId: character.id,
        characterName: character.name,
        isMainRole: character.faction !== '中立'
      };
    });

    return { assignments };
  }

  /**
   * 生成真心话问题
   * @param {Object} gameContext - 游戏上下文
   * @returns {Promise<Array>} 真心话问题列表
   */
  async generateTruthQuestions(gameContext) {
    const systemPrompt = `你是一位经验丰富的游戏主持人，擅长设计有趣的真心话问题。问题要结合当前游戏剧情，能够揭示角色信息，促进玩家互动。`;

    const userPrompt = `基于当前游戏情况，生成5个真心话问题：

游戏背景：${gameContext.storyBackground}
当前阶段：${gameContext.currentPhase}
存活角色：${gameContext.aliveCharacters.join(', ')}

要求：
1. 问题要与剧情相关
2. 能够引导玩家透露有用信息
3. 增加游戏的趣味性和互动性
4. 避免过于直接的指向性问题

请按JSON格式输出：
\`\`\`json
{
  "questions": [
    "问题1",
    "问题2",
    "问题3",
    "问题4",
    "问题5"
  ]
}
\`\`\``;

    const messages = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt }
    ];

    try {
      const response = await this.sendRequest(messages, { temperature: 0.8 });
      const parsed = this.parseJsonResponse(response);
      return parsed.questions || [];
    } catch (error) {
      console.error('生成真心话问题失败:', error);
      return this.getDefaultTruthQuestions();
    }
  }

  /**
   * 解析JSON响应的通用方法
   * @param {string} response - AI响应
   * @returns {Object} 解析结果
   */
  parseJsonResponse(response) {
    try {
      const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[1]);
      }
      return JSON.parse(response);
    } catch (error) {
      console.error('JSON解析失败:', error);
      return {};
    }
  }

  /**
   * 获取默认真心话问题
   * @returns {Array} 默认问题列表
   */
  getDefaultTruthQuestions() {
    return [
      "在这个案件中，你最怀疑谁？为什么？",
      "你认为凶手的动机是什么？",
      "你有什么秘密没有告诉大家？",
      "如果你是凶手，你会怎么做？",
      "你觉得哪个线索最重要？"
    ];
  }

  /**
   * 生成个人坚持机制选择
   * @param {Object} persistenceContext - 坚持机制上下文
   * @returns {Promise<Object>} 坚持机制选择数据
   */
  async generatePersistenceOptions(persistenceContext) {
    try {
      const prompt = AIPrompts.getPersistencePrompt(persistenceContext);
      const messages = [
        { role: 'system', content: AIPrompts.getSystemPrompt() },
        { role: 'user', content: prompt }
      ];

      const response = await this.sendRequest(messages, {
        temperature: 0.7,
        maxTokens: 2500
      });

      const result = this.parseJsonResponse(response);

      // 为AI生成的选择添加风险等级类名
      if (result && result.persistenceOptions) {
        result.persistenceOptions = result.persistenceOptions.map(option => ({
          ...option,
          riskLevelClass: this.getRiskLevelClass(option.riskLevel || '中风险')
        }));
      }

      return result;
    } catch (error) {
      console.error('生成坚持机制选择失败:', error);
      // 返回默认的坚持机制选择
      return this.getDefaultPersistenceOptions(persistenceContext);
    }
  }

  /**
   * 计算玩家影响力
   * @param {Object} influenceContext - 影响力计算上下文
   * @returns {Promise<Object>} 影响力计算结果
   */
  async calculateInfluence(influenceContext) {
    try {
      const prompt = AIPrompts.getInfluenceCalculationPrompt(influenceContext);
      const messages = [
        { role: 'system', content: AIPrompts.getSystemPrompt() },
        { role: 'user', content: prompt }
      ];

      const response = await this.sendRequest(messages, {
        temperature: 0.3, // 较低的温度确保计算的一致性
        maxTokens: 2000
      });

      return this.parseJsonResponse(response);
    } catch (error) {
      console.error('计算影响力失败:', error);
      // 返回默认的影响力计算
      return this.getDefaultInfluenceCalculation(influenceContext);
    }
  }

  /**
   * 获取风险等级的CSS类名
   * @param {string} riskLevel - 风险等级
   * @returns {string} CSS类名
   */
  getRiskLevelClass(riskLevel) {
    const riskMap = {
      '无风险': 'none',
      '低风险': 'low',
      '中风险': 'medium',
      '高风险': 'high'
    };
    return riskMap[riskLevel] || 'medium';
  }

  /**
   * 获取默认的坚持机制选择
   * @param {Object} persistenceContext - 坚持机制上下文
   * @returns {Object} 默认坚持机制选择
   */
  getDefaultPersistenceOptions(persistenceContext) {
    const currentInfluence = persistenceContext.currentInfluence || 50;

    const options = [
      {
        type: "follow_collective",
        name: "跟随集体剧情",
        description: `接受"${persistenceContext.voteResult || '集体决定'}"的结果，与团队保持一致，剧情按多数选择发展。`,
        influenceCost: 0,
        effects: ["保持团队和谐", "获得+5影响力奖励", "剧情按集体选择发展"],
        riskLevel: "无风险",
        recommendation: "稳妥的选择，适合保守型玩家",
        icon: "✓"
      },
      {
        type: "light_persistence",
        name: "轻度个人坚持",
        description: "在集体选择基础上，发挥个人特长获得额外收益，不改变主要剧情走向。",
        influenceCost: 20,
        effects: ["获得额外个人线索", "增加角色专属信息", "小幅提升个人优势"],
        riskLevel: "低风险",
        recommendation: currentInfluence >= 20 ? "性价比较高的选择" : "影响力不足，建议谨慎",
        icon: "💡"
      },
      {
        type: "strong_persistence",
        name: "强力个人坚持",
        description: "完全改变剧情发展方向，开启个人专属剧情线，可能影响其他玩家体验。",
        influenceCost: 40,
        effects: ["开启个人剧情线", "获得显著游戏优势", "可能改变游戏走向"],
        riskLevel: "中风险",
        recommendation: currentInfluence >= 40 ? "高风险高回报的选择" : "影响力不足，无法选择",
        icon: "⚡"
      },
      {
        type: "extreme_persistence",
        name: "极限个人坚持",
        description: "颠覆性的剧情转折，开启隐藏剧情分支，获得游戏决定性优势。",
        influenceCost: 70,
        effects: ["开启隐藏剧情", "获得决定性优势", "可能完全改变游戏结局"],
        riskLevel: "高风险",
        recommendation: currentInfluence >= 70 ? "孤注一掷的选择，需要谨慎考虑" : "影响力不足，无法选择",
        icon: "🔥",
        requirements: ["需要高地位角色", "影响力≥70点"]
      }
    ];

    // 为每个选项添加风险等级类名
    const optionsWithClass = options.map(option => ({
      ...option,
      riskLevelClass: this.getRiskLevelClass(option.riskLevel)
    }));

    return {
      persistenceOptions: optionsWithClass,
      contextAnalysis: `当前集体选择是"${persistenceContext.voteResult || '未知'}"，你作为${persistenceContext.playerRole || '角色'}，拥有${currentInfluence}点影响力。`,
      strategicAdvice: currentInfluence >= 70 ? "影响力充足，可以考虑大胆的选择" : currentInfluence >= 40 ? "影响力中等，建议平衡风险和收益" : "影响力较低，建议保守选择",
      timeLimit: 30,
      specialConditions: ["选择后无法撤销", "影响力消耗立即生效"],
      influenceAnalysis: {
        currentLevel: currentInfluence >= 80 ? "极高影响" : currentInfluence >= 60 ? "高影响" : currentInfluence >= 40 ? "中等影响" : "低影响",
        afterChoice: "根据选择不同而变化",
        recommendations: ["合理使用影响力", "考虑长远游戏策略"]
      }
    };
  }

  /**
   * 获取默认的影响力计算结果
   * @param {Object} influenceContext - 影响力计算上下文
   * @returns {Object} 默认影响力计算结果
   */
  getDefaultInfluenceCalculation(influenceContext) {
    const currentInfluence = influenceContext.currentInfluence || 50;
    const roleStatus = influenceContext.roleStatus || '中等';

    // 基础影响力映射
    const baseInfluenceMap = {
      '低等': 35,
      '中等': 50,
      '高等': 70,
      '极高': 90
    };

    const baseInfluence = baseInfluenceMap[roleStatus] || 50;
    const performanceBonus = Math.floor(Math.random() * 20) - 10; // -10到+10的随机加成
    const totalEarned = Math.max(0, performanceBonus);
    const totalSpent = Math.max(0, baseInfluence + totalEarned - currentInfluence);

    return {
      currentInfluence: currentInfluence,
      availableInfluence: currentInfluence - totalSpent,
      influenceBreakdown: {
        baseInfluence: baseInfluence,
        performanceBonus: performanceBonus,
        totalEarned: totalEarned,
        totalSpent: totalSpent
      },
      influenceLevel: currentInfluence >= 80 ? "极高" : currentInfluence >= 60 ? "高" : currentInfluence >= 40 ? "中" : "低",
      levelDescription: currentInfluence >= 80 ? "拥有强大的话语权和影响力" : currentInfluence >= 60 ? "在团队中有重要地位" : currentInfluence >= 40 ? "具有一定的影响力" : "影响力有限，需要积极表现",
      nextLevelRequirement: currentInfluence >= 80 ? "已达最高等级" : currentInfluence >= 60 ? 80 : currentInfluence >= 40 ? 60 : 40,
      recommendations: [
        "积极参与讨论",
        "分享有价值的线索",
        "展现出色的推理能力",
        "与其他玩家建立良好关系"
      ],
      specialAbilities: currentInfluence >= 80 ? ["可使用所有坚持机制", "额外投票权重"] : currentInfluence >= 60 ? ["可使用强力坚持", "优先发言权"] : currentInfluence >= 40 ? ["可使用轻度坚持", "额外线索获取"] : ["基础游戏功能"],
      persistenceCapacity: {
        lightPersistence: Math.floor(currentInfluence / 20),
        strongPersistence: Math.floor(currentInfluence / 40),
        extremePersistence: Math.floor(currentInfluence / 70)
      }
    };
  }

  /**
   * 诊断JSON解析错误，提供详细的错误分析
   * @param {Error} error - JSON解析错误
   * @param {string} response - 原始响应内容
   */
  diagnoseJsonError(error, response) {
    console.log('🔍 开始JSON错误诊断...');

    // 分析错误类型
    if (error.message.includes('Unexpected token')) {
      const tokenMatch = error.message.match(/Unexpected token (.+?) in JSON/);
      if (tokenMatch) {
        const token = tokenMatch[1];
        console.log(`🚨 发现意外字符: ${token}`);

        // 特殊字符诊断
        if (token === '\\') {
          console.log('💡 诊断结果: 反斜杠转义问题');
          console.log('💡 可能原因: AI生成了非法的转义字符（如 \\b, \\f, \\v 等）');
          console.log('💡 解决方案: 强化预处理，移除所有非法转义字符');
        } else if (token === '/') {
          console.log('💡 诊断结果: 注释问题');
          console.log('💡 可能原因: AI在JSON中添加了 // 或 /* */ 注释');
          console.log('💡 解决方案: 强化注释移除功能');
        } else if (token === '"') {
          console.log('💡 诊断结果: 引号问题');
          console.log('💡 可能原因: 字符串中包含未转义的引号');
          console.log('💡 解决方案: 修复字符串转义');
        }
      }
    }

    // 检查常见问题
    const issues = [];
    if (response.includes('//')) {
      issues.push('包含单行注释 //');
    }
    if (response.includes('/*')) {
      issues.push('包含多行注释 /* */');
    }
    if (response.includes('\\b')) {
      issues.push('包含退格字符 \\b');
    }
    if (response.includes('\\f')) {
      issues.push('包含换页字符 \\f');
    }
    if (response.includes('\\v')) {
      issues.push('包含垂直制表符 \\v');
    }

    if (issues.length > 0) {
      console.log('🔍 发现的问题:', issues.join(', '));
    }
  }
}

// 创建全局AI服务实例
const aiService = new AIService();

module.exports = aiService;
