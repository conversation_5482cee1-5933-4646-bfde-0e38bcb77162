// 全局状态管理
class Store {
  constructor() {
    this.state = {
      // 用户状态
      user: {
        isLoggedIn: false,
        userInfo: null,
        token: ''
      },
      
      // 当前房间状态
      currentRoom: {
        roomId: '',
        roomInfo: null,
        players: [],
        isHost: false,
        isReady: false
      },
      
      // 当前游戏状态
      currentGame: {
        gameId: '',
        status: '', // preparing, story, roles, discussion, voting, result
        phase: '',
        timeLeft: 0,
        myCharacter: null,
        allCharacters: [],
        clues: [],
        messages: []
      },
      
      // 应用设置
      settings: {
        soundEnabled: true,
        vibrationEnabled: true,
        autoSave: true,
        theme: 'auto' // light, dark, auto
      }
    };
    
    this.listeners = new Map();
    this.loadFromStorage();
  }

  // 获取状态
  getState(path = '') {
    if (!path) return this.state;
    
    const keys = path.split('.');
    let current = this.state;
    
    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return undefined;
      }
    }
    
    return current;
  }

  // 设置状态
  setState(path, value) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    let current = this.state;
    
    // 导航到目标对象
    for (const key of keys) {
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }
    
    // 设置值
    const oldValue = current[lastKey];
    current[lastKey] = value;
    
    // 触发监听器
    this.notifyListeners(path, value, oldValue);
    
    // 保存到本地存储
    this.saveToStorage();
  }

  // 更新状态（合并对象）
  updateState(path, updates) {
    const currentValue = this.getState(path);
    if (typeof currentValue === 'object' && typeof updates === 'object') {
      const newValue = { ...currentValue, ...updates };
      this.setState(path, newValue);
    } else {
      this.setState(path, updates);
    }
  }

  // 订阅状态变化
  subscribe(path, callback) {
    if (!this.listeners.has(path)) {
      this.listeners.set(path, new Set());
    }
    this.listeners.get(path).add(callback);
    
    // 返回取消订阅函数
    return () => {
      const pathListeners = this.listeners.get(path);
      if (pathListeners) {
        pathListeners.delete(callback);
        if (pathListeners.size === 0) {
          this.listeners.delete(path);
        }
      }
    };
  }

  // 通知监听器
  notifyListeners(path, newValue, oldValue) {
    // 通知精确路径的监听器
    const pathListeners = this.listeners.get(path);
    if (pathListeners) {
      pathListeners.forEach(callback => {
        try {
          callback(newValue, oldValue, path);
        } catch (error) {
          console.error('State listener error:', error);
        }
      });
    }
    
    // 通知父路径的监听器
    const pathParts = path.split('.');
    for (let i = pathParts.length - 1; i > 0; i--) {
      const parentPath = pathParts.slice(0, i).join('.');
      const parentListeners = this.listeners.get(parentPath);
      if (parentListeners) {
        const parentValue = this.getState(parentPath);
        parentListeners.forEach(callback => {
          try {
            callback(parentValue, parentValue, parentPath);
          } catch (error) {
            console.error('State listener error:', error);
          }
        });
      }
    }
  }

  // 从本地存储加载
  loadFromStorage() {
    try {
      const savedState = wx.getStorageSync('app_state');
      if (savedState) {
        const parsed = JSON.parse(savedState);
        // 只恢复用户信息和设置，游戏状态不恢复
        if (parsed.user) {
          this.state.user = { ...this.state.user, ...parsed.user };
        }
        if (parsed.settings) {
          this.state.settings = { ...this.state.settings, ...parsed.settings };
        }
      }
    } catch (error) {
      console.error('Failed to load state from storage:', error);
    }
  }

  // 保存到本地存储
  saveToStorage() {
    try {
      const stateToSave = {
        user: this.state.user,
        settings: this.state.settings
        // 不保存房间和游戏状态，这些是临时的
      };
      wx.setStorageSync('app_state', JSON.stringify(stateToSave));
    } catch (error) {
      console.error('Failed to save state to storage:', error);
    }
  }

  // 清除状态
  clearState(path = '') {
    if (!path) {
      // 清除所有状态
      this.state = {
        user: { isLoggedIn: false, userInfo: null, token: '' },
        currentRoom: { roomId: '', roomInfo: null, players: [], isHost: false, isReady: false },
        currentGame: { gameId: '', status: '', phase: '', timeLeft: 0, myCharacter: null, allCharacters: [], clues: [], messages: [] },
        settings: this.state.settings // 保留设置
      };
    } else {
      this.setState(path, null);
    }
    this.saveToStorage();
  }

  // 用户相关方法
  setUser(userInfo) {
    this.setState('user.userInfo', userInfo);
    this.setState('user.isLoggedIn', true);
  }

  setToken(token) {
    this.setState('user.token', token);
  }

  logout() {
    this.setState('user', {
      isLoggedIn: false,
      userInfo: null,
      token: ''
    });
    this.clearState('currentRoom');
    this.clearState('currentGame');
  }

  // 房间相关方法
  setCurrentRoom(roomInfo) {
    this.setState('currentRoom.roomInfo', roomInfo);
    this.setState('currentRoom.roomId', roomInfo.roomId);
  }

  updateRoomPlayers(players) {
    this.setState('currentRoom.players', players);
  }

  setHostStatus(isHost) {
    this.setState('currentRoom.isHost', isHost);
  }

  setReadyStatus(isReady) {
    this.setState('currentRoom.isReady', isReady);
  }

  // 游戏相关方法
  setCurrentGame(gameInfo) {
    this.updateState('currentGame', gameInfo);
  }

  updateGameStatus(status, phase = '') {
    this.setState('currentGame.status', status);
    if (phase) {
      this.setState('currentGame.phase', phase);
    }
  }

  setMyCharacter(character) {
    this.setState('currentGame.myCharacter', character);
  }

  addClue(clue) {
    const currentClues = this.getState('currentGame.clues') || [];
    this.setState('currentGame.clues', [...currentClues, clue]);
  }

  addMessage(message) {
    const currentMessages = this.getState('currentGame.messages') || [];
    this.setState('currentGame.messages', [...currentMessages, message]);
  }
}

// 创建全局store实例
const store = new Store();

module.exports = store;
