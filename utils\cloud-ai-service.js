// utils/cloud-ai-service.js
// 基于云函数的安全AI服务

const MockAIService = require('./mock-ai-service');

class CloudAIService {
  constructor() {
    this.mockService = new MockAIService();
    this.cloudEnabled = false;
    this.init();
  }

  /**
   * 初始化服务
   */
  init() {
    // 检查云开发是否可用
    const app = getApp();
    this.cloudEnabled = app.globalData.cloudEnabled || false;
    
    console.log('☁️ 云AI服务初始化:', {
      cloudEnabled: this.cloudEnabled,
      hasWxCloud: !!wx.cloud
    });
  }

  /**
   * 生成剧本
   * @param {Object} params - 生成参数
   * @returns {Promise<Object>} 生成的剧本
   */
  async generateScript(params) {
    console.log('📝 生成剧本请求:', params);
    
    try {
      if (this.cloudEnabled && wx.cloud) {
        // 使用云函数
        const result = await this.callCloudFunction('generateScript', params);
        if (result.success) {
          return result.data;
        } else {
          console.warn('☁️ 云函数调用失败，回退到模拟服务:', result.error);
        }
      }
      
      // 回退到模拟服务
      console.log('🎭 使用模拟AI服务生成剧本');
      return await this.mockService.generateScript(params);
      
    } catch (error) {
      console.error('❌ 生成剧本失败:', error);
      
      // 最终回退到模拟服务
      console.log('🎭 回退到模拟AI服务');
      return await this.mockService.generateScript(params);
    }
  }

  /**
   * 生成角色
   * @param {Object} params - 生成参数
   * @returns {Promise<Object>} 生成的角色
   */
  async generateCharacter(params) {
    console.log('👤 生成角色请求:', params);
    
    try {
      if (this.cloudEnabled && wx.cloud) {
        // 使用云函数
        const result = await this.callCloudFunction('generateCharacter', params);
        if (result.success) {
          return result.data;
        } else {
          console.warn('☁️ 云函数调用失败，回退到模拟服务:', result.error);
        }
      }
      
      // 回退到模拟服务
      console.log('🎭 使用模拟AI服务生成角色');
      return await this.mockService.generateCharacter(params);
      
    } catch (error) {
      console.error('❌ 生成角色失败:', error);
      
      // 最终回退到模拟服务
      console.log('🎭 回退到模拟AI服务');
      return await this.mockService.generateCharacter(params);
    }
  }

  /**
   * 生成线索
   * @param {Object} params - 生成参数
   * @returns {Promise<Object>} 生成的线索
   */
  async generateClue(params) {
    console.log('🔍 生成线索请求:', params);
    
    try {
      if (this.cloudEnabled && wx.cloud) {
        // 使用云函数
        const result = await this.callCloudFunction('generateClue', params);
        if (result.success) {
          return result.data;
        } else {
          console.warn('☁️ 云函数调用失败，回退到模拟服务:', result.error);
        }
      }
      
      // 回退到模拟服务
      console.log('🎭 使用模拟AI服务生成线索');
      return await this.mockService.generateClue(params);
      
    } catch (error) {
      console.error('❌ 生成线索失败:', error);
      
      // 最终回退到模拟服务
      console.log('🎭 回退到模拟AI服务');
      return await this.mockService.generateClue(params);
    }
  }

  /**
   * 测试连接
   * @returns {Promise<Object>} 测试结果
   */
  async testConnection() {
    console.log('🔍 测试AI服务连接...');
    
    const results = {
      cloudService: null,
      mockService: null,
      recommendation: 'mock'
    };

    // 测试云函数服务
    if (this.cloudEnabled && wx.cloud) {
      try {
        const result = await this.callCloudFunction('testConnection', {});
        results.cloudService = {
          available: result.success,
          message: result.success ? '云AI服务连接成功' : result.error,
          details: result
        };
        
        if (result.success) {
          results.recommendation = 'cloud';
        }
      } catch (error) {
        results.cloudService = {
          available: false,
          message: '云AI服务连接失败',
          error: error.message
        };
      }
    } else {
      results.cloudService = {
        available: false,
        message: '云开发未启用或不可用'
      };
    }

    // 测试模拟服务
    try {
      const mockResult = await this.mockService.generateResponse('测试', { maxTokens: 10 });
      results.mockService = {
        available: true,
        message: '模拟AI服务正常',
        response: mockResult
      };
    } catch (error) {
      results.mockService = {
        available: false,
        message: '模拟AI服务异常',
        error: error.message
      };
    }

    return results;
  }

  /**
   * 调用云函数
   * @param {string} action - 操作类型
   * @param {Object} data - 请求数据
   * @returns {Promise<Object>} 云函数响应
   */
  async callCloudFunction(action, data) {
    if (!wx.cloud) {
      throw new Error('云开发不可用');
    }

    try {
      console.log(`☁️ 调用云函数: ${action}`);
      
      const result = await wx.cloud.callFunction({
        name: 'ai-service',
        data: {
          action: action,
          data: data
        }
      });

      console.log('☁️ 云函数响应:', {
        success: result.result?.success,
        hasData: !!result.result?.data,
        error: result.result?.error
      });

      return result.result;
    } catch (error) {
      console.error('☁️ 云函数调用异常:', error);
      
      // 解析云函数错误
      let errorMessage = '云函数调用失败';
      
      if (error.errMsg) {
        if (error.errMsg.includes('FunctionNotFound')) {
          errorMessage = '云函数未部署或不存在';
        } else if (error.errMsg.includes('timeout')) {
          errorMessage = '云函数调用超时';
        } else {
          errorMessage = error.errMsg;
        }
      }
      
      return {
        success: false,
        error: errorMessage,
        code: 'CLOUD_FUNCTION_ERROR'
      };
    }
  }

  /**
   * 获取服务状态
   * @returns {Object} 服务状态信息
   */
  getServiceStatus() {
    return {
      cloudEnabled: this.cloudEnabled,
      hasWxCloud: !!wx.cloud,
      mockServiceAvailable: true,
      currentMode: this.cloudEnabled ? 'cloud' : 'mock'
    };
  }

  /**
   * 强制使用模拟服务
   */
  forceUseMockService() {
    this.cloudEnabled = false;
    console.log('🎭 强制使用模拟AI服务');
  }

  /**
   * 尝试启用云服务
   */
  tryEnableCloudService() {
    const app = getApp();
    this.cloudEnabled = app.globalData.cloudEnabled || false;
    console.log('☁️ 尝试启用云AI服务:', this.cloudEnabled);
  }

  /**
   * 兼容旧接口 - generateStoryPrompt
   */
  async generateStoryPrompt(params) {
    return await this.generateScript(params);
  }

  /**
   * 兼容旧接口 - callMoonshotAPI
   */
  async callMoonshotAPI(prompt, options = {}) {
    console.warn('⚠️ callMoonshotAPI 已弃用，请使用具体的生成方法');
    
    // 简单的提示词分类
    if (prompt.includes('剧本') || prompt.includes('故事')) {
      return await this.generateScript({
        theme: '推理悬疑',
        playerCount: 4,
        difficulty: '中等'
      });
    } else if (prompt.includes('角色') || prompt.includes('人物')) {
      return await this.generateCharacter({
        theme: '推理悬疑',
        characterType: '主角'
      });
    } else {
      return await this.generateClue({
        theme: '推理悬疑',
        clueType: '物证'
      });
    }
  }
}

module.exports = CloudAIService;
