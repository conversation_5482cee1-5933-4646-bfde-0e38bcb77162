// 私人线索页面逻辑
const api = require('../../utils/api');
const common = require('../../utils/common');
const errorHandler = require('../../utils/error-handler');

Page({
  data: {
    roomId: '',
    activeTab: 'all',
    activeTabLabel: '全部',
    showClueDetail: false,
    selectedClue: null,

    // 角色信息
    myCharacter: {
      id: 'char_001',
      name: '艾米丽·哈特',
      title: '庄园女主人',
      avatar: '',
      isAlive: true
    },

    // 目标完成情况
    completedObjectives: 1,
    totalObjectives: 3,

    // 线索分类标签
    clueTabs: [
      { key: 'all', label: '全部', icon: 'view-list', count: 0 },
      { key: 'critical', label: '关键', icon: 'error', count: 0 },
      { key: 'character', label: '人物', icon: 'user', count: 0 },
      { key: 'location', label: '地点', icon: 'location', count: 0 },
      { key: 'item', label: '物品', icon: 'shop', count: 0 }
    ],

    // 所有线索
    clues: [],

    // 过滤后的线索
    filteredClues: []
  },

  onLoad(options) {
    if (options.roomId) {
      this.setData({ roomId: options.roomId });
    }

    // 加载私人线索
    this.loadPrivateClues();
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },

  // 刷新线索
  async refreshClues() {
    wx.vibrateShort();
    await this.loadPrivateClues();
    this.showToast('线索已刷新', 'success');
  },

  // 加载私人线索
  async loadPrivateClues() {
    try {
      // 这里应该调用API获取私人线索
      // const clues = await api.getPrivateClues(this.data.roomId, this.data.myCharacter.id);

      // 模拟线索数据
      const mockClues = [
        {
          id: 'clue_001',
          title: '神秘的日记本',
          content: '在书房的抽屉里发现了一本日记，记录着庄园主人的秘密往事。日记中提到了一个隐藏的密室，以及一段不为人知的恋情。',
          preview: '在书房的抽屉里发现了一本日记，记录着庄园主人的秘密往事...',
          type: 'item',
          importance: 'critical',
          discoveredTime: '10分钟前',
          isRead: false,
          tags: ['日记', '秘密', '密室'],
          relatedCharacters: [
            { id: 'char_002', name: '詹姆斯', avatar: '' }
          ],
          analysis: '这本日记可能是解开庄园秘密的关键线索，建议仔细研读其中的内容。'
        },
        {
          id: 'clue_002',
          title: '破损的照片',
          content: '在壁炉旁发现了一张破损的老照片，照片中有两个人，但其中一人的脸被故意划掉了。背面写着"永远不会原谅"。',
          preview: '在壁炉旁发现了一张破损的老照片，照片中有两个人...',
          type: 'item',
          importance: 'high',
          discoveredTime: '15分钟前',
          isRead: true,
          tags: ['照片', '仇恨', '过去'],
          relatedCharacters: [
            { id: 'char_003', name: '维多利亚', avatar: '' }
          ],
          analysis: '照片显示了某种深层的怨恨关系，被划掉的人可能就是关键人物。'
        },
        {
          id: 'clue_003',
          title: '奇怪的脚印',
          content: '在花园的泥土中发现了一串奇怪的脚印，脚印很深，似乎是有人匆忙逃离现场留下的。',
          preview: '在花园的泥土中发现了一串奇怪的脚印...',
          type: 'location',
          importance: 'normal',
          discoveredTime: '20分钟前',
          isRead: true,
          tags: ['脚印', '花园', '逃离'],
          relatedCharacters: [],
          analysis: '脚印的方向指向后门，可能与某个角色的行踪有关。'
        },
        {
          id: 'clue_004',
          title: '管家的证词',
          content: '管家提到昨晚听到了争吵声，声音来自二楼的书房。争吵持续了大约十分钟，然后突然安静下来。',
          preview: '管家提到昨晚听到了争吵声，声音来自二楼的书房...',
          type: 'character',
          importance: 'high',
          discoveredTime: '25分钟前',
          isRead: false,
          tags: ['证词', '争吵', '书房'],
          relatedCharacters: [
            { id: 'char_004', name: '查尔斯', avatar: '' }
          ],
          analysis: '争吵的时间点很关键，需要确认当时在场的人员。'
        },
        {
          id: 'clue_005',
          title: '丢失的钥匙',
          content: '发现保险箱的钥匙不见了，而保险箱里原本存放着重要的文件。钥匙最后一次被看到是在昨天晚上。',
          preview: '发现保险箱的钥匙不见了，而保险箱里原本存放着重要的文件...',
          type: 'item',
          importance: 'normal',
          discoveredTime: '30分钟前',
          isRead: true,
          tags: ['钥匙', '保险箱', '文件'],
          relatedCharacters: [],
          analysis: '钥匙的失踪可能与某个重要秘密有关。'
        }
      ];

      this.setData({ clues: mockClues });
      this.updateClueStats();
      this.filterClues();

    } catch (error) {
      console.error('加载私人线索失败:', error);
      errorHandler.showError(error, '加载私人线索', this);
    }
  },

  // 更新线索统计
  updateClueStats() {
    const clues = this.data.clues;
    const clueTabs = this.data.clueTabs.map(tab => {
      let count = 0;
      if (tab.key === 'all') {
        count = clues.length;
      } else if (tab.key === 'critical') {
        count = clues.filter(c => c.importance === 'critical').length;
      } else {
        count = clues.filter(c => c.type === tab.key).length;
      }
      return { ...tab, count };
    });

    this.setData({ clueTabs });
  },

  // 切换标签
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    const tabInfo = this.data.clueTabs.find(t => t.key === tab);

    this.setData({
      activeTab: tab,
      activeTabLabel: tabInfo.label
    });

    this.filterClues();
    wx.vibrateShort();
  },

  // 过滤线索
  filterClues() {
    const { clues, activeTab } = this.data;
    let filteredClues = clues;

    if (activeTab !== 'all') {
      if (activeTab === 'critical') {
        filteredClues = clues.filter(c => c.importance === 'critical');
      } else {
        filteredClues = clues.filter(c => c.type === activeTab);
      }
    }

    // 按重要性和时间排序
    filteredClues.sort((a, b) => {
      const importanceOrder = { critical: 4, high: 3, normal: 2, low: 1 };
      const aImportance = importanceOrder[a.importance] || 0;
      const bImportance = importanceOrder[b.importance] || 0;

      if (aImportance !== bImportance) {
        return bImportance - aImportance;
      }

      return new Date(b.discoveredTime) - new Date(a.discoveredTime);
    });

    this.setData({ filteredClues });
  },

  // 查看线索详情
  viewClueDetail(e) {
    const clueId = e.currentTarget.dataset.clueId;
    const clue = this.data.clues.find(c => c.id === clueId);

    if (clue) {
      // 标记为已读
      if (!clue.isRead) {
        clue.isRead = true;
        this.setData({ clues: this.data.clues });
        this.filterClues();
      }

      this.setData({
        selectedClue: clue,
        showClueDetail: true
      });
    }
  },

  // 关闭线索详情
  closeClueDetail() {
    this.setData({ showClueDetail: false });
  },

  // 获取线索图标
  getClueIcon(type) {
    const iconMap = {
      item: 'shop',
      character: 'user',
      location: 'location',
      evidence: 'file-text',
      testimony: 'chat'
    };
    return iconMap[type] || 'file-text';
  },

  // 获取线索颜色
  getClueColor(importance) {
    const colorMap = {
      critical: '#ff4d4f',
      high: '#ffc107',
      normal: '#667eea',
      low: '#999'
    };
    return colorMap[importance] || '#667eea';
  },

  // 获取线索主题
  getClueTheme(importance) {
    const themeMap = {
      critical: 'error',
      high: 'warning',
      normal: 'primary',
      low: 'default'
    };
    return themeMap[importance] || 'primary';
  },

  // 获取重要性文本
  getImportanceText(importance) {
    const textMap = {
      critical: '关键线索',
      high: '重要线索',
      normal: '普通线索',
      low: '次要线索'
    };
    return textMap[importance] || '普通线索';
  },

  // 标记为重要
  markAsImportant() {
    wx.vibrateShort();
    this.showToast('已标记为重要', 'success');
  },

  // 添加到笔记
  addToNotes() {
    wx.vibrateShort();
    this.showToast('已添加到笔记', 'success');
  },

  // 查看笔记
  viewNotes() {
    wx.showToast({
      title: '笔记功能开发中',
      icon: 'none'
    });
  },

  // 分享线索
  shareClue() {
    wx.showToast({
      title: '分享功能开发中',
      icon: 'none'
    });
  },

  // 进入讨论
  enterDiscussion() {
    wx.navigateTo({
      url: `/pages/discussion/discussion?roomId=${this.data.roomId}`
    });
  },

  // 继续到人物关系页面
  continueToCharacterRelations() {
    console.log('跳转到人物关系页面', this.data.roomId);

    wx.navigateTo({
      url: `/pages/character-relations/character-relations?roomId=${this.data.roomId}`,
      success: (res) => {
        console.log('跳转人物关系页面成功', res);
      },
      fail: (err) => {
        console.error('跳转人物关系页面失败', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        });
      }
    });
  },

  // 显示提示
  showToast(message, theme = 'warning') {
    wx.showToast({
      title: message,
      icon: theme === 'success' ? 'success' : theme === 'error' ? 'error' : 'none',
      duration: 2000
    });
  }
});