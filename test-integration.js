// test-integration.js - 测试整个系统集成
const AIService = require('./utils/ai-service');
const apiConfig = require('./config/api-config');

async function testIntegration() {
  console.log('🧪 开始集成测试...');
  console.log('================================');

  // 1. 测试API配置
  console.log('📋 1. 测试API配置');
  const config = apiConfig.getCurrentConfig();
  console.log('当前配置:', {
    baseUrl: config.baseUrl,
    model: config.model,
    apiKey: config.apiKey ? `${config.apiKey.substring(0, 10)}...${config.apiKey.substring(config.apiKey.length - 4)}` : '未设置'
  });

  const validation = apiConfig.validateApiKey(config.apiKey);
  console.log('密钥验证:', validation);
  console.log('');

  // 2. 测试AI服务初始化
  console.log('🤖 2. 测试AI服务初始化');
  const aiService = new AIService();
  console.log('AI服务已初始化');
  console.log('是否使用模拟服务:', aiService.shouldUseMockService());
  console.log('');

  // 3. 测试故事生成
  console.log('📖 3. 测试故事生成');
  try {
    const storyParams = {
      theme: '现代都市',
      playerCount: 4,
      difficulty: 'easy'
    };

    console.log('生成参数:', storyParams);
    const startTime = Date.now();
    
    const story = await aiService.generateStoryPrompt(storyParams);
    
    const endTime = Date.now();
    console.log(`✅ 故事生成成功 (耗时: ${endTime - startTime}ms)`);
    console.log('生成的故事:');
    console.log(JSON.stringify(story, null, 2));
    console.log('');

    // 4. 测试角色生成
    console.log('👥 4. 测试角色生成');
    if (aiService.shouldUseMockService()) {
      const characters = await aiService.mockService.generateCharacters(story, storyParams.playerCount);
      console.log('✅ 角色生成成功');
      console.log('生成的角色:');
      console.log(JSON.stringify(characters, null, 2));
    } else {
      console.log('⏭️  跳过角色生成测试（使用真实AI服务）');
    }
    console.log('');

    // 5. 测试线索生成
    console.log('🔍 5. 测试线索生成');
    if (aiService.shouldUseMockService()) {
      const characters = await aiService.mockService.generateCharacters(story, storyParams.playerCount);
      const clues = await aiService.mockService.generateClues(story, characters);
      console.log('✅ 线索生成成功');
      console.log('生成的线索:');
      console.log(JSON.stringify(clues, null, 2));
    } else {
      console.log('⏭️  跳过线索生成测试（使用真实AI服务）');
    }
    console.log('');

  } catch (error) {
    console.log('❌ 故事生成失败:', error.message);
    console.log('错误详情:', error);
  }

  // 6. 测试服务状态
  console.log('📊 6. 测试服务状态');
  try {
    let status;
    if (aiService.shouldUseMockService()) {
      status = await aiService.mockService.checkServiceStatus();
    } else {
      // 对于真实AI服务，我们可以尝试测试连接
      const connectionTest = await aiService.testConnection();
      status = {
        status: connectionTest ? 'connected' : 'disconnected',
        message: connectionTest ? 'AI服务连接正常' : 'AI服务连接失败'
      };
    }
    
    console.log('✅ 服务状态检查完成');
    console.log('状态信息:', status);
  } catch (error) {
    console.log('❌ 服务状态检查失败:', error.message);
  }

  console.log('');
  console.log('🎉 集成测试完成！');
  console.log('================================');
  
  // 总结
  console.log('📝 测试总结:');
  console.log(`- API配置: ${validation.valid ? '✅ 有效' : '❌ 无效'}`);
  console.log(`- 服务模式: ${aiService.shouldUseMockService() ? '🎭 模拟服务' : '🤖 真实AI服务'}`);
  console.log(`- 故事生成: 已测试`);
  console.log(`- 系统状态: 运行正常`);
  
  if (aiService.shouldUseMockService()) {
    console.log('');
    console.log('💡 提示: 当前使用模拟AI服务');
    console.log('   - 这是因为API密钥无效或未配置');
    console.log('   - 模拟服务提供完整的功能用于开发和测试');
    console.log('   - 配置有效的API密钥后将自动切换到真实AI服务');
  }
}

// 运行测试
console.log('🚀 启动系统集成测试');
console.log('时间:', new Date().toLocaleString());
console.log('');

testIntegration().catch(error => {
  console.error('💥 测试过程中发生错误:', error);
  process.exit(1);
});
