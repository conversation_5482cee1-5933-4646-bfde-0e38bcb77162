// 个人中心页面逻辑
const api = require('../../utils/api');
const common = require('../../utils/common');

Page({
  data: {
    userInfo: {
      id: 'user123',
      nickname: '游客MdEg',
      avatar: '',
      description: '推理游戏爱好者',
      influence: 385,
      totalGames: 15,
      winRate: 73,
      skillTags: ['推理型', '社交型', '合作型']
    },
    gameStats: {
      wins: 11,
      losses: 4,
      avgRating: 8.7,
      mvpCount: 3
    },
    settings: {
      notification: true,
      privacy: false
    },
    newNickname: '',
    appVersion: '1.2.0'
  },

  onLoad() {
    this.loadUserInfo();
  },

  onShow() {
    // 页面显示时刷新用户信息
    this.refreshUserInfo();
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      common.showLoading('加载中...');
      
      // 这里应该调用实际的API
      // const userRes = await api.getUserInfo();
      // const statsRes = await api.getUserStats();
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // this.setData({
      //   userInfo: userRes.data,
      //   gameStats: statsRes.data
      // });
      
      common.hideLoading();
    } catch (error) {
      console.error('加载用户信息失败:', error);
      common.hideLoading();
      this.showToast('加载失败，请重试');
    }
  },

  // 刷新用户信息
  async refreshUserInfo() {
    try {
      // 静默刷新，不显示loading
      // const res = await api.getUserInfo();
      // this.setData({ userInfo: res.data });
    } catch (error) {
      console.error('刷新用户信息失败:', error);
    }
  },

  // 编辑个人资料
  editProfile() {
    wx.navigateTo({
      url: '/pages/profile-edit/profile-edit'
    });
  },

  // 查看影响力详情
  viewInfluence() {
    wx.showModal({
      title: '影响力说明',
      content: '影响力通过游戏表现、真心话评分、团队合作等方式获得，可用于坚持机制中改变剧情走向。',
      showCancel: false
    });
  },

  // 查看游戏记录
  viewGames() {
    wx.switchTab({
      url: '/pages/history/history'
    });
  },

  // 查看胜率详情
  viewWinRate() {
    const { wins, losses } = this.data.gameStats;
    const total = wins + losses;
    wx.showModal({
      title: '胜率详情',
      content: `总场次：${total}\n胜利：${wins}场\n失败：${losses}场\n胜率：${this.data.userInfo.winRate}%`,
      showCancel: false
    });
  },

  // 编辑昵称
  editNickname() {
    this.setData({ newNickname: this.data.userInfo.nickname });
    const dialog = this.selectComponent('#nickname-dialog');
    dialog.showDialog();
  },

  // 昵称输入变化
  onNicknameChange(e) {
    this.setData({ newNickname: e.detail.value });
  },

  // 确认修改昵称
  async confirmNickname() {
    const newNickname = this.data.newNickname.trim();
    
    if (!newNickname) {
      this.showToast('昵称不能为空');
      return;
    }

    if (newNickname.length < 2 || newNickname.length > 20) {
      this.showToast('昵称长度应在2-20个字符之间');
      return;
    }

    try {
      common.showLoading('修改中...');
      
      // 这里应该调用实际的API
      // await api.updateUserInfo({ nickname: newNickname });
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      this.setData({
        'userInfo.nickname': newNickname
      });
      
      common.hideLoading();
      this.showToast('昵称修改成功', 'success');
      
    } catch (error) {
      console.error('修改昵称失败:', error);
      common.hideLoading();
      this.showToast('修改失败，请重试');
    }
  },

  // 编辑头像
  editAvatar() {
    wx.showActionSheet({
      itemList: ['拍照', '从相册选择'],
      success: (res) => {
        const sourceType = res.tapIndex === 0 ? ['camera'] : ['album'];
        this.chooseImage(sourceType);
      }
    });
  },

  // 选择图片
  chooseImage(sourceType) {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType,
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        this.uploadAvatar(tempFilePath);
      }
    });
  },

  // 上传头像
  async uploadAvatar(filePath) {
    try {
      common.showLoading('上传中...');
      
      // 这里应该调用实际的上传API
      // const res = await api.uploadAvatar(filePath);
      
      // 模拟上传
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      this.setData({
        'userInfo.avatar': filePath
      });
      
      common.hideLoading();
      this.showToast('头像更新成功', 'success');
      
    } catch (error) {
      console.error('上传头像失败:', error);
      common.hideLoading();
      this.showToast('上传失败，请重试');
    }
  },

  // 通知设置变化
  async onNotificationChange(e) {
    const enabled = e.detail.value;
    
    try {
      // 这里应该调用API更新设置
      // await api.updateSettings({ notification: enabled });
      
      this.setData({
        'settings.notification': enabled
      });
      
      this.showToast(enabled ? '已开启通知' : '已关闭通知', 'success');
      
    } catch (error) {
      console.error('更新通知设置失败:', error);
      this.showToast('设置失败，请重试');
    }
  },

  // 通知设置
  notificationSettings() {
    wx.navigateTo({
      url: '/pages/notification-settings/notification-settings'
    });
  },

  // 隐私设置
  privacySettings() {
    wx.navigateTo({
      url: '/pages/privacy-settings/privacy-settings'
    });
  },

  // API设置
  apiSettings() {
    wx.navigateTo({
      url: '/pages/api-settings/api-settings'
    });
  },

  // 帮助与反馈
  helpAndFeedback() {
    wx.navigateTo({
      url: '/pages/help-feedback/help-feedback'
    });
  },

  // 关于我们
  aboutUs() {
    wx.navigateTo({
      url: '/pages/about/about'
    });
  },

  // 退出登录
  async logout() {
    const confirmed = await common.showConfirm('确定要退出登录吗？', '退出登录');
    
    if (confirmed) {
      try {
        common.showLoading('退出中...');
        
        // 清除本地存储的用户信息
        common.removeStorage('token');
        common.removeStorage('userInfo');
        
        // 这里应该调用API退出登录
        // await api.logout();
        
        common.hideLoading();
        
        // 跳转到登录页面
        wx.reLaunch({
          url: '/pages/login/login'
        });
        
      } catch (error) {
        console.error('退出登录失败:', error);
        common.hideLoading();
        this.showToast('退出失败，请重试');
      }
    }
  },

  // 显示提示
  showToast(message, theme = 'warning') {
    const toast = this.selectComponent('#t-toast');
    toast.showToast({
      theme,
      message,
      duration: 2000
    });
  }
});
