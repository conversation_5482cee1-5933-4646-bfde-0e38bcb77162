<!-- 人物关系页面 -->
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="nav-left" bindtap="goBack">
        <text class="nav-icon">←</text>
      </view>
      <view class="nav-title">🕸️ 人物关系</view>
      <view class="nav-right"></view>
    </view>
  </view>

  <!-- 页面内容 -->
  <view class="page-content">
    <!-- 关系图谱卡片 -->
    <view class="relations-card artistic-card">
      <view class="section-header">
        <text class="section-icon">🕸️</text>
        <text class="section-title">人物关系网络</text>
      </view>

      <!-- 简化的关系列表 -->
      <view class="relations-list">
        <view class="relation-item" wx:for="{{relations}}" wx:key="index">
          <view class="relation-participants">
            <view class="participant">
              <view class="participant-avatar">{{item.from.charAt(0)}}</view>
              <text class="participant-name">{{item.from}}</text>
            </view>
            <view class="relation-type {{item.type}}">{{item.relation}}</view>
            <view class="participant">
              <view class="participant-avatar">{{item.to.charAt(0)}}</view>
              <text class="participant-name">{{item.to}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

  </view>

  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <button class="action-btn secondary" bindtap="goBack">
      <text class="btn-icon">←</text>
      <text>返回</text>
    </button>
    <button class="action-btn primary" bindtap="continueToDiscussion" loading="{{loading}}">
      <text class="btn-icon">💬</text>
      <text>{{loading ? '准备中...' : '开始讨论'}}</text>
    </button>
  </view>
</view>