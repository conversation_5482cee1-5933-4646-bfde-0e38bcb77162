/* 投票页面样式 - 艺术化设计 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #2c1810 100%);
  position: relative;
  overflow: hidden;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

.container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 30%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(0, 191, 255, 0.08) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(145deg, rgba(26, 26, 46, 0.95), rgba(0, 0, 0, 0.8));
  backdrop-filter: blur(40rpx);
  border-bottom: 2rpx solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 40rpx;
  padding-top: calc(24rpx + env(safe-area-inset-top));
  height: 100rpx;
  position: relative;
}

.nav-left, .nav-right {
  width: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-icon {
  color: #ffffff;
  font-size: 40rpx;
  font-weight: bold;
  text-shadow: 0 0 20rpx rgba(255, 255, 255, 0.5);
}

.nav-title {
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 700;
  text-shadow: 
    0 0 10rpx rgba(255, 255, 255, 0.8),
    0 0 20rpx rgba(255, 255, 255, 0.6),
    0 0 30rpx rgba(255, 255, 255, 0.4);
}

.timer {
  color: #ffd700;
  font-size: 28rpx;
  font-weight: 700;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.3), rgba(255, 193, 7, 0.2));
  padding: 12rpx 20rpx;
  border-radius: 25rpx;
  border: 2rpx solid rgba(255, 215, 0, 0.4);
  box-shadow: 0 8rpx 24rpx rgba(255, 215, 0, 0.3);
  backdrop-filter: blur(10rpx);
}

.timer.urgent {
  color: #ff4d4f;
  background: linear-gradient(135deg, rgba(255, 77, 79, 0.3), rgba(255, 77, 79, 0.2));
  border-color: rgba(255, 77, 79, 0.4);
  box-shadow: 0 8rpx 24rpx rgba(255, 77, 79, 0.4);
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1);
  }
  50% { 
    opacity: 0.8; 
    transform: scale(1.05);
  }
}

/* 页面内容 - 优化布局 */
.page-content {
  padding: 160rpx 32rpx 180rpx;
  position: relative;
  z-index: 1;
  max-width: 750rpx;
  margin: 0 auto;
}

/* 艺术化卡片 */
.artistic-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(30rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 48rpx;
  padding: 48rpx;
  margin-bottom: 40rpx;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 24rpx 80rpx rgba(0, 0, 0, 0.15),
    0 8rpx 32rpx rgba(0, 0, 0, 0.1),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.artistic-card:hover {
  transform: translateY(-8rpx) scale(1.02);
  box-shadow:
    0 32rpx 100rpx rgba(0, 0, 0, 0.2),
    0 12rpx 40rpx rgba(0, 0, 0, 0.15);
}

/* 章节标题 - 优化字体 */
.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.section-icon {
  font-size: 52rpx;
  margin-right: 24rpx;
  filter: drop-shadow(0 0 20rpx rgba(255, 255, 255, 0.5));
}

.section-title {
  font-size: 48rpx;
  font-weight: 700;
  color: #ffffff;
  line-height: 1.4;
  text-shadow:
    0 0 10rpx rgba(255, 255, 255, 0.8),
    0 0 20rpx rgba(255, 255, 255, 0.6),
    0 0 30rpx rgba(255, 255, 255, 0.4);
}

/* 说明内容 - 优化可读性 */
.instruction-content {
  padding: 32rpx 0;
}

.instruction-text {
  font-size: 32rpx;
  line-height: 1.8;
  color: rgba(255, 255, 255, 0.95);
  text-align: justify;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5rpx;
}

/* 投票选项 - 参考原型设计 */
.voting-options {
  margin: 32rpx 0;
}

.vote-option {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(20rpx);
  border: 3rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 32rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.vote-option:hover {
  transform: translateY(-4rpx) scale(1.02);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);
}

.vote-option.selected {
  border-color: #4caf50;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.2) 0%, rgba(76, 175, 80, 0.1) 100%);
  transform: scale(1.02);
  box-shadow: 0 16rpx 48rpx rgba(76, 175, 80, 0.3);
}

/* 头像区域 */
.option-avatar {
  margin-right: 24rpx;
}

.avatar-circle {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(45deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 36rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

/* 选项信息 */
.option-info {
  flex: 1;
}

.option-name {
  font-size: 36rpx;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.option-role {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 16rpx;
}

/* 嫌疑度条 */
.suspicion-meter {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.suspicion-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  white-space: nowrap;
}

.suspicion-bar {
  flex: 1;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
}

.suspicion-fill {
  height: 100%;
  background: linear-gradient(90deg, #4caf50, #ff9800, #f44336);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.suspicion-value {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  min-width: 60rpx;
  text-align: right;
}

/* 票数显示 */
.option-votes {
  text-align: center;
  margin-left: 24rpx;
}

.vote-count {
  font-size: 32rpx;
  font-weight: 700;
  color: #ffd700;
  text-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.5);
}

.vote-label {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
}

/* 选中指示器 */
.selection-indicator {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(45deg, #4caf50, #388e3c);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.4);
}

.check-icon {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

/* 投票理由卡片 */
.reason-input {
  position: relative;
}

.reason-field {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 24rpx;
  color: white;
  font-size: 28rpx;
  line-height: 1.6;
  backdrop-filter: blur(10rpx);
  box-sizing: border-box;
}

.reason-field::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.reason-counter {
  position: absolute;
  bottom: 12rpx;
  right: 24rpx;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 40rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
  background: linear-gradient(145deg, rgba(26, 26, 46, 0.95), rgba(0, 0, 0, 0.8));
  backdrop-filter: blur(40rpx);
  border-top: 2rpx solid rgba(255, 255, 255, 0.1);
  display: flex;
  gap: 24rpx;
  z-index: 1000;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.3);
}

.action-btn {
  flex: 1;
  padding: 32rpx;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(20rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  position: relative;
  overflow: hidden;
}

.action-btn.primary {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333;
  box-shadow: 0 16rpx 48rpx rgba(255, 215, 0, 0.4);
}

.action-btn.secondary {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.action-btn:hover {
  transform: translateY(-8rpx) scale(1.05);
}

.btn-icon {
  font-size: 32rpx;
  filter: drop-shadow(0 2rpx 8rpx rgba(0, 0, 0, 0.3));
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(20rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 40rpx;
}

.modal-content {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
  backdrop-filter: blur(30rpx);
  border-radius: 32rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 24rpx 80rpx rgba(0, 0, 0, 0.3);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;
  border-bottom: 2rpx solid rgba(0, 0, 0, 0.1);
}

.modal-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #666;
  cursor: pointer;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 40rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.result-summary {
  text-align: center;
  margin-bottom: 32rpx;
}

.result-text {
  font-size: 32rpx;
  color: #333;
  line-height: 1.6;
}

.result-details {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 16rpx;
  padding: 24rpx;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.result-item:last-child {
  border-bottom: none;
}

.result-player {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.result-votes {
  font-size: 28rpx;
  color: #666;
}

.modal-footer {
  padding: 32rpx 40rpx;
  border-top: 2rpx solid rgba(0, 0, 0, 0.1);
}

.modal-btn {
  width: 100%;
  padding: 24rpx;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  cursor: pointer;
}

.modal-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

/* 动画效果 */
.animate-fadeIn {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 文字效果 */
.text-glow {
  text-shadow:
    0 0 10rpx rgba(255, 255, 255, 0.8),
    0 0 20rpx rgba(255, 255, 255, 0.6),
    0 0 30rpx rgba(255, 255, 255, 0.4);
}

.text-shadow {
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

/* 字体大小 */
.text-3xl {
  font-size: 48rpx;
}

.text-lg {
  font-size: 32rpx;
}

.font-bold {
  font-weight: 700;
}

.text-gray-100 {
  color: rgba(255, 255, 255, 0.95);
}

.mt-4 {
  margin-top: 32rpx;
}
