// test-rate-limit-fix.js
// 测试频率限制修复

const aiService = require('./utils/ai-service');

console.log('🧪 测试API频率限制修复');
console.log('========================');

async function testRateLimitFix() {
  console.log('\n1. 测试单个请求...');
  
  try {
    const startTime = Date.now();
    
    const result = await aiService.generateScript({
      theme: '测试主题',
      playerCount: 4,
      difficulty: 'easy'
    });
    
    const endTime = Date.now();
    console.log(`✅ 单个请求成功，耗时: ${endTime - startTime}ms`);
    console.log('响应数据:', result ? '有数据' : '无数据');
    
  } catch (error) {
    console.log('❌ 单个请求失败:', error.message);
  }
  
  console.log('\n2. 测试连续请求（应该有频率控制）...');
  
  for (let i = 1; i <= 3; i++) {
    try {
      console.log(`\n发送第 ${i} 个请求...`);
      const startTime = Date.now();
      
      const result = await aiService.sendRequest([
        { role: 'user', content: `这是测试请求 ${i}，请简单回复"收到"` }
      ], { maxTokens: 50 });
      
      const endTime = Date.now();
      console.log(`✅ 请求 ${i} 成功，耗时: ${endTime - startTime}ms`);
      
    } catch (error) {
      console.log(`❌ 请求 ${i} 失败:`, error.message);
      
      if (error.message.includes('频率')) {
        console.log('🎯 频率限制检测正常工作');
      }
    }
  }
}

// 运行测试
testRateLimitFix().then(() => {
  console.log('\n✅ 测试完成');
}).catch(error => {
  console.error('\n❌ 测试异常:', error);
});
