<!--AI剧本生成Loading页面-->
<view class="loading-container">
  <view class="loading-content">
    <!-- AI大脑图标 -->
    <view class="ai-brain">
      <t-icon name="brain" size="120rpx" color="#667eea" class="brain-icon" />
      <view class="pulse-ring"></view>
      <view class="pulse-ring delay-1"></view>
      <view class="pulse-ring delay-2"></view>
    </view>

    <!-- 标题 -->
    <view class="loading-title">AI正在创作剧本</view>
    <view class="loading-subtitle">基于你们的设置，AI正在生成独特的推理剧本...</view>

    <!-- 进度条 -->
    <view class="progress-section">
      <t-progress 
        percentage="{{progress}}" 
        theme="primary" 
        stroke-width="8rpx"
        show-info="{{true}}"
        class="progress-bar"
      />
      <view class="progress-text">生成进度: {{progress}}%</view>
    </view>

    <!-- 生成步骤 -->
    <view class="steps-section">
      <view class="step-item {{item.status}}" wx:for="{{steps}}" wx:key="id">
        <view class="step-icon">
          <t-icon 
            name="{{item.status === 'completed' ? 'check' : item.status === 'processing' ? 'loading' : 'time'}}" 
            size="32rpx"
            color="{{item.status === 'completed' ? '#52c41a' : item.status === 'processing' ? '#1890ff' : '#d9d9d9'}}"
          />
        </view>
        <view class="step-text">{{item.text}}</view>
      </view>
    </view>

    <!-- 提示信息 -->
    <view class="tips-section">
      <t-card class="tips-card">
        <view class="tips-content">
          <t-icon name="lightbulb" size="32rpx" color="#faad14" />
          <text class="tips-text">AI正在根据你们的偏好定制专属剧本，请稍候...</text>
        </view>
      </t-card>
    </view>
  </view>

  <!-- 底部装饰 -->
  <view class="decoration-dots">
    <view class="dot dot-1"></view>
    <view class="dot dot-2"></view>
    <view class="dot dot-3"></view>
    <view class="dot dot-4"></view>
    <view class="dot dot-5"></view>
  </view>
</view>

<!-- 错误提示 -->
<t-dialog 
  id="error-dialog"
  title="生成失败"
  content="{{errorMessage}}"
  confirm-btn="重试"
  cancel-btn="返回"
  bind:confirm="retryGeneration"
  bind:cancel="goBack"
/>

<t-toast id="t-toast" />
