<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内凹按钮效果测试</title>
    <style>
        body {
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'PingFang SC', sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .title {
            color: white;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 40px;
            text-align: center;
        }

        .button-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
            align-items: center;
        }

        /* 内凹按钮效果 - 模拟图片中的凹陷效果 */
        .inset-btn {
            border: none;
            border-radius: 40px;
            width: 200px;
            height: 60px;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            position: relative;
            overflow: hidden;
            color: #333333;
            
            /* 强烈的内凹效果 - 模拟按钮被按下的状态 */
            background: linear-gradient(145deg, #d0d0d0, #f0f0f0);
            box-shadow: 
                /* 强烈的内部阴影 - 创造深度凹陷感 */
                inset 8px 8px 16px #b8b8b8,
                inset -8px -8px 16px #ffffff,
                /* 轻微的外部阴影 - 保持边界清晰 */
                2px 2px 8px rgba(0,0,0,0.1);
            border: 1px solid rgba(0,0,0,0.1);
        }

        .inset-btn:hover {
            transform: translateY(-1px);
            /* 悬停时稍微减少内凹效果 */
            box-shadow: 
                inset 6px 6px 12px #b8b8b8,
                inset -6px -6px 12px #ffffff,
                3px 3px 10px rgba(0,0,0,0.15);
        }

        .inset-btn:active {
            transform: translateY(1px) scale(0.98);
            /* 按下时进一步加深内凹效果 */
            box-shadow: 
                /* 更深的内部阴影 - 模拟按钮被进一步按下 */
                inset 12px 12px 24px #a8a8a8,
                inset -12px -12px 24px #ffffff,
                /* 几乎消除外部阴影 */
                1px 1px 4px rgba(0,0,0,0.05);
        }

        .btn-icon {
            font-size: 20px;
            opacity: 0.8;
        }

        .comparison {
            margin-top: 40px;
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .normal-btn {
            border: none;
            border-radius: 40px;
            width: 150px;
            height: 50px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        }

        .normal-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0,0,0,0.4);
        }

        .label {
            color: white;
            font-size: 12px;
            text-align: center;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="title">内凹按钮效果对比</div>
    
    <div class="button-container">
        <button class="inset-btn">
            <span class="btn-icon">👑</span>
            创建房间
        </button>
        
        <button class="inset-btn">
            <span class="btn-icon">🎯</span>
            加入游戏
        </button>
    </div>

    <div class="comparison">
        <div>
            <button class="inset-btn" style="width: 150px; height: 50px; font-size: 14px;">
                内凹按钮
            </button>
            <div class="label">内凹效果</div>
        </div>
        
        <div>
            <button class="normal-btn">
                普通按钮
            </button>
            <div class="label">普通效果</div>
        </div>
    </div>
</body>
</html>
