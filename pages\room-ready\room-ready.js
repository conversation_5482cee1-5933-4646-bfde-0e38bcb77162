// 房间准备页面 - 模拟真实的游戏准备流程
const roomManager = require('../../utils/room-manager');
const aiService = require('../../utils/ai-service-simple');

Page({
  data: {
    roomId: '',
    roomInfo: null,
    players: [],
    allReady: false,
    isHost: false,
    currentUser: null,
    scriptGenerated: false,
    scriptData: null,
    loading: false,
    step: 1, // 1: 等待玩家, 2: 生成剧本, 3: 准备完成
    steps: [
      { id: 1, title: '等待玩家加入', desc: '需要足够的玩家才能开始游戏' },
      { id: 2, title: '生成游戏剧本', desc: 'AI正在为您生成专属剧本' },
      { id: 3, title: '准备开始游戏', desc: '所有准备工作已完成' }
    ]
  },

  onLoad(options) {
    if (options.roomId) {
      this.setData({ roomId: options.roomId });
      this.loadRoomInfo();
    } else {
      this.createNewRoom();
    }
  },

  // 创建新房间
  async createNewRoom() {
    try {
      this.setData({ loading: true });

      const roomResult = roomManager.createRoom({
        gameMode: '经典推理模式',
        maxPlayers: 6,
        timeLimit: 600,
        rounds: 3
      });

      this.setData({ 
        roomId: roomResult.roomId,
        isHost: true
      });

      // 添加测试玩家
      await this.addTestPlayers();
      
      this.loadRoomInfo();
      
    } catch (error) {
      console.error('创建房间失败:', error);
      wx.showToast({
        title: '创建房间失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 添加测试玩家
  async addTestPlayers() {
    const testPlayers = [
      { id: 'test_player_2', nickname: '艾米丽', avatar: '', isReady: false },
      { id: 'test_player_3', nickname: '詹姆斯', avatar: '', isReady: false },
      { id: 'test_player_4', nickname: '莉莉安', avatar: '', isReady: false },
      { id: 'test_player_5', nickname: '维克多', avatar: '', isReady: false },
      { id: 'test_player_6', nickname: '索菲亚', avatar: '', isReady: false }
    ];

    for (const player of testPlayers) {
      roomManager.joinRoom(this.data.roomId, player);
    }

    console.log('✅ 测试玩家添加完成');
  },

  // 加载房间信息
  loadRoomInfo() {
    try {
      const roomInfo = roomManager.getRoomInfo(this.data.roomId);
      const currentUser = roomManager.getCurrentUser();
      const players = Array.from(roomInfo.players.values());

      this.setData({
        roomInfo,
        currentUser,
        players,
        isHost: currentUser.isHost
      });

      this.checkGameReadiness();

    } catch (error) {
      console.error('加载房间信息失败:', error);
      wx.showToast({
        title: '房间不存在',
        icon: 'error'
      });
    }
  },

  // 检查游戏准备状态
  checkGameReadiness() {
    const { players } = this.data;
    
    // 检查玩家数量
    if (players.length >= 4) {
      this.setData({ step: 2 });
      
      // 如果还没生成剧本，自动生成
      if (!this.data.scriptGenerated) {
        this.generateScript();
      }
    }
  },

  // 生成剧本
  async generateScript() {
    try {
      this.setData({ loading: true });
      console.log('🎬 开始生成剧本...');

      const scriptResult = await aiService.generateScript({
        storyType: 'mystery',
        playerCount: this.data.players.length,
        difficulty: 'medium',
        theme: '神秘庄园'
      });

      // 使用静态方法提取剧本数据
      const scriptData = aiService.AIService.extractScriptData(scriptResult);

      // 保存剧本到本地存储
      wx.setStorageSync(`script_${this.data.roomId}`, scriptData);

      this.setData({
        scriptData,
        scriptGenerated: true,
        step: 3
      });

      console.log('✅ 剧本生成完成:', scriptData.storyInfo?.title);

    } catch (error) {
      console.error('剧本生成失败:', error);
      wx.showToast({
        title: '剧本生成失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 切换玩家准备状态
  togglePlayerReady(e) {
    const playerId = e.currentTarget.dataset.playerId;
    const players = this.data.players.map(player => {
      if (player.id === playerId) {
        return { ...player, isReady: !player.isReady };
      }
      return player;
    });

    this.setData({ players });

    // 检查是否所有玩家都准备好了
    const allReady = players.every(player => player.isReady);
    this.setData({ allReady });
  },

  // 开始游戏
  startGame() {
    if (!this.data.scriptGenerated) {
      wx.showToast({
        title: '剧本还未生成完成',
        icon: 'none'
      });
      return;
    }

    if (!this.data.allReady) {
      wx.showToast({
        title: '请等待所有玩家准备',
        icon: 'none'
      });
      return;
    }

    // 跳转到角色分配页面
    wx.redirectTo({
      url: `/pages/role-assignment/role-assignment?roomId=${this.data.roomId}`
    });
  },

  // 模拟所有玩家准备
  makeAllReady() {
    const players = this.data.players.map(player => ({
      ...player,
      isReady: true
    }));

    this.setData({ 
      players,
      allReady: true
    });

    wx.showToast({
      title: '所有玩家已准备',
      icon: 'success'
    });
  },

  // 查看剧本预览
  previewScript() {
    if (!this.data.scriptData) {
      wx.showToast({
        title: '剧本还未生成',
        icon: 'none'
      });
      return;
    }

    const script = this.data.scriptData;
    const content = `标题: ${script.storyInfo?.title || '未知'}\n\n背景: ${script.storyInfo?.background || '未知'}\n\n角色数量: ${script.characters?.length || 0}个`;

    wx.showModal({
      title: '剧本预览',
      content: content,
      showCancel: false
    });
  },

  // 返回主页
  goHome() {
    wx.navigateBack();
  }
});
