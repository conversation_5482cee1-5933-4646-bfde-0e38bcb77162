/* 测试准备开始游戏功能页面样式 */
.container {
  padding: 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 控制按钮 */
.controls {
  display: flex;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.control-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

.control-btn.start {
  background: rgba(76, 175, 80, 0.9);
  color: white;
}

.control-btn.start[disabled] {
  background: rgba(158, 158, 158, 0.5);
  color: rgba(255, 255, 255, 0.6);
}

.control-btn.clear {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.control-btn.clear[disabled] {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 255, 255, 0.1);
}

/* 进度条 */
.progress-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
}

.progress-label {
  color: white;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.progress-bar {
  height: 12rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

/* 当前测试 */
.current-test {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
}

.current-test-label {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.current-test-name {
  display: block;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

/* 测试结果 */
.test-results {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid rgba(255, 255, 255, 0.2);
}

.results-title {
  color: white;
  font-size: 36rpx;
  font-weight: bold;
}

.results-summary {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
}

.result-item {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15rpx;
}

.result-item:last-child {
  margin-bottom: 0;
}

.result-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.result-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.result-name {
  flex: 1;
  color: white;
  font-size: 28rpx;
  font-weight: bold;
}

.result-time {
  color: rgba(255, 255, 255, 0.6);
  font-size: 24rpx;
}

.result-message {
  color: rgba(255, 255, 255, 0.8);
  font-size: 26rpx;
  line-height: 1.4;
}

/* 测试说明 */
.test-info {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
}

.info-title {
  display: block;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.info-item {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
  line-height: 1.4;
}

/* 最终结果 */
.final-result {
  margin-top: 40rpx;
}

.result-card {
  text-align: center;
  padding: 40rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.result-card.success {
  background: rgba(76, 175, 80, 0.2);
  border: 2rpx solid rgba(76, 175, 80, 0.5);
}

.result-card.warning {
  background: rgba(255, 152, 0, 0.2);
  border: 2rpx solid rgba(255, 152, 0, 0.5);
}

.result-emoji {
  display: block;
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.result-text {
  display: block;
  color: white;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.result-detail {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
}
