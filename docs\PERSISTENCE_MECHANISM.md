# 个人坚持机制实现文档

## 概述

个人坚持机制是AI推理游戏中的核心创新功能，允许玩家在集体投票结束后，使用个人影响力来改变剧情走向或开启个人专属剧情线。该机制结合了原型设计中的UI元素和AI智能生成的内容，为玩家提供了丰富的策略选择。

## 核心特性

### 1. 影响力系统
- **基础影响力**: 根据角色地位等级确定（低等30-40点，中等40-60点，高等60-80点，极高80-100点）
- **表现加成**: 基于玩家在游戏中的行为表现动态调整
- **消耗机制**: 使用坚持机制会消耗相应的影响力点数
- **等级系统**: 影响力等级决定可使用的坚持机制类型

### 2. 坚持机制选择层次

#### 跟随集体选择（无消耗）
- 接受多数人的决定
- 剧情按集体选择发展
- 保持团队和谐，获得少量影响力奖励

#### 轻度坚持（消耗15-25影响力）
- 在集体选择基础上添加个人色彩
- 获得额外的个人线索或信息
- 不改变主要剧情走向

#### 强力坚持（消耗30-50影响力）
- 完全改变剧情发展方向
- 开启个人专属剧情线
- 可能影响其他玩家的体验

#### 极限坚持（消耗60-80影响力）
- 颠覆性的剧情转折
- 开启隐藏剧情分支
- 获得游戏决定性优势
- 需要高地位角色才能使用

## 技术实现

### 1. AI提示词系统

#### 个人坚持机制提示词 (`getPersistencePrompt`)
```javascript
// 位置: utils/ai-prompts.js
static getPersistencePrompt(persistenceContext) {
  // 生成基于上下文的坚持机制选择
  // 包含角色背景、当前影响力、集体投票结果等信息
}
```

#### 影响力计算提示词 (`getInfluenceCalculationPrompt`)
```javascript
// 位置: utils/ai-prompts.js  
static getInfluenceCalculationPrompt(influenceContext) {
  // 计算玩家当前影响力和可用选择
  // 基于游戏表现、角色地位等因素
}
```

### 2. AI服务方法

#### 生成坚持机制选择 (`generatePersistenceOptions`)
```javascript
// 位置: utils/ai-service.js
async generatePersistenceOptions(persistenceContext) {
  // 调用AI API生成个性化的坚持机制选择
  // 包含默认选择的fallback机制
}
```

#### 计算影响力 (`calculateInfluence`)
```javascript
// 位置: utils/ai-service.js
async calculateInfluence(influenceContext) {
  // 使用AI计算玩家影响力
  // 提供详细的影响力分析和建议
}
```

### 3. 演示页面实现

#### 页面结构
- **状态卡片**: 显示当前游戏状态和玩家信息
- **坚持机制面板**: 展示所有可选的坚持机制选项
- **操作按钮**: AI生成、影响力计算、倒计时等功能
- **演示配置**: 不同角色和影响力等级的快速切换

#### 核心功能
- **实时选择**: 玩家可以实时选择不同的坚持机制选项
- **影响力检查**: 自动检查玩家是否有足够影响力使用某个选项
- **倒计时机制**: 模拟真实游戏中的时间限制
- **动画效果**: 丰富的UI动画提升用户体验

## UI设计特色

### 1. 视觉设计
- **深空主题**: 使用深蓝色渐变背景，营造神秘氛围
- **金色强调**: 使用金色作为主要强调色，突出重要信息
- **毛玻璃效果**: 使用backdrop-filter实现现代化的毛玻璃效果
- **渐变边框**: 使用渐变边框增强视觉层次

### 2. 交互设计
- **选择反馈**: 选中状态有明显的视觉反馈
- **禁用状态**: 影响力不足的选项会显示禁用状态
- **动画过渡**: 所有状态变化都有平滑的动画过渡
- **响应式布局**: 适配不同屏幕尺寸

### 3. 信息架构
- **层次清晰**: 信息按重要性分层展示
- **易于扫描**: 使用图标和颜色编码便于快速识别
- **详细说明**: 每个选项都有详细的描述和效果说明

## 游戏平衡设计

### 1. 影响力平衡
- **获得机制**: 通过积极参与游戏获得影响力
- **消耗机制**: 使用坚持机制需要消耗影响力
- **风险回报**: 高消耗的选择提供更大的游戏优势

### 2. 选择平衡
- **多样性**: 提供4个不同层次的选择
- **适用性**: 不同影响力等级的玩家都有合适的选择
- **策略性**: 玩家需要权衡风险和收益

### 3. 公平性保障
- **角色平等**: 所有角色都有机会获得影响力
- **表现导向**: 影响力主要基于游戏表现而非角色设定
- **动态调整**: 系统会根据游戏进程动态调整平衡

## 使用指南

### 1. 开发者使用
```javascript
// 生成坚持机制选择
const persistenceOptions = await aiService.generatePersistenceOptions({
  voteResult: '深入调查神秘访客',
  playerRole: '管家',
  currentInfluence: 65,
  roleStatus: '中等',
  currentRound: 2,
  plotImportance: '高'
});

// 计算影响力
const influenceResult = await aiService.calculateInfluence({
  characterName: '管家',
  roleStatus: '中等',
  currentInfluence: 65,
  performanceScore: 75
});
```

### 2. 玩家使用
1. 查看当前影响力和游戏状态
2. 了解集体投票结果
3. 选择合适的坚持机制选项
4. 考虑风险和收益
5. 在时间限制内确认选择

### 3. 演示页面访问
- 在首页开发测试区域点击"坚持机制演示"按钮
- 或直接访问 `/pages/persistence-demo/persistence-demo`

## 扩展可能性

### 1. 功能扩展
- **团队坚持**: 多个玩家联合使用坚持机制
- **条件坚持**: 基于特定条件触发的坚持选项
- **时间坚持**: 延迟生效的坚持机制

### 2. AI优化
- **学习机制**: AI根据玩家历史选择优化推荐
- **个性化**: 基于玩家偏好生成个性化选项
- **动态难度**: 根据玩家水平调整选择复杂度

### 3. 社交功能
- **影响力排行**: 显示玩家影响力排行榜
- **选择统计**: 统计不同选择的使用频率
- **成就系统**: 基于坚持机制使用情况的成就

## 总结

个人坚持机制成功地将原型设计中的UI概念与AI智能生成相结合，创造了一个既有策略深度又有技术创新的游戏功能。通过精心设计的影响力系统和多层次的选择机制，为玩家提供了丰富的游戏体验和策略选择空间。
