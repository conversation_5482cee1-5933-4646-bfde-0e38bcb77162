// 首页逻辑
Page({
  data: {
    loading: false,
    roomIdInput: '',
    showDevTest: false, // 开发者测试区域显示状态
    roomList: [
      {
        id: 1,
        name: '🏰 神秘庄园',
        roomId: '123456',
        currentPlayers: 5,
        maxPlayers: 6,
        gameMode: '剧情推理模式',
        status: 'playing',
        difficulty: 'hard',
        difficultyText: '高难度',
        hasVoice: true,
        isNewbie: true,
        hasAI: false
      },
      {
        id: 2,
        name: '🏛️ 古堡疑云',
        roomId: '789012',
        currentPlayers: 4,
        maxPlayers: 8,
        gameMode: '团队合作模式',
        status: 'waiting',
        difficulty: 'medium',
        difficultyText: '中等难度',
        hasVoice: true,
        isNewbie: false,
        hasAI: true
      },
      {
        id: 3,
        name: '🏫 校园悬案',
        roomId: '345678',
        currentPlayers: 3,
        maxPlayers: 6,
        gameMode: '快速推理模式',
        status: 'waiting',
        difficulty: 'easy',
        difficultyText: '简单难度',
        hasVoice: false,
        isNewbie: true,
        hasAI: true
      }
    ]
  },

  onLoad() {
    this.loadRoomList();
  },

  onShow() {
    // 页面显示时刷新房间列表
    this.loadRoomList();
  },

  onPullDownRefresh() {
    this.loadRoomList().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载房间列表
  async loadRoomList() {
    try {
      this.setData({ loading: true });
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 这里应该调用实际的API
      // const res = await api.getRoomList();
      // this.setData({ roomList: res.data });
      
      this.setData({ loading: false });
    } catch (error) {
      console.error('加载房间列表失败:', error);
      this.setData({ loading: false });
      this.showToast('加载失败，请重试');
    }
  },

  // 创建房间
  createRoom() {
    // 如果是开发测试模式，显示快速选项
    if (this.data.showDevTest) {
      wx.showActionSheet({
        itemList: ['正常创建房间', '快速创建房间（测试）'],
        success: (res) => {
          if (res.tapIndex === 0) {
            wx.navigateTo({
              url: '/pages/room-create/room-create'
            });
          } else if (res.tapIndex === 1) {
            this.quickCreateRoom();
          }
        }
      });
    } else {
      wx.navigateTo({
        url: '/pages/room-create/room-create'
      });
    }
  },

  // 加入房间
  joinRoom() {
    wx.navigateTo({
      url: '/pages/room-join/room-join'
    });
  },

  // 通过房间ID加入
  joinRoomById(e) {
    const roomId = e.currentTarget.dataset.roomId;
    const room = this.data.roomList.find(r => r.id === roomId);
    
    if (!room) {
      this.showToast('房间不存在');
      return;
    }

    if (room.currentPlayers >= room.maxPlayers) {
      this.showToast('房间已满');
      return;
    }

    // 跳转到房间大厅
    wx.navigateTo({
      url: `/pages/room-lobby/room-lobby?roomId=${room.roomId}`
    });
  },

  // 快速加入房间
  quickJoinRoom() {
    const roomId = this.data.roomIdInput.trim();
    if (!roomId) {
      this.showToast('请输入房间号');
      return;
    }

    if (roomId.length !== 6) {
      this.showToast('房间号应为6位数字');
      return;
    }

    // 验证房间号并加入
    this.joinRoomByCode(roomId);
  },

  // 通过房间号加入
  async joinRoomByCode(roomId) {
    try {
      this.setData({ loading: true });
      
      // 模拟API调用验证房间号
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 这里应该调用实际的API验证房间号
      // const res = await api.verifyRoomId(roomId);
      
      this.setData({ loading: false });
      
      // 跳转到房间大厅
      wx.navigateTo({
        url: `/pages/room-lobby/room-lobby?roomId=${roomId}`
      });
    } catch (error) {
      console.error('加入房间失败:', error);
      this.setData({ loading: false });
      this.showToast('房间不存在或已满');
    }
  },

  // 扫码加入
  scanQRCode() {
    wx.scanCode({
      success: (res) => {
        const roomId = this.extractRoomIdFromQR(res.result);
        if (roomId) {
          this.joinRoomByCode(roomId);
        } else {
          this.showToast('无效的二维码');
        }
      },
      fail: () => {
        this.showToast('扫码失败');
      }
    });
  },

  // 从二维码结果中提取房间号
  extractRoomIdFromQR(qrResult) {
    // 这里应该根据实际的二维码格式来解析
    const match = qrResult.match(/roomId=(\d{6})/);
    return match ? match[1] : null;
  },

  // 房间号输入变化
  onRoomIdChange(e) {
    this.setData({
      roomIdInput: e.detail.value
    });
  },

  // 显示输入对话框
  showInputDialog() {
    // 这里可以使用自定义的输入对话框组件
    this.showToast('请在下方输入框中输入房间号');
  },

  // 显示提示
  showToast(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  },

  // 显示对话框
  showDialog(options) {
    wx.showModal(options);
  },

  // 切换开发者测试区域显示
  toggleDevTest() {
    this.setData({
      showDevTest: !this.data.showDevTest
    });
  },

  // 测试角色分配页面
  async testRoleAssignment() {
    try {
      // 创建一个真实的房间用于测试
      const roomManager = require('../../utils/room-manager');

      // 创建房间
      const roomResult = roomManager.createRoom({
        gameMode: '经典推理模式',
        maxPlayers: 6,
        timeLimit: 600,
        rounds: 3
      });

      // 添加测试玩家
      const testPlayers = [
        { id: 'test_player_2', nickname: '测试玩家2', avatar: '', isReady: true },
        { id: 'test_player_3', nickname: '测试玩家3', avatar: '', isReady: true },
        { id: 'test_player_4', nickname: '测试玩家4', avatar: '', isReady: true },
        { id: 'test_player_5', nickname: '测试玩家5', avatar: '', isReady: true },
        { id: 'test_player_6', nickname: '测试玩家6', avatar: '', isReady: true }
      ];

      for (const player of testPlayers) {
        roomManager.joinRoom(roomResult.roomId, player);
      }

      // 生成测试剧本
      const aiService = require('../../utils/ai-service-simple');
      const scriptData = await aiService.generateScript({
        storyType: 'mystery',
        playerCount: 6,
        difficulty: 'medium',
        theme: '神秘庄园'
      });

      // 保存剧本到本地存储
      wx.setStorageSync(`script_${roomResult.roomId}`, scriptData);

      console.log('✅ 测试环境准备完成，房间ID:', roomResult.roomId);

      // 跳转到角色分配页面
      wx.navigateTo({
        url: `/pages/role-assignment/role-assignment?roomId=${roomResult.roomId}`
      });

    } catch (error) {
      console.error('❌ 创建测试环境失败:', error);
      wx.showToast({
        title: '创建测试环境失败',
        icon: 'error'
      });
    }
  },

  // 测试角色分配功能
  testRoleAssignmentFunction() {
    wx.navigateTo({
      url: '/pages/test-role-assignment/test-role-assignment'
    });
  },

  // 简单角色测试
  simpleRoleTest() {
    wx.navigateTo({
      url: '/pages/simple-role-test/simple-role-test'
    });
  },

  // 测试房间准备流程
  testRoomReady() {
    wx.navigateTo({
      url: '/pages/room-ready/room-ready'
    });
  },

  // 快速单人测试模式
  quickSinglePlayerTest() {
    console.log('🚀 点击了单人测试模式按钮');

    // 先显示一个简单的提示，确认按钮点击有效
    wx.showModal({
      title: '🎮 单人测试模式',
      content: '将体验完整的剧本杀流程：\n\n📝 AI剧情生成 - 选择类型和主题\n🎭 智能角色分配 - 获得专属角色\n🔍 动态线索收集 - AI生成个性化线索',
      confirmText: '开始体验',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.startSinglePlayerFlow();
        }
      }
    });
  },

  // 开始单人测试流程
  startSinglePlayerFlow() {
    // 生成唯一的测试房间ID
    const testRoomId = 'single_test_' + Date.now();

    // 跳转到AI剧情生成页面（整合了故事生成器功能）
    wx.navigateTo({
      url: `/pages/ai-story-generator/ai-story-generator?roomId=${testRoomId}&mode=singleTest`,
      success: () => {
        console.log('✅ 跳转到AI剧情生成页面成功');
      },
      fail: (error) => {
        console.error('❌ 跳转失败:', error);
        wx.showToast({
          title: '跳转失败',
          icon: 'error'
        });
      }
    });
  },

  // 创建单人测试环境
  async createSinglePlayerTestEnvironment() {
    try {
      console.log('🏠 开始创建单人测试环境...');

      // 检查房间管理器是否可用
      const roomManager = require('../../utils/room-manager');
      if (!roomManager) {
        throw new Error('房间管理器加载失败');
      }

      // 创建房间
      const roomResult = roomManager.createRoom({
        gameMode: '经典推理模式',
        maxPlayers: 6,
        timeLimit: 600,
        rounds: 3
      });

      if (!roomResult || !roomResult.roomId) {
        throw new Error('房间创建失败');
      }

      console.log('✅ 房间创建成功:', roomResult.roomId);

      // 添加AI测试玩家
      const testPlayers = [
        { id: 'ai_player_2', nickname: 'AI-艾米丽', avatar: '', isReady: true },
        { id: 'ai_player_3', nickname: 'AI-詹姆斯', avatar: '', isReady: true },
        { id: 'ai_player_4', nickname: 'AI-莉莉安', avatar: '', isReady: true },
        { id: 'ai_player_5', nickname: 'AI-维克多', avatar: '', isReady: true },
        { id: 'ai_player_6', nickname: 'AI-索菲亚', avatar: '', isReady: true }
      ];

      for (const player of testPlayers) {
        roomManager.joinRoom(roomResult.roomId, player);
      }

      console.log('✅ AI测试玩家添加完成');

      // 隐藏加载提示
      wx.hideLoading();

      // 跳转到房间大厅，并传递测试模式参数
      wx.navigateTo({
        url: `/pages/room-lobby/room-lobby?roomId=${roomResult.roomId}&testMode=single`,
        success: () => {
          console.log('✅ 跳转到房间大厅成功');
        },
        fail: (error) => {
          console.error('❌ 跳转失败:', error);
          wx.showToast({
            title: '跳转失败',
            icon: 'error'
          });
        }
      });

    } catch (error) {
      // 隐藏加载提示
      wx.hideLoading();

      console.error('❌ 创建单人测试环境失败:', error);
      wx.showToast({
        title: `创建失败: ${error.message}`,
        icon: 'error',
        duration: 3000
      });
    }
  },

  // 完整流程测试
  testCompleteFlow() {
    wx.navigateTo({
      url: '/pages/test-complete-flow/test-complete-flow'
    });
  },

  // 测试私人线索页面
  testPrivateClues() {
    wx.navigateTo({
      url: '/pages/private-clues/private-clues?roomId=test123'
    });
  },

  // 测试讨论阶段页面
  testDiscussion() {
    wx.navigateTo({
      url: '/pages/discussion/discussion?roomId=test123'
    });
  },

  // 测试投票页面
  testVoting() {
    wx.navigateTo({
      url: '/pages/voting/voting?roomId=test123'
    });
  },

  // 快速创建房间（用于测试）
  quickCreateRoom() {
    const roomId = Math.floor(100000 + Math.random() * 900000).toString();
    wx.navigateTo({
      url: `/pages/room-lobby/room-lobby?roomId=${roomId}`
    });
  },

  // 测试AI剧情生成页面
  testAIStoryGenerator() {
    const roomId = Math.floor(100000 + Math.random() * 900000).toString();
    wx.navigateTo({
      url: `/pages/ai-story-generator/ai-story-generator?roomId=${roomId}&maxPlayers=6`
    });
  },

  // 测试个人坚持机制演示页面
  testPersistenceDemo() {
    wx.navigateTo({
      url: '/pages/persistence-demo/persistence-demo'
    });
  },

  // 测试网络诊断页面
  testNetworkDiagnostic() {
    wx.navigateTo({
      url: '/pages/network-test/network-test'
    });
  },

  // 测试房间功能
  testRoomFunction() {
    wx.navigateTo({
      url: '/pages/test-room/test-room'
    });
  },

  // 测试准备开始游戏功能
  testReadyGameFunction() {
    wx.navigateTo({
      url: '/pages/test-ready-game/test-ready-game'
    });
  },

  // 手动测试指南
  manualTestGuide() {
    wx.navigateTo({
      url: '/pages/manual-test-guide/manual-test-guide'
    });
  },

  // 线索收集测试
  testCluesCollection() {
    wx.navigateTo({
      url: '/pages/test-clues/test-clues'
    });
  }
});
