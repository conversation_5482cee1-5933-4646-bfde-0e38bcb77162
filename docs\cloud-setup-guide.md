# ☁️ 微信云开发设置指南

## 🎯 **目标**

通过微信云开发安全地调用Moonshot AI API，避免在客户端暴露API密钥。

## 📋 **设置步骤**

### 第1步: 开通云开发

1. **打开微信开发者工具**
   - 确保使用最新版本的微信开发者工具
   - 打开你的小程序项目

2. **开通云开发**
   - 点击工具栏的"云开发"按钮
   - 如果是第一次使用，点击"开通"
   - 选择"按量付费"（新用户有免费额度）

3. **创建云环境**
   - 环境名称：`party-game-ai`
   - 环境ID会自动生成，类似：`party-game-ai-7g9k8m2h0c8e5f3a`
   - **重要**: 确保环境与你的小程序AppID (`wxe5962f9a3d2cdd06`) 关联
   - 记录这个环境ID，需要在代码中使用

### 🚨 **AppID关联检查**

如果遇到 `-501000` 错误，说明环境ID与小程序AppID不匹配：

1. **检查当前AppID**: `wxe5962f9a3d2cdd06`
2. **检查环境关联**: 在云开发控制台确认环境是否关联到正确的小程序
3. **可用的环境ID**:
   - `cloud1-7ggoe4se871c08ae`
   - `cloud1-1g88tqlcd2222f2e`

### 第2步: 配置环境变量

1. **进入云开发控制台**
   - 在微信开发者工具中点击"云开发"
   - 或访问：https://console.cloud.tencent.com/tcb

2. **设置环境变量**
   - 点击左侧菜单"环境" → "环境变量"
   - 点击"添加变量"
   - 变量名：`MOONSHOT_API_KEY`
   - 变量值：`sk-rFun7AywY7jUUdJAtUBbFD`
   - 点击"确定"

### 第3步: 部署云函数

1. **在微信开发者工具中**
   - 右键点击 `cloudfunctions/ai-service` 文件夹
   - 选择"上传并部署：云端安装依赖"
   - 等待部署完成

2. **验证部署**
   - 在云开发控制台的"云函数"页面
   - 应该能看到 `ai-service` 函数
   - 状态显示为"正常"

### 第4步: 更新应用配置

1. **修改环境ID**
   - 打开 `app.js` 文件
   - 找到这行代码：
   ```javascript
   env: 'party-game-ai-7g9k8m2h0c8e5f3a', // 请替换为你的实际环境ID
   ```
   - 替换为你的实际环境ID

2. **测试云开发**
   - 重新编译小程序
   - 查看控制台是否显示"☁️ 云开发初始化成功"

### 第5步: 测试AI服务

1. **在小程序中测试**
   - 进入"个人中心" → "API设置"
   - 点击"前往测试页面"
   - 点击"测试AI服务"
   - 应该显示云服务连接成功

## 🔧 **故障排除**

### 问题1: 云开发初始化失败

**症状**: 控制台显示"云开发初始化失败"

**解决方法**:
1. 检查基础库版本是否 ≥ 2.2.3
2. 确认已开通云开发服务
3. 检查环境ID是否正确

### 问题2: 云函数调用失败

**症状**: 显示"云函数未部署或不存在"

**解决方法**:
1. 确认云函数已正确部署
2. 检查函数名称是否为 `ai-service`
3. 在云开发控制台查看函数状态

### 问题3: API密钥错误

**症状**: 云函数返回"API密钥无效"

**解决方法**:
1. 检查环境变量 `MOONSHOT_API_KEY` 是否正确设置
2. 确认API密钥有效且有余额
3. 重新部署云函数

### 问题4: 权限错误

**症状**: 显示"权限不足"或"访问被拒绝"

**解决方法**:
1. 确认小程序已关联云开发环境
2. 检查云函数权限设置
3. 确认用户已登录（如果需要）

## 💰 **费用说明**

### 免费额度（每月）
- **云函数调用**: 100万次
- **云函数资源使用量**: 40万GBs
- **云存储**: 5GB
- **CDN流量**: 5GB

### 预估费用
- **AI服务调用**: 主要费用来自Moonshot AI API
- **云函数费用**: 基本可以在免费额度内
- **建议**: 先使用免费额度测试，根据实际使用量决定是否升级

## 🔒 **安全优势**

### 相比客户端直接调用
- ✅ **API密钥安全**: 密钥存储在云端，客户端无法访问
- ✅ **访问控制**: 可以添加用户认证和权限控制
- ✅ **使用监控**: 可以监控和限制API使用量
- ✅ **成本控制**: 可以设置使用配额和告警

### 额外安全措施
- 可以添加请求频率限制
- 可以记录所有API调用日志
- 可以实施用户级别的配额管理
- 可以添加内容过滤和审核

## 📊 **监控和日志**

### 查看云函数日志
1. 进入云开发控制台
2. 点击"云函数" → "ai-service"
3. 查看"日志"选项卡
4. 可以看到所有调用记录和错误信息

### 监控API使用
1. 在云函数中添加使用统计
2. 记录每次API调用的成本
3. 设置异常使用告警

## 🚀 **下一步优化**

### 功能增强
- [ ] 添加用户认证
- [ ] 实施请求频率限制
- [ ] 添加内容缓存机制
- [ ] 实现多模型支持

### 性能优化
- [ ] 优化云函数冷启动
- [ ] 添加请求重试机制
- [ ] 实现智能负载均衡

### 安全加固
- [ ] 添加请求签名验证
- [ ] 实施IP白名单
- [ ] 添加异常检测

## 📞 **获取帮助**

### 官方文档
- [微信云开发文档](https://developers.weixin.qq.com/miniprogram/dev/wxcloud/basis/getting-started.html)
- [云函数开发指南](https://developers.weixin.qq.com/miniprogram/dev/wxcloud/guide/functions/)

### 社区支持
- 微信开发者社区
- 云开发官方QQ群

---

**🎉 完成以上步骤后，你的AI服务就完全安全了！API密钥不再暴露在客户端，所有调用都通过安全的云函数进行。**
