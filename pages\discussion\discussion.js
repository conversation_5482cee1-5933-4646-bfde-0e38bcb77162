// 讨论阶段页面逻辑
const api = require('../../utils/api');
const common = require('../../utils/common');

Page({
  data: {
    roomId: '',
    activeTab: 'all',
    currentRound: 1,
    timeLeft: 300, // 5分钟，以秒为单位
    timeLeftDisplay: '05:00',
    currentUserId: 'current',
    inputText: '',
    clueContent: '',
    scrollTop: 0,
    scrollIntoView: '',
    isMuted: false,
    voiceInputMode: false,
    currentUser: {
      id: 'current',
      nickname: '我',
      avatar: ''
    },
    allMessages: [
      {
        id: 1,
        senderId: 'user1',
        sender: { nickname: '张三', avatar: '' },
        type: 'text',
        content: '大家好，我觉得这个案子很有意思',
        timestamp: Date.now() - 300000,
        timeStr: '5分钟前'
      },
      {
        id: 2,
        senderId: 'user2',
        sender: { nickname: '李四', avatar: '' },
        type: 'clue',
        content: '我在书房发现了一张纸条',
        timestamp: Date.now() - 240000,
        timeStr: '4分钟前'
      },
      {
        id: 3,
        senderId: 'current',
        type: 'text',
        content: '我也有一些发现，等会分享给大家',
        timestamp: Date.now() - 180000,
        timeStr: '3分钟前'
      }
    ],
    players: [
      {
        id: 'user1',
        nickname: '张三',
        avatar: '',
        lastMessage: '我觉得凶手可能是...',
        unreadCount: 2,
        isSpeaking: false,
        isMuted: false
      },
      {
        id: 'user2',
        nickname: '李四',
        avatar: '',
        lastMessage: '我有重要线索',
        unreadCount: 0,
        isSpeaking: true,
        isMuted: false
      },
      {
        id: 'user3',
        nickname: '王五',
        avatar: '',
        lastMessage: null,
        unreadCount: 1,
        isSpeaking: false,
        isMuted: true
      }
    ]
  },

  onLoad(options) {
    console.log('讨论页面加载参数:', options);

    // 设置房间ID和轮次
    const roomId = options.roomId || 'test_room_001';
    const currentRound = parseInt(options.round) || 1;

    this.setData({
      roomId: roomId,
      currentRound: currentRound
    });

    console.log('设置数据:', { roomId, currentRound });

    // 开始倒计时
    this.startTimer();

    // 加载聊天记录
    this.loadChatHistory();

    // 滚动到底部
    this.scrollToBottom();
  },

  onUnload() {
    // 清理定时器
    this.clearTimer();
  },

  // 开始倒计时
  startTimer() {
    let totalSeconds = this.data.timeLeft; // 使用data中的初始时间

    this.timer = setInterval(() => {
      totalSeconds--;

      if (totalSeconds <= 0) {
        this.clearTimer();
        this.timeUp();
        return;
      }

      // 更新timeLeft为秒数，供formatTime使用
      this.setData({ timeLeft: totalSeconds });
    }, 1000);
  },

  // 清理定时器
  clearTimer() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },

  // 时间到
  timeUp() {
    wx.showModal({
      title: '讨论时间结束',
      content: '讨论阶段已结束，即将进入剧情分支投票环节',
      showCancel: false,
      success: () => {
        wx.redirectTo({
          url: `/pages/plot-voting/plot-voting?roomId=${this.data.roomId}&round=${this.data.currentRound}`
        });
      }
    });
  },

  // 加载聊天记录
  async loadChatHistory() {
    try {
      // const res = await api.getChatHistory(this.roomId);
      // this.setData({ allMessages: res.data });
      // this.scrollToBottom();
    } catch (error) {
      console.error('加载聊天记录失败:', error);
    }
  },

  // 标签页切换
  onTabChange(e) {
    this.setData({ activeTab: e.detail.value });
  },

  // 新的标签页切换方法
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ activeTab: tab });
  },

  // 输入内容变化
  onInputChange(e) {
    this.setData({ inputText: e.detail.value });
  },

  // 发送消息
  async sendMessage() {
    const content = this.data.inputText.trim();
    if (!content) return;

    try {
      const message = {
        id: Date.now(),
        senderId: this.data.currentUserId,
        type: 'text',
        content,
        timestamp: Date.now(),
        timeStr: '刚刚'
      };

      // 添加到消息列表
      const allMessages = [...this.data.allMessages, message];
      this.setData({
        allMessages,
        inputText: ''
      });

      // 发送到服务器
      // await api.sendMessage(this.roomId, message);

      // 滚动到底部
      this.scrollToBottom();

    } catch (error) {
      console.error('发送消息失败:', error);
      this.showToast('发送失败，请重试');
    }
  },

  // 分享线索
  shareClue() {
    this.setData({
      clueContent: '',
      showClueDialog: true
    });
  },

  // 关闭线索弹窗
  closeClueDialog() {
    this.setData({ showClueDialog: false });
  },

  // 线索内容变化
  onClueContentChange(e) {
    this.setData({ clueContent: e.detail.value });
  },

  // 确认分享线索
  async confirmShareClue() {
    const content = this.data.clueContent.trim();
    if (!content) {
      this.showToast('请输入线索内容');
      return;
    }

    try {
      const message = {
        id: Date.now(),
        senderId: this.data.currentUserId,
        type: 'clue',
        content,
        timestamp: Date.now(),
        timeStr: '刚刚'
      };

      // 添加到消息列表
      const allMessages = [...this.data.allMessages, message];
      this.setData({
        allMessages,
        clueContent: ''
      });

      // 发送到服务器
      // await api.sendMessage(this.roomId, message);

      // 滚动到底部
      this.scrollToBottom();

      this.showToast('线索分享成功', 'success');

    } catch (error) {
      console.error('分享线索失败:', error);
      this.showToast('分享失败，请重试');
    }
  },

  // 提出质疑
  raiseDoubt() {
    wx.showActionSheet({
      itemList: ['质疑张三的说法', '质疑李四的线索', '质疑王五的行为'],
      success: (res) => {
        const doubts = ['质疑张三的说法', '质疑李四的线索', '质疑王五的行为'];
        const doubtContent = doubts[res.tapIndex];
        
        // 发送质疑消息
        this.sendDoubtMessage(doubtContent);
      }
    });
  },

  // 发送质疑消息
  async sendDoubtMessage(content) {
    try {
      const message = {
        id: Date.now(),
        senderId: this.data.currentUserId,
        type: 'doubt',
        content: `提出质疑：${content}`,
        timestamp: Date.now(),
        timeStr: '刚刚'
      };

      // 添加到消息列表
      const allMessages = [...this.data.allMessages, message];
      this.setData({ allMessages });

      // 滚动到底部
      this.scrollToBottom();

    } catch (error) {
      console.error('发送质疑失败:', error);
      this.showToast('发送失败，请重试');
    }
  },

  // 打开私聊
  openPrivateChat(e) {
    const playerId = e.currentTarget.dataset.playerId;
    wx.navigateTo({
      url: `/pages/private-chat/private-chat?roomId=${this.roomId}&playerId=${playerId}`
    });
  },

  // 切换静音
  toggleMute() {
    const isMuted = !this.data.isMuted;
    this.setData({ isMuted });
    
    this.showToast(isMuted ? '已静音' : '已取消静音', 'success');
  },

  // 滚动到底部
  scrollToBottom() {
    setTimeout(() => {
      const messages = this.data.allMessages;
      if (messages.length > 0) {
        const lastMessageId = `msg-${messages[messages.length - 1].id}`;
        this.setData({ scrollIntoView: lastMessageId });
      }
    }, 100);
  },

  // 返回上一页
  goBack() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出讨论吗？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  },

  // 显示提示
  showToast(message, theme = 'warning') {
    wx.showToast({
      title: message,
      icon: theme === 'success' ? 'success' : 'none',
      duration: 2000
    });
  },

  // 笔记内容变化
  onNotesChange(e) {
    this.setData({ notesText: e.detail.value });
  },

  // 显示游戏规则
  showGameRules() {
    wx.showModal({
      title: '游戏规则',
      content: '1. 在讨论阶段，所有玩家可以自由交流\n2. 可以分享线索、提出质疑\n3. 请充分利用时间收集信息\n4. 讨论结束后将进入投票环节',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 格式化时间显示
  formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  },

  // 输入框获得焦点
  onInputFocus() {
    // 可以添加一些焦点获得时的处理逻辑
  },

  // 输入框失去焦点
  onInputBlur() {
    // 可以添加一些焦点失去时的处理逻辑
  },

  // 显示表情选择器
  showEmoji() {
    wx.showToast({
      title: '表情功能开发中',
      icon: 'none'
    });
  },

  // 显示语音功能
  showVoice() {
    this.toggleVoiceInput();
  },

  // 指控玩家
  accusePlayer() {
    wx.showActionSheet({
      itemList: this.data.players.map(p => `指控 ${p.nickname}`),
      success: (res) => {
        const player = this.data.players[res.tapIndex];
        wx.showModal({
          title: '确认指控',
          content: `确定要指控 ${player.nickname} 吗？`,
          success: (modalRes) => {
            if (modalRes.confirm) {
              this.sendAccusation(player);
            }
          }
        });
      }
    });
  },

  // 发送指控消息
  sendAccusation(player) {
    const accusationMessage = {
      id: Date.now(),
      senderId: this.data.currentUserId,
      type: 'accusation',
      content: `指控 ${player.nickname}`,
      targetPlayer: player,
      timestamp: Date.now(),
      timeStr: '刚刚'
    };

    const allMessages = [...this.data.allMessages, accusationMessage];
    this.setData({ allMessages });
    this.scrollToBottom();

    // 添加触觉反馈
    wx.vibrateShort();

    wx.showToast({
      title: '指控已发送',
      icon: 'success'
    });
  },

  // 显示游戏规则
  showGameRules() {
    wx.showModal({
      title: '游戏规则',
      content: '1. 自由讨论阶段，玩家可以分享线索、质疑他人\n2. 可以使用语音或文字交流\n3. 注意观察其他玩家的言行\n4. 讨论结束后将进入投票环节',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 进入投票阶段
  proceedToVoting() {
    console.log('点击进入投票按钮');
    console.log('当前roomId:', this.data.roomId);
    console.log('当前round:', this.data.currentRound);

    // 直接跳转，不显示确认对话框（用于调试）
    console.log('开始直接跳转');

    // 清除计时器
    this.clearTimer();

    // 跳转到剧情分支投票页面
    const url = `/pages/plot-voting/plot-voting?roomId=${this.data.roomId}&round=${this.data.currentRound}`;
    console.log('准备跳转到:', url);

    wx.redirectTo({
      url: url,
      success: () => {
        console.log('跳转成功');
      },
      fail: (error) => {
        console.error('跳转失败:', error);
        wx.showToast({
          title: '跳转失败，请重试',
          icon: 'none'
        });
      }
    });
  }
});
