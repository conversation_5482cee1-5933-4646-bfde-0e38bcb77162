// fix-api-key.js
// 快速修复API密钥问题的脚本

const apiConfig = require('./config/api-config');

console.log('🔧 API密钥修复工具');
console.log('==================');

// 1. 检查当前配置
console.log('\n1. 检查当前配置...');
const config = apiConfig.getCurrentConfig();
console.log('当前API地址:', config.baseUrl);
console.log('当前模型:', config.model);
console.log('当前密钥:', apiConfig.getSafeKeyDisplay(config.apiKey));

// 2. 验证密钥格式
console.log('\n2. 验证密钥格式...');
const validation = apiConfig.validateApiKey(config.apiKey);
console.log('验证结果:', validation.valid ? '✅ 通过' : '❌ 失败');
if (!validation.valid) {
  console.log('错误信息:', validation.message);
}

// 3. 清除旧的本地存储
console.log('\n3. 清除旧的本地存储...');
try {
  // 注意：这个脚本在Node.js环境中运行，无法直接使用wx API
  console.log('⚠️  请在小程序中手动清除本地存储：');
  console.log('   - 进入"个人中心" → "API设置"');
  console.log('   - 点击"清除密钥"按钮');
  console.log('   - 重新输入新的API密钥');
} catch (error) {
  console.log('❌ 清除失败:', error.message);
}

// 4. 提供修复建议
console.log('\n4. 修复建议:');
console.log('✅ API地址已更新为: https://api.moonshot.cn/v1');
console.log('✅ API密钥已更新为: sk-rFun7AywY7jUUdJAtUBbFDxhaVTI5okdFpL3mSSeLfOmKCmP');
console.log('');
console.log('📋 下一步操作:');
console.log('1. 在微信小程序中清除本地存储的旧密钥');
console.log('2. 重新启动小程序');
console.log('3. 测试AI功能是否正常');
console.log('');
console.log('🔍 如果仍有问题，请检查:');
console.log('- 微信小程序域名白名单是否包含: https://api.moonshot.cn');
console.log('- API密钥是否在Moonshot AI控制台中有效');
console.log('- 账户余额是否充足');

console.log('\n✅ 修复完成！');
