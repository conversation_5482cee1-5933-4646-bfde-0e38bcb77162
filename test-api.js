// test-api.js - 简单的API测试脚本
const https = require('https');

const API_KEY = 'sk-rFun7AywY7jUUdJAtUBbFDxhaVTI5okdFpL3mSSeLfOmKCmP';
const BASE_URL = 'https://api.moonshot.cn/v1';
const MODEL = 'kimi-k2-0711-preview';

function testMoonshotAPI() {
  const data = JSON.stringify({
    model: MODEL,
    messages: [
      { role: 'user', content: 'Hello, this is a connection test. Please respond with "API connection successful".' }
    ],
    max_tokens: 50
  });

  const options = {
    hostname: 'api.moonshot.cn',
    port: 443,
    path: '/v1/chat/completions',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${API_KEY}`,
      'Content-Length': data.length
    }
  };

  console.log('🚀 开始测试Moonshot AI API...');
  console.log(`📡 API地址: ${BASE_URL}/chat/completions`);
  console.log(`🤖 模型: ${MODEL}`);
  console.log(`🔑 API密钥: ${API_KEY.substring(0, 10)}...${API_KEY.substring(API_KEY.length - 4)}`);
  console.log('');

  const req = https.request(options, (res) => {
    console.log(`📊 状态码: ${res.statusCode}`);
    console.log(`📋 响应头:`, res.headers);
    console.log('');

    let responseData = '';

    res.on('data', (chunk) => {
      responseData += chunk;
    });

    res.on('end', () => {
      try {
        if (res.statusCode === 200) {
          const jsonResponse = JSON.parse(responseData);
          console.log('✅ API连接成功！');
          console.log('📝 响应内容:');
          console.log(JSON.stringify(jsonResponse, null, 2));
          
          if (jsonResponse.choices && jsonResponse.choices[0]) {
            console.log('');
            console.log('💬 AI回复:', jsonResponse.choices[0].message.content);
          }
        } else {
          console.log('❌ API请求失败');
          console.log('📄 响应内容:', responseData);
          
          try {
            const errorData = JSON.parse(responseData);
            if (errorData.error) {
              console.log('🚨 错误信息:', errorData.error.message);
              console.log('🔍 错误类型:', errorData.error.type);
            }
          } catch (parseError) {
            console.log('⚠️  无法解析错误响应');
          }
        }
      } catch (error) {
        console.log('❌ 解析响应失败:', error.message);
        console.log('📄 原始响应:', responseData);
      }
    });
  });

  req.on('error', (error) => {
    console.log('❌ 网络请求失败:', error.message);
    
    if (error.code === 'ENOTFOUND') {
      console.log('🌐 DNS解析失败，请检查网络连接');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('🚫 连接被拒绝，请检查API地址');
    } else if (error.code === 'ETIMEDOUT') {
      console.log('⏰ 连接超时，请检查网络状况');
    }
  });

  req.on('timeout', () => {
    console.log('⏰ 请求超时');
    req.destroy();
  });

  // 设置超时时间
  req.setTimeout(15000);

  // 发送请求数据
  req.write(data);
  req.end();
}

// 运行测试
console.log('🔧 Moonshot AI API 连接测试');
console.log('================================');
testMoonshotAPI();
