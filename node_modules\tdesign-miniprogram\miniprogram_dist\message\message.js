import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import{MessageType}from"./message.interface";import props from"./props";import{unitConvert}from"../common/utils";const SHOW_DURATION=400,{prefix:prefix}=config,name=`${prefix}-message`;let Message=class extends SuperComponent{constructor(){super(...arguments),this.options={multipleSlots:!0},this.properties=Object.assign({},props),this.data={prefix:prefix,classPrefix:name,messageList:[]},this.index=0,this.instances=[],this.gap=12,this.observers={visible(s){s?this.setMessage(this.properties,this.properties.theme):this.setData({messageList:[]})}},this.pageLifetimes={show(){this.hideAll()}},this.lifetimes={ready(){this.memoInitialData()}}}memoInitialData(){this.initialData=Object.assign(Object.assign({},this.properties),this.data)}setMessage(s,e=MessageType.info){let t=`${name}_${this.index}`;s.single&&(t=name),this.gap=unitConvert(s.gap||this.gap);const i=Object.assign(Object.assign({},s),{theme:e,id:t,gap:this.gap}),n=this.instances.findIndex(s=>s.id===t);if(n<0)this.addMessage(i);else{const s=this.instances[n],e=this.getOffsetHeight(n);s.resetData(()=>{s.setData(i,s.show.bind(s,e)),s.onHide=()=>{this.close(t)}})}}addMessage(s){const e=[...this.data.messageList,{id:s.id}];this.setData({messageList:e},()=>{const e=this.getOffsetHeight(),t=this.showMessageItem(s,s.id,e);this.instances&&(this.instances.push(t),this.index+=1)})}getOffsetHeight(s=-1){let e=0,t=s;(-1===t||t>this.instances.length)&&(t=this.instances.length);for(let s=0;s<t;s+=1){const t=this.instances[s];e+=t.data.height+t.data.gap}return e}showMessageItem(s,e,t){const i=this.selectComponent(`#${e}`);if(i)return i.resetData(()=>{i.setData(s,i.show.bind(i,t)),i.onHide=()=>{this.close(e)}}),i;console.error("未找到组件,请确认 selector && context 是否正确")}close(s){setTimeout(()=>{this.removeMsg(s)},400),this.removeInstance(s)}hide(s){s||this.hideAll();const e=this.instances.find(e=>e.id===s);e&&e.hide()}hideAll(){for(let s=0;s<this.instances.length;){this.instances[s].hide()}}removeInstance(s){const e=this.instances.findIndex(e=>e.id===s);if(e<0)return;const t=this.instances[e].data.height;this.instances.splice(e,1);for(let s=e;s<this.instances.length;s+=1){const e=this.instances[s];e.setData({wrapTop:e.data.wrapTop-t-e.data.gap})}}removeMsg(s){const e=this.data.messageList.findIndex(e=>e.id===s);e>-1&&(this.data.messageList.splice(e,1),this.setData({messageList:this.data.messageList}))}handleClose(){this.triggerEvent("close-btn-click")}handleLinkClick(){this.triggerEvent("link-click")}handleDurationEnd(){this.triggerEvent("duration-end")}};Message=__decorate([wxComponent()],Message);export default Message;