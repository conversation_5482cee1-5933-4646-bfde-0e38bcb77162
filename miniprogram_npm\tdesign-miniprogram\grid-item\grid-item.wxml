<import src="../common/template/image.wxml"/><import src="../common/template/icon.wxml"/><wxs module="util">module.exports.getImageSize = function(column) { if (column >= 5) return 'small'; if (column == 4) return 'middle'; return 'large'; }</wxs><wxs module="_" src="../common/utils.wxs"/><view class="{{_.cls(classPrefix, [['auto-size', column == 0]])}} class {{prefix}}-class" style="{{_._style([gridItemStyle, style, customStyle])}}" hover-class="{{hover ? classPrefix + '--hover':''}}" hover-stay-time="{{200}}" bindtap="onClick" aria-role="{{ariaRole || 'button'}}" aria-label="{{ariaLabel}}" aria-describedby="{{describedbyID}}"><view class="{{_.cls(classPrefix + '__wrapper', [layout])}}" style="{{gridItemWrapperStyle}}"><view class="{{_.cls(classPrefix + '__content', [align, layout])}} {{prefix}}-class-content" style="{{gridItemContentStyle}}"><slot/><t-badge wx:if="{{image || icon}}" color="{{badgeProps.color || ''}}" content="{{badgeProps.content || ''}}" count="{{badgeProps.count || 0}}" dot="{{badgeProps.dot || false}}" max-count="{{badgeProps.maxCount || 99}}" offset="{{badgeProps.offset || []}}" shape="{{badgeProps.shape || 'circle'}}" show-zero="{{badgeProps.showZero || false}}" size="{{badgeProps.size || 'medium'}}" t-class="{{badgeProps.tClass}}" t-class-content="{{badgeProps.tClassContent}}" t-class-count="{{badgeProps.tClassCount}}"><view class="{{_.cls(classPrefix + '__image', [util.getImageSize(column), ['icon', icon]])}} {{prefix}}-class-image"><block wx:if="{{image && image != 'slot'}}"><template is="image" data="{{ src: image, shape: 'round', mode: 'widthFix', tClass: _.cls(classPrefix + '__image', [util.getImageSize(column)]) + ' ' + prefix + '-class-image', ...imageProps }}"/></block><slot name="image"/><template wx:if="{{iconName || _.isNoEmptyObj(iconData)}}" is="icon" data="{{tClass: classPrefix + '__icon', name: iconName, ...iconData}}"/></view></t-badge><view class="{{_.cls(classPrefix + '__words', [layout])}}" id="{{describedbyID}}" aria-label="{{ ariaLabel || (badgeProps.dot || badgeProps.count ? text + ',' +  description + ',' + _.getBadgeAriaLabel({ ...badgeProps }) : '') }}"><view wx:if="{{text}}" class="{{_.cls(classPrefix + '__text', [util.getImageSize(column), layout])}} {{prefix}}-class-text">{{text}}</view><slot name="text"/><view wx:if="{{description}}" class="{{_.cls(classPrefix + '__description', [util.getImageSize(column), layout])}} {{prefix}}-class-description">{{description}}</view><slot name="description"/></view></view></view></view>