// test-network-diagnostic.js
// 测试网络诊断功能

const NetworkDiagnostic = require('./utils/network-diagnostic');

console.log('🧪 测试网络诊断功能');
console.log('====================');

async function testNetworkDiagnostic() {
  try {
    console.log('\n1. 创建网络诊断实例...');
    const diagnostic = new NetworkDiagnostic();
    
    console.log('\n2. 运行完整诊断...');
    const results = await diagnostic.runFullDiagnostic();
    
    console.log('\n3. 诊断结果:');
    console.log('📊 网络状态:', results.networkStatus);
    console.log('🌐 域名可达性:', results.domainReachability);
    console.log('🔌 API端点:', results.apiEndpoint);
    console.log('💡 建议数量:', results.recommendations.length);
    
    console.log('\n4. 详细建议:');
    results.recommendations.forEach((rec, index) => {
      const icon = rec.type === 'critical' ? '🚨' : rec.type === 'warning' ? '⚠️' : 'ℹ️';
      console.log(`${index + 1}. ${icon} ${rec.title}`);
      console.log(`   ${rec.description}`);
      console.log(`   建议: ${rec.action}\n`);
    });
    
    console.log('\n✅ 网络诊断测试完成');
    return results;
    
  } catch (error) {
    console.error('\n❌ 网络诊断测试失败:', error);
    throw error;
  }
}

// 测试单个功能
async function testIndividualFunctions() {
  console.log('\n🔍 测试单个诊断功能...');
  
  const diagnostic = new NetworkDiagnostic();
  
  try {
    console.log('\n📶 测试网络状态检查...');
    const networkStatus = await diagnostic.checkNetworkStatus();
    console.log('网络状态结果:', networkStatus);
    
    console.log('\n🌐 测试域名可达性...');
    const domainReachability = await diagnostic.checkDomainReachability();
    console.log('域名可达性结果:', domainReachability);
    
    console.log('\n🔌 测试API端点...');
    const apiEndpoint = await diagnostic.checkApiEndpoint();
    console.log('API端点结果:', apiEndpoint);
    
    console.log('\n✅ 单个功能测试完成');
    
  } catch (error) {
    console.error('\n❌ 单个功能测试失败:', error);
  }
}

// 如果在小程序环境中运行
if (typeof wx !== 'undefined') {
  console.log('🎯 在微信小程序环境中运行测试');
  
  testNetworkDiagnostic().then(results => {
    console.log('\n🎉 所有测试完成');
    console.log('诊断结果可用于网络测试页面');
  }).catch(error => {
    console.log('\n💥 测试过程中出现错误');
    console.log('错误详情:', error);
  });
  
} else {
  console.log('⚠️ 非小程序环境，无法测试wx.request相关功能');
  console.log('建议在微信开发者工具中运行此测试');
}

module.exports = {
  testNetworkDiagnostic,
  testIndividualFunctions
};
