<!--线索收集页面-->
<view class="container">
  <!-- 顶部状态栏 -->
  <view class="status-bar">
    <view class="timer">
      <text class="timer-icon">⏰</text>
      <text class="timer-text">{{formatTime(timeRemaining)}}</text>
    </view>
    <view class="phase-indicator">
      <text class="phase-text">线索收集阶段</text>
    </view>
  </view>

  <!-- 角色信息卡片 -->
  <view class="role-card" wx:if="{{myRole}}">
    <view class="role-header">
      <text class="role-name">{{myRole.name}}</text>
      <text class="role-title">{{myRole.title}}</text>
    </view>
    <view class="role-objectives">
      <text class="objectives-label">目标:</text>
      <text class="objectives-text">{{myRole.objectives ? myRole.objectives.join('、') : '探寻真相'}}</text>
    </view>
  </view>

  <!-- 线索过滤器 -->
  <view class="filters-section">
    <scroll-view class="filters-scroll" scroll-x="true">
      <view class="filters-list">
        <view
          class="filter-item {{activeFilter === item.id ? 'active' : ''}}"
          wx:for="{{filters}}"
          wx:key="id"
          bind:tap="switchFilter"
          data-filter="{{item.id}}"
        >
          <text class="filter-name">{{item.name}}</text>
          <view class="filter-badge" wx:if="{{item.count > 0}}">{{item.count}}</view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 线索列表 -->
  <view class="clues-section">
    <view class="clues-list" wx:if="{{!loading}}">
      <view
        class="clue-card {{item.importance === 'key' ? 'key-clue' : ''}} {{item.discovered ? 'discovered' : 'undiscovered'}}"
        wx:for="{{filteredClues}}"
        wx:key="id"
        bind:tap="viewClueDetail"
        data-clue-id="{{item.id}}"
      >
        <view class="clue-header">
          <text class="clue-title">{{item.title}}</text>
          <view class="clue-meta">
            <text class="clue-time">{{item.timeLimit}}分钟前</text>
            <view class="clue-status">
              <text wx:if="{{item.discovered}}">✅</text>
              <text wx:else>🔍</text>
            </view>
          </view>
        </view>

        <text class="clue-content">{{item.content}}</text>

        <view class="clue-footer">
          <view class="clue-tags">
            <text
              class="clue-tag"
              wx:for="{{item.tags}}"
              wx:for-item="tag"
              wx:key="*this"
            >
              #{{tag}}
            </text>
          </view>
          <text class="related-chars" wx:if="{{item.relatedCharacters.length > 0}}">
            相关角色: {{item.relatedCharacters.join('、')}}
          </text>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{loading}}">
      <view class="loading-animation">
        <view class="loading-spinner"></view>
      </view>
      <text class="loading-text">🤖 AI正在为您生成个性化线索...</text>
      <text class="loading-subtitle">基于剧本《{{myRole.scriptTitle || '神秘剧本'}}》和角色{{myRole.name || '调查员'}}</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && filteredClues.length === 0}}">
      <text class="empty-text">暂无相关线索</text>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <button class="action-btn secondary" bind:tap="generateClues">
      🔄 刷新线索
    </button>
    <button class="action-btn primary" bind:tap="endClueCollection">
      📝 进入讨论
    </button>
  </view>
</view>

<!-- 线索详情弹窗 -->
<view class="clue-detail-modal" wx:if="{{showClueDetail}}" bind:tap="closeClueDetail">
  <view class="modal-content" catch:tap="">
    <view class="modal-header">
      <text class="modal-title">{{selectedClue.title}}</text>
      <button class="close-btn" bind:tap="closeClueDetail">✕</button>
    </view>

    <view class="modal-body">
      <text class="clue-full-content">{{selectedClue.content}}</text>

      <view class="clue-details">
        <view class="detail-item">
          <text class="detail-label">类型:</text>
          <text class="detail-value">
            {{selectedClue.type === 'item' ? '物品' :
              selectedClue.type === 'person' ? '人物' :
              selectedClue.type === 'location' ? '地点' : '其他'}}
          </text>
        </view>

        <view class="detail-item">
          <text class="detail-label">重要性:</text>
          <text class="detail-value {{selectedClue.importance === 'key' ? 'key-importance' : ''}}">
            {{selectedClue.importance === 'key' ? '关键线索' : '普通线索'}}
          </text>
        </view>

        <view class="detail-item" wx:if="{{selectedClue.relatedCharacters.length > 0}}">
          <text class="detail-label">相关角色:</text>
          <text class="detail-value">{{selectedClue.relatedCharacters.join('、')}}</text>
        </view>
      </view>

      <view class="clue-tags-detail" wx:if="{{selectedClue.tags.length > 0}}">
        <text class="tags-label">标签:</text>
        <view class="tags-list">
          <text
            class="tag-item"
            wx:for="{{selectedClue.tags}}"
            wx:for-item="tag"
            wx:key="*this"
          >
            #{{tag}}
          </text>
        </view>
      </view>
    </view>

    <view class="modal-footer">
      <button class="modal-btn" bind:tap="closeClueDetail">确定</button>
    </view>
  </view>
</view>

<!-- 线索详情弹窗 -->
<view class="clue-detail-modal" wx:if="{{showClueDetail}}" bind:tap="closeClueDetail">
  <view class="modal-content" catch:tap="">
    <view class="modal-header">
      <text class="modal-title">{{selectedClue.title}}</text>
      <button class="close-btn" bind:tap="closeClueDetail">✕</button>
    </view>
    
    <view class="modal-body">
      <text class="clue-full-content">{{selectedClue.content}}</text>
      
      <view class="clue-details">
        <view class="detail-item">
          <text class="detail-label">类型:</text>
          <text class="detail-value">
            {{selectedClue.type === 'item' ? '物品' : 
              selectedClue.type === 'person' ? '人物' : 
              selectedClue.type === 'location' ? '地点' : '其他'}}
          </text>
        </view>
        
        <view class="detail-item">
          <text class="detail-label">重要性:</text>
          <text class="detail-value {{selectedClue.importance === 'key' ? 'key-importance' : ''}}">
            {{selectedClue.importance === 'key' ? '关键线索' : '普通线索'}}
          </text>
        </view>
        
        <view class="detail-item" wx:if="{{selectedClue.relatedCharacters.length > 0}}">
          <text class="detail-label">相关角色:</text>
          <text class="detail-value">{{selectedClue.relatedCharacters.join('、')}}</text>
        </view>
      </view>
      
      <view class="clue-tags-detail" wx:if="{{selectedClue.tags.length > 0}}">
        <text class="tags-label">标签:</text>
        <view class="tags-list">
          <text 
            class="tag-item"
            wx:for="{{selectedClue.tags}}" 
            wx:for-item="tag"
            wx:key="*this"
          >
            #{{tag}}
          </text>
        </view>
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="modal-btn" bind:tap="closeClueDetail">确定</button>
    </view>
  </view>
</view>
