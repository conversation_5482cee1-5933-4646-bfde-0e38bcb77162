<!--房间准备页面-->
<view class="container">
  <!-- 头部信息 -->
  <view class="header">
    <view class="room-info">
      <text class="room-id">房间号: {{roomId}}</text>
      <text class="room-mode">经典推理模式</text>
    </view>
    <view class="step-indicator">
      <view class="step-item {{step >= item.id ? 'active' : ''}}" wx:for="{{steps}}" wx:key="id">
        <view class="step-number">{{item.id}}</view>
        <text class="step-title">{{item.title}}</text>
      </view>
    </view>
  </view>

  <!-- 当前步骤内容 -->
  <view class="step-content">
    <!-- 步骤1: 等待玩家 -->
    <view class="step-section" wx:if="{{step === 1}}">
      <view class="section-title">
        <text class="title-text">👥 等待玩家加入</text>
        <text class="title-desc">当前 {{players.length}}/6 人</text>
      </view>
      
      <view class="players-list">
        <view class="player-item" wx:for="{{players}}" wx:key="id">
          <view class="player-avatar">
            <text class="avatar-text">{{item.nickname.charAt(0)}}</text>
          </view>
          <view class="player-info">
            <text class="player-name">{{item.nickname}}</text>
            <text class="player-status">{{item.isReady ? '已准备' : '未准备'}}</text>
          </view>
          <view class="ready-indicator {{item.isReady ? 'ready' : ''}}">
            {{item.isReady ? '✓' : '○'}}
          </view>
        </view>
      </view>

      <view class="step-actions" wx:if="{{players.length >= 4}}">
        <text class="action-hint">玩家数量已满足，正在生成剧本...</text>
      </view>
    </view>

    <!-- 步骤2: 生成剧本 -->
    <view class="step-section" wx:if="{{step === 2}}">
      <view class="section-title">
        <text class="title-text">🎬 生成游戏剧本</text>
        <text class="title-desc">AI正在创作专属剧本</text>
      </view>

      <view class="script-generation">
        <view class="generation-status">
          <view class="loading-icon {{loading ? 'spinning' : ''}}">
            {{scriptGenerated ? '✅' : '🎭'}}
          </view>
          <text class="status-text">
            {{scriptGenerated ? '剧本生成完成' : loading ? '正在生成剧本...' : '准备生成剧本'}}
          </text>
        </view>

        <view class="script-info" wx:if="{{scriptGenerated && scriptData}}">
          <view class="info-item">
            <text class="info-label">剧本标题:</text>
            <text class="info-value">{{scriptData.storyInfo.title}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">角色数量:</text>
            <text class="info-value">{{scriptData.characters.length}}个</text>
          </view>
          <button class="preview-btn" bind:tap="previewScript">预览剧本</button>
        </view>
      </view>
    </view>

    <!-- 步骤3: 准备完成 -->
    <view class="step-section" wx:if="{{step === 3}}">
      <view class="section-title">
        <text class="title-text">🚀 准备开始游戏</text>
        <text class="title-desc">等待所有玩家确认准备</text>
      </view>

      <view class="ready-check">
        <view class="players-ready">
          <view 
            class="player-ready-item {{item.isReady ? 'ready' : ''}}" 
            wx:for="{{players}}" 
            wx:key="id"
            bind:tap="togglePlayerReady"
            data-player-id="{{item.id}}"
          >
            <view class="player-avatar-small">
              <text>{{item.nickname.charAt(0)}}</text>
            </view>
            <text class="player-name-small">{{item.nickname}}</text>
            <view class="ready-status">
              {{item.isReady ? '✓' : '○'}}
            </view>
          </view>
        </view>

        <view class="ready-actions">
          <button class="action-btn secondary" bind:tap="makeAllReady" wx:if="{{!allReady}}">
            模拟全部准备
          </button>
          <button 
            class="action-btn primary" 
            bind:tap="startGame"
            disabled="{{!allReady || !scriptGenerated}}"
          >
            {{allReady && scriptGenerated ? '开始游戏' : '等待准备'}}
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作 -->
  <view class="footer">
    <button class="footer-btn" bind:tap="goHome">返回主页</button>
    <view class="room-status">
      <text class="status-text">
        {{step === 1 ? '等待玩家' : step === 2 ? '生成剧本' : '准备开始'}}
      </text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">处理中...</text>
    </view>
  </view>
</view>
