import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import Props from"./props";const{prefix:prefix}=config,name=`${prefix}-checkbox`;let CheckBox=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`,`${prefix}-class-label`,`${prefix}-class-icon`,`${prefix}-class-content`,`${prefix}-class-border`],this.behaviors=["wx://form-field"],this.relations={"../checkbox-group/checkbox-group":{type:"ancestor",linked(e){const{value:t,disabled:s,borderless:a}=e.data,i=new Set(t),o=i.has(this.data.value),c={_disabled:null==this.data.disabled?s:this.data.disabled};a&&(c.borderless=!0),c.checked=this.data.checked||o,this.data.checked&&e.updateValue(this.data),this.data.checkAll&&(c.checked=i.size>0),this.setData(c)}}},this.options={multipleSlots:!0},this.properties=Object.assign(Object.assign({},Props),{theme:{type:String,value:"default"},tId:{type:String}}),this.data={prefix:prefix,classPrefix:name,_disabled:!1},this.observers={disabled(e){this.setData({_disabled:e})}},this.controlledProps=[{key:"checked",event:"change"}],this.methods={handleTap(e){const{_disabled:t,readonly:s,contentDisabled:a}=this.data,{target:i}=e.currentTarget.dataset;if(t||s||"text"===i&&a)return;const{value:o,label:c}=this.data,d=!this.data.checked,r=this.$parent;r?r.updateValue(Object.assign(Object.assign({},this.data),{checked:d,item:{label:c,value:o,checked:d}})):this._trigger("change",{context:{value:o,label:c},checked:d})},setDisabled(e){this.setData({_disabled:this.data.disabled||e})}}}};CheckBox=__decorate([wxComponent()],CheckBox);export default CheckBox;