/* AI剧本生成页面样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  padding: 20rpx;
  position: relative;
  overflow: hidden;
}

/* 顶部导航 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-bottom: 40rpx;
  padding: 20rpx 0;
}

.back-btn {
  position: absolute;
  left: 0;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 15rpx;
  padding: 10rpx 20rpx;
  font-size: 26rpx;
}

.nav-title {
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
}

/* 生成进度区域 */
.generation-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
}

.progress-circle {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.progress-ring {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(from 0deg, #ffd700 0deg, rgba(255, 255, 255, 0.2) 0deg);
  position: relative;
  overflow: hidden;
}

.progress-fill {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: conic-gradient(from 0deg, #ffd700 0deg, transparent 0deg);
  border-radius: 50%;
  transition: transform 0.5s ease;
}

.progress-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  background: rgba(26, 26, 46, 0.9);
  border-radius: 50%;
  width: 140rpx;
  height: 140rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.progress-number {
  color: #ffd700;
  font-size: 36rpx;
  font-weight: bold;
  font-family: 'Courier New', monospace;
}

.progress-label {
  color: #ccc;
  font-size: 22rpx;
  margin-top: 5rpx;
}

.current-step {
  text-align: center;
}

.step-title {
  display: block;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.step-desc {
  display: block;
  color: #ccc;
  font-size: 26rpx;
  line-height: 1.4;
}

/* 步骤列表 */
.steps-list {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.steps-title {
  color: #ffd700;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  text-align: center;
}

.steps-container {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 15rpx;
  border-radius: 15rpx;
  transition: all 0.3s ease;
}

.step-item.active {
  background: rgba(255, 215, 0, 0.2);
  border: 1px solid rgba(255, 215, 0, 0.5);
}

.step-item.completed {
  background: rgba(76, 175, 80, 0.2);
  border: 1px solid rgba(76, 175, 80, 0.5);
}

.step-indicator {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.step-number {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  font-size: 20rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-status {
  font-size: 24rpx;
}

.step-content {
  flex: 1;
}

.step-name {
  display: block;
  color: #fff;
  font-size: 26rpx;
  font-weight: bold;
  margin-bottom: 5rpx;
}

.step-description {
  display: block;
  color: #ccc;
  font-size: 22rpx;
  line-height: 1.3;
}

/* 完成后的操作区域 */
.completion-actions {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.script-preview {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20rpx;
  padding: 30rpx;
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
}

.preview-title {
  display: block;
  color: #ffd700;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.preview-desc {
  display: block;
  color: #e0e0e0;
  font-size: 26rpx;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.preview-stats {
  display: flex;
  justify-content: center;
  gap: 30rpx;
}

.stat-item {
  color: #ccc;
  font-size: 24rpx;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.action-btn {
  height: 80rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.action-btn:active {
  transform: scale(0.98);
}

/* 装饰元素 */
.decoration-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: -1;
}

.floating-icon {
  position: absolute;
  font-size: 40rpx;
  opacity: 0.3;
  animation: float 3s ease-in-out infinite;
}

.icon-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.icon-2 {
  top: 30%;
  right: 15%;
  animation-delay: 1s;
}

.icon-3 {
  bottom: 30%;
  left: 20%;
  animation-delay: 2s;
}

.icon-4 {
  bottom: 20%;
  right: 10%;
  animation-delay: 1.5s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20rpx);
  }
}
