// 测试角色分配功能页面
const roomManager = require('../../utils/room-manager');
const aiService = require('../../utils/ai-service-simple');

Page({
  data: {
    roomId: '',
    scriptData: null,
    assignmentResult: null,
    testStep: 0,
    steps: [
      '创建测试房间',
      '添加测试玩家',
      '生成AI剧本',
      '分配角色',
      '验证结果'
    ]
  },

  onLoad() {
    console.log('🧪 角色分配测试页面加载');
  },

  // 开始测试
  async startTest() {
    try {
      this.setData({ testStep: 0 });
      
      // 步骤1：创建测试房间
      await this.createTestRoom();
      this.nextStep();

      // 步骤2：添加测试玩家
      await this.addTestPlayers();
      this.nextStep();

      // 步骤3：生成AI剧本
      await this.generateTestScript();
      this.nextStep();

      // 步骤4：分配角色
      await this.assignRoles();
      this.nextStep();

      // 步骤5：验证结果
      await this.verifyResults();
      this.nextStep();

      wx.showToast({
        title: '测试完成！',
        icon: 'success'
      });

    } catch (error) {
      console.error('❌ 测试失败:', error);
      wx.showToast({
        title: error.message || '测试失败',
        icon: 'error'
      });
    }
  },

  // 创建测试房间
  async createTestRoom() {
    console.log('🏠 创建测试房间...');
    
    const result = roomManager.createRoom({
      gameMode: '经典推理模式',
      maxPlayers: 6,
      timeLimit: 600,
      rounds: 3
    });

    this.setData({ roomId: result.roomId });
    console.log('✅ 测试房间创建成功:', result.roomId);
  },

  // 添加测试玩家
  async addTestPlayers() {
    console.log('👥 添加测试玩家...');
    
    const testPlayers = [
      { id: 'test_player_2', nickname: '测试玩家2' },
      { id: 'test_player_3', nickname: '测试玩家3' },
      { id: 'test_player_4', nickname: '测试玩家4' },
      { id: 'test_player_5', nickname: '测试玩家5' },
      { id: 'test_player_6', nickname: '测试玩家6' }
    ];

    for (let i = 0; i < testPlayers.length; i++) {
      roomManager.joinRoom(this.data.roomId, testPlayers[i]);
    }

    console.log('✅ 测试玩家添加完成');
  },

  // 生成测试剧本
  async generateTestScript() {
    console.log('📝 生成测试剧本...');
    
    const scriptData = await aiService.generateScript({
      storyType: 'mystery',
      playerCount: 6,
      difficulty: 'medium',
      theme: '神秘庄园'
    });

    this.setData({ scriptData });
    
    // 保存到本地存储
    wx.setStorageSync(`script_${this.data.roomId}`, scriptData);
    
    console.log('✅ 测试剧本生成完成:', scriptData.storyInfo?.title);
  },

  // 分配角色
  async assignRoles() {
    console.log('🎭 分配角色...');
    
    const room = roomManager.getRoomInfo(this.data.roomId);
    const playerIds = Array.from(room.players.keys());
    
    const assignmentResult = await aiService.generateRoleAssignment(this.data.scriptData, playerIds);
    
    this.setData({ assignmentResult });
    console.log('✅ 角色分配完成');
  },

  // 验证结果
  async verifyResults() {
    console.log('🔍 验证结果...');
    
    const { scriptData, assignmentResult } = this.data;
    
    // 验证角色数量
    if (assignmentResult.assignments.length !== scriptData.characters.length) {
      throw new Error('角色分配数量不匹配');
    }

    // 验证每个角色都被分配
    const assignedCharacterIds = assignmentResult.assignments.map(a => a.characterId);
    const allCharacterIds = scriptData.characters.map(c => c.id);
    
    for (const charId of allCharacterIds) {
      if (!assignedCharacterIds.includes(charId)) {
        throw new Error(`角色 ${charId} 未被分配`);
      }
    }

    // 验证每个玩家都有角色
    const room = roomManager.getRoomInfo(this.data.roomId);
    const playerIds = Array.from(room.players.keys());
    const assignedPlayerIds = assignmentResult.assignments.map(a => a.playerId);
    
    for (const playerId of playerIds) {
      if (!assignedPlayerIds.includes(playerId)) {
        throw new Error(`玩家 ${playerId} 未获得角色`);
      }
    }

    console.log('✅ 验证通过');
  },

  // 下一步
  nextStep() {
    const nextStep = this.data.testStep + 1;
    this.setData({ testStep: nextStep });
  },

  // 查看剧本详情
  viewScriptDetail() {
    if (!this.data.scriptData) {
      wx.showToast({
        title: '请先生成剧本',
        icon: 'none'
      });
      return;
    }

    const script = this.data.scriptData;
    const content = `标题: ${script.storyInfo?.title || '未知'}\n背景: ${script.storyInfo?.background || '未知'}\n角色数量: ${script.characters?.length || 0}`;

    wx.showModal({
      title: '剧本详情',
      content: content,
      showCancel: false
    });
  },

  // 查看分配详情
  viewAssignmentDetail() {
    if (!this.data.assignmentResult) {
      wx.showToast({
        title: '请先分配角色',
        icon: 'none'
      });
      return;
    }

    const assignments = this.data.assignmentResult.assignments;
    const room = roomManager.getRoomInfo(this.data.roomId);
    
    const details = assignments.map(assignment => {
      const player = room.players.get(assignment.playerId);
      const character = this.data.scriptData.characters.find(c => c.id === assignment.characterId);
      return `${player?.nickname || assignment.playerId} → ${character?.name || assignment.characterId}`;
    }).join('\n');

    wx.showModal({
      title: '分配详情',
      content: details,
      showCancel: false
    });
  },

  // 进入角色分配页面
  goToRoleAssignment() {
    if (!this.data.roomId) {
      wx.showToast({
        title: '请先创建房间',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/role-assignment/role-assignment?roomId=${this.data.roomId}`
    });
  },

  // 重置测试
  resetTest() {
    this.setData({
      roomId: '',
      scriptData: null,
      assignmentResult: null,
      testStep: 0
    });

    wx.showToast({
      title: '测试已重置',
      icon: 'success'
    });
  }
});
