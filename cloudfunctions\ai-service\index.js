// cloudfunctions/ai-service/index.js
// 微信云函数 - 安全的AI服务代理

const cloud = require('wx-server-sdk');
const axios = require('axios');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  console.log('☁️ AI服务云函数被调用:', { action: event.action, hasData: !!event.data });

  const { action, data } = event;

  // 从环境变量获取API密钥（安全）
  const API_KEY = process.env.MOONSHOT_API_KEY;
  const BASE_URL = 'https://api.moonshot.cn/v1';

  // 检查API密钥配置
  if (!API_KEY || API_KEY === 'YOUR_API_KEY_HERE') {
    console.error('❌ API密钥未正确配置');
    return {
      success: false,
      error: 'API密钥未配置，请在云开发控制台设置环境变量 MOONSHOT_API_KEY',
      code: 'NO_API_KEY'
    };
  }

  // 验证请求参数
  if (!action) {
    return {
      success: false,
      error: '缺少必要参数: action',
      code: 'MISSING_ACTION'
    };
  }

  try {
    console.log(`🚀 执行操作: ${action}`);

    switch (action) {
      case 'generateScript':
        return await generateScript(data, API_KEY, BASE_URL);
      case 'generateCharacter':
        return await generateCharacter(data, API_KEY, BASE_URL);
      case 'generateClue':
        return await generateClue(data, API_KEY, BASE_URL);
      case 'testConnection':
        return await testConnection(API_KEY, BASE_URL);
      default:
        return {
          success: false,
          error: `不支持的操作: ${action}`,
          code: 'UNSUPPORTED_ACTION'
        };
    }
  } catch (error) {
    console.error('☁️ 云函数执行错误:', error);
    return {
      success: false,
      error: error.message || '服务异常',
      code: 'EXECUTION_ERROR',
      details: error.stack
    };
  }
};

/**
 * 生成剧本
 */
async function generateScript(params, apiKey, baseUrl) {
  const { theme, playerCount, difficulty } = params;
  
  const prompt = `请为${playerCount}人聚会游戏生成一个${theme}主题的剧本。
难度等级：${difficulty}
要求：
1. 包含完整的故事背景
2. 每个角色都有独特的身份和动机
3. 设置合理的线索和谜题
4. 确保游戏时长约1-2小时

请以JSON格式返回，包含：
- title: 剧本标题
- background: 故事背景
- characters: 角色列表
- clues: 线索列表
- solution: 解决方案`;

  const response = await callMoonshotAPI(baseUrl, apiKey, prompt);
  
  return {
    success: true,
    data: response
  };
}

/**
 * 生成角色
 */
async function generateCharacter(params, apiKey, baseUrl) {
  const { theme, characterType, background } = params;
  
  const prompt = `基于以下背景，生成一个${theme}主题的${characterType}角色：
背景：${background}

请生成角色的：
1. 姓名和基本信息
2. 性格特点
3. 背景故事
4. 秘密信息
5. 游戏目标

以JSON格式返回。`;

  const response = await callMoonshotAPI(baseUrl, apiKey, prompt);
  
  return {
    success: true,
    data: response
  };
}

/**
 * 生成线索
 */
async function generateClue(params, apiKey, baseUrl) {
  const { theme, clueType, context } = params;
  
  const prompt = `为${theme}主题的聚会游戏生成${clueType}类型的线索：
游戏背景：${context}

线索要求：
1. 与主题相符
2. 难度适中
3. 逻辑清晰
4. 有助于推进游戏

以JSON格式返回线索信息。`;

  const response = await callMoonshotAPI(baseUrl, apiKey, prompt);
  
  return {
    success: true,
    data: response
  };
}

/**
 * 测试API连接
 */
async function testConnection(apiKey, baseUrl) {
  console.log('🔍 测试API连接...');

  try {
    const response = await axios.post(`${baseUrl}/chat/completions`, {
      model: 'kimi-k2-0711-preview',
      messages: [
        {
          role: 'user',
          content: '你好，这是一个连接测试。请简单回复"连接成功"。'
        }
      ],
      max_tokens: 50
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 15000
    });

    if (response.data && response.data.choices && response.data.choices[0]) {
      return {
        success: true,
        message: '连接测试成功',
        response: response.data.choices[0].message.content,
        usage: response.data.usage
      };
    } else {
      throw new Error('API响应格式异常');
    }
  } catch (error) {
    console.error('🔍 连接测试失败:', error);

    let errorMessage = '连接测试失败';
    let errorCode = 'CONNECTION_FAILED';

    if (error.response) {
      const status = error.response.status;
      const message = error.response.data?.error?.message || '未知错误';

      switch (status) {
        case 401:
          errorMessage = 'API密钥无效或账户余额不足';
          errorCode = 'INVALID_API_KEY';
          break;
        case 402:
          errorMessage = '账户余额不足，请充值';
          errorCode = 'INSUFFICIENT_BALANCE';
          break;
        case 403:
          errorMessage = 'API访问被拒绝';
          errorCode = 'ACCESS_DENIED';
          break;
        case 429:
          errorMessage = '请求频率过高';
          errorCode = 'RATE_LIMITED';
          break;
        default:
          errorMessage = `API调用失败: ${message}`;
          errorCode = 'API_ERROR';
      }
    } else if (error.code === 'ECONNABORTED') {
      errorMessage = '连接超时';
      errorCode = 'TIMEOUT';
    } else {
      errorMessage = '网络连接失败';
      errorCode = 'NETWORK_ERROR';
    }

    return {
      success: false,
      error: errorMessage,
      code: errorCode,
      details: error.message
    };
  }
}

/**
 * 调用Moonshot AI API
 */
async function callMoonshotAPI(baseUrl, apiKey, prompt) {
  try {
    console.log('📡 调用Moonshot AI API...');

    const response = await axios.post(`${baseUrl}/chat/completions`, {
      model: 'kimi-k2-0711-preview',
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 2000
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    if (response.data && response.data.choices && response.data.choices[0]) {
      const content = response.data.choices[0].message.content;

      console.log('✅ API调用成功，响应长度:', content.length);

      // 尝试解析JSON响应
      try {
        return JSON.parse(content);
      } catch (parseError) {
        // 如果不是JSON，返回原始文本
        return {
          content: content,
          type: 'text'
        };
      }
    } else {
      throw new Error('API响应格式异常');
    }
  } catch (error) {
    console.error('❌ Moonshot API调用失败:', error);

    if (error.response) {
      const status = error.response.status;
      const message = error.response.data?.error?.message || '未知错误';

      if (status === 401) {
        throw new Error('API密钥无效或余额不足');
      } else if (status === 402) {
        throw new Error('账户余额不足，请充值');
      } else if (status === 429) {
        throw new Error('请求频率过高，请稍后重试');
      } else {
        throw new Error(`API调用失败 (${status}): ${message}`);
      }
    } else if (error.code === 'ECONNABORTED') {
      throw new Error('请求超时，请稍后重试');
    } else {
      throw new Error('网络连接失败');
    }
  }
}
