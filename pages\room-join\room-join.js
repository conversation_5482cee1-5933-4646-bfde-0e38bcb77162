// 加入房间页面逻辑
const api = require('../../utils/api');
const common = require('../../utils/common');
const errorHandler = require('../../utils/error-handler');

Page({
  data: {
    roomId: '',
    password: '',
    joining: false,
    quickJoining: false,
    showPasswordInput: false,
    showRoomPreview: false,
    previewRoomInfo: null,
    recentRooms: []
  },

  onLoad(options) {
    // 如果从其他页面传入房间号
    if (options.roomId) {
      this.setData({ roomId: options.roomId });
    }
    
    // 加载最近房间
    this.loadRecentRooms();
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },

  // 房间号输入变化
  onRoomIdChange(e) {
    const roomId = e.detail.value;
    this.setData({ roomId });
    
    // 如果输入了6位房间号，自动检查房间信息
    if (roomId.length === 6) {
      this.checkRoomInfo(roomId);
    }
  },

  // 密码输入变化
  onPasswordChange(e) {
    this.setData({ password: e.detail.value });
  },

  // 检查房间信息
  async checkRoomInfo(roomId) {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 这里应该调用实际的API
      // const roomInfo = await api.getRoomInfo(roomId);
      
      // 模拟房间信息
      const mockRoomInfo = {
        roomId: roomId,
        roomName: '神秘庄园' + roomId,
        gameStyle: '经典推理',
        difficulty: '中等难度',
        currentPlayers: Math.floor(Math.random() * 6) + 1,
        maxPlayers: Math.floor(Math.random() * 6) + 6,
        status: Math.random() > 0.5 ? 'waiting' : 'playing',
        hasPassword: Math.random() > 0.7
      };
      
      // 如果房间需要密码
      if (mockRoomInfo.hasPassword) {
        this.setData({ showPasswordInput: true });
      }
      
      // 显示房间预览
      this.setData({ 
        previewRoomInfo: mockRoomInfo,
        showRoomPreview: true 
      });
      
    } catch (error) {
      console.error('获取房间信息失败:', error);
      errorHandler.showError(error, '获取房间信息', this);
    }
  },

  // 加入房间
  async joinRoom() {
    if (!this.data.roomId || this.data.roomId.length !== 6) {
      this.showToast('请输入正确的6位房间号');
      return;
    }

    try {
      this.setData({ joining: true });

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 这里应该调用实际的API
      // const result = await api.joinRoom(this.data.roomId, this.data.password);
      
      // 保存到最近房间
      this.saveToRecentRooms({
        roomId: this.data.roomId,
        roomName: this.data.previewRoomInfo?.roomName || '未知房间',
        lastJoinTime: common.formatTime(new Date()),
        status: 'waiting'
      });
      
      this.showToast('加入房间成功！', 'success');
      
      // 跳转到房间大厅
      setTimeout(() => {
        wx.redirectTo({
          url: `/pages/room-lobby/room-lobby?roomId=${this.data.roomId}`
        });
      }, 1000);

    } catch (error) {
      console.error('加入房间失败:', error);
      errorHandler.showError(error, '加入房间', this);
    } finally {
      this.setData({ joining: false });
    }
  },

  // 快速加入
  async quickJoin() {
    try {
      this.setData({ quickJoining: true });

      // 模拟搜索房间
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 这里应该调用实际的API
      // const result = await api.quickJoinRoom();
      
      // 模拟找到房间
      const randomRoomId = Math.floor(100000 + Math.random() * 900000).toString();
      
      this.showToast('找到房间，正在加入...', 'success');
      
      // 跳转到房间大厅
      setTimeout(() => {
        wx.redirectTo({
          url: `/pages/room-lobby/room-lobby?roomId=${randomRoomId}`
        });
      }, 1000);

    } catch (error) {
      console.error('快速加入失败:', error);
      errorHandler.showError(error, '快速加入', this);
    } finally {
      this.setData({ quickJoining: false });
    }
  },

  // 加入最近房间
  joinRecentRoom(e) {
    const roomId = e.currentTarget.dataset.roomId;
    this.setData({ roomId });
    this.checkRoomInfo(roomId);
  },

  // 关闭房间预览
  closeRoomPreview() {
    this.setData({ showRoomPreview: false });
  },

  // 确认加入房间
  confirmJoinRoom() {
    this.setData({ showRoomPreview: false });
    this.joinRoom();
  },

  // 加载最近房间
  loadRecentRooms() {
    const recentRooms = common.getStorage('recentRooms', []);
    this.setData({ recentRooms });
  },

  // 保存到最近房间
  saveToRecentRooms(roomInfo) {
    let recentRooms = common.getStorage('recentRooms', []);
    
    // 移除重复的房间
    recentRooms = recentRooms.filter(room => room.roomId !== roomInfo.roomId);
    
    // 添加到开头
    recentRooms.unshift(roomInfo);
    
    // 只保留最近5个房间
    if (recentRooms.length > 5) {
      recentRooms = recentRooms.slice(0, 5);
    }
    
    common.setStorage('recentRooms', recentRooms);
    this.setData({ recentRooms });
  },

  // 显示提示
  showToast(message, theme = 'warning') {
    const toast = this.selectComponent('#t-toast');
    toast.showToast({
      theme,
      message,
      duration: 2000
    });
  }
});
