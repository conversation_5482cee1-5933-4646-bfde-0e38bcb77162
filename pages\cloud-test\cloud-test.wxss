/* pages/cloud-test/cloud-test.wxss */

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 通用区块样式 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

/* 状态区块 */
.status-section {
  margin-bottom: 40rpx;
}

.status-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  font-size: 28rpx;
  color: #666;
}

.status-value {
  font-size: 28rpx;
  font-weight: bold;
}

.status-value.success {
  color: #27ae60;
}

.status-value.error {
  color: #e74c3c;
}

.status-value.warning {
  color: #f39c12;
}

/* 测试区块 */
.test-section {
  margin-bottom: 40rpx;
}

.test-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 88rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  border: none;
}

.test-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.test-btn.secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.test-btn:disabled {
  opacity: 0.6;
}

.btn-icon {
  margin-right: 16rpx;
  font-size: 36rpx;
}

.btn-text {
  font-size: 32rpx;
}

/* 结果区块 */
.results-section {
  margin-bottom: 40rpx;
}

.result-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.result-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.result-status {
  font-size: 24rpx;
  font-weight: bold;
}

.result-message {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.recommendation {
  background: #e8f5e8;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rec-label {
  font-size: 28rpx;
  color: #333;
}

.rec-value {
  font-size: 28rpx;
  font-weight: bold;
}

/* 控制区块 */
.control-section {
  margin-bottom: 40rpx;
}

.control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 80rpx;
  background: white;
  border: 2rpx solid #ddd;
  border-radius: 12rpx;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  color: #333;
}

.control-btn:active {
  background: #f0f0f0;
}

/* 帮助区块 */
.help-section {
  margin-bottom: 40rpx;
}

.help-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: white;
  border-radius: 12rpx;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  border: none;
}

/* 历史区块 */
.history-section {
  margin-bottom: 40rpx;
}

.clear-btn {
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

.history-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.history-item {
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.history-item:last-child {
  border-bottom: none;
}

.history-item:active {
  background: #f8f8f8;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.history-type {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.history-status {
  font-size: 24rpx;
}

.history-time {
  font-size: 24rpx;
  color: #999;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background: white;
  border-radius: 16rpx;
  padding: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #333;
}

/* 底部按钮 */
.footer {
  margin-top: 40rpx;
  padding-bottom: 40rpx;
}

.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 80rpx;
  background: #95a5a6;
  color: white;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}
