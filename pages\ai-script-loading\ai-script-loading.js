// AI剧本生成Loading页面逻辑
Page({
  data: {
    roomId: '',
    progress: 0,
    errorMessage: '',
    steps: [
      { id: 1, text: '背景故事生成完成', status: 'pending' },
      { id: 2, text: '角色设定创建完成', status: 'pending' },
      { id: 3, text: '线索系统构建完成', status: 'pending' },
      { id: 4, text: '人物关系网络生成中...', status: 'pending' },
      { id: 5, text: '剧情分支优化待处理', status: 'pending' }
    ]
  },

  onLoad(options) {
    const { roomId } = options;
    this.setData({ roomId });
    
    // 开始生成剧本
    this.startScriptGeneration();
  },

  // 开始剧本生成
  async startScriptGeneration() {
    try {
      // 模拟AI剧本生成过程
      await this.simulateGeneration();
      
      // 生成完成，跳转到角色分配页面
      this.navigateToRoleAssignment();
    } catch (error) {
      console.error('剧本生成失败:', error);
      this.handleGenerationError(error.message || '生成过程中出现未知错误');
    }
  },

  // 模拟生成过程
  async simulateGeneration() {
    const steps = this.data.steps;
    
    for (let i = 0; i < steps.length; i++) {
      // 更新当前步骤为处理中
      steps[i].status = 'processing';
      this.setData({ steps });
      
      // 模拟处理时间
      const processingTime = 1000 + Math.random() * 2000; // 1-3秒
      await new Promise(resolve => setTimeout(resolve, processingTime));
      
      // 更新进度
      const newProgress = Math.floor(((i + 1) / steps.length) * 100);
      this.updateProgress(newProgress);
      
      // 标记当前步骤完成
      steps[i].status = 'completed';
      this.setData({ steps });
      
      // 随机模拟失败（5%概率）
      if (Math.random() < 0.05) {
        throw new Error(`在"${steps[i].text}"步骤中发生错误`);
      }
    }
  },

  // 更新进度
  updateProgress(targetProgress) {
    const currentProgress = this.data.progress;
    const step = (targetProgress - currentProgress) / 20; // 分20步更新
    
    const updateInterval = setInterval(() => {
      const newProgress = this.data.progress + step;
      if (newProgress >= targetProgress) {
        this.setData({ progress: targetProgress });
        clearInterval(updateInterval);
      } else {
        this.setData({ progress: Math.floor(newProgress) });
      }
    }, 50);
  },

  // 处理生成错误
  handleGenerationError(message) {
    this.setData({ errorMessage: message });
    
    const dialog = this.selectComponent('#error-dialog');
    dialog.showDialog();
  },

  // 重试生成
  retryGeneration() {
    // 重置状态
    this.setData({
      progress: 0,
      errorMessage: '',
      steps: this.data.steps.map(step => ({ ...step, status: 'pending' }))
    });
    
    // 重新开始生成
    this.startScriptGeneration();
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },

  // 跳转到角色分配页面
  navigateToRoleAssignment() {
    setTimeout(() => {
      wx.redirectTo({
        url: `/pages/role-assignment/role-assignment?roomId=${this.data.roomId}&maxPlayers=${this.data.maxPlayers || 6}`
      });
    }, 1000);
  },

  // 页面卸载时清理定时器
  onUnload() {
    // 清理可能存在的定时器
    this.clearAllTimers();
  },

  // 清理所有定时器
  clearAllTimers() {
    // 这里可以添加清理定时器的逻辑
  },

  // 显示提示
  showToast(message, theme = 'warning') {
    const toast = this.selectComponent('#t-toast');
    toast.showToast({
      theme,
      message,
      duration: 2000
    });
  }
});
