// 测试准备开始游戏功能
const roomManager = require('../../utils/room-manager');

Page({
  data: {
    testResults: [],
    currentTest: '',
    roomId: '',
    testUsers: [],
    isRunning: false,
    progress: 0,
    successCount: 0,
    totalCount: 0,
    allTestsPassed: false
  },

  onLoad() {
    console.log('🧪 开始测试准备开始游戏功能');
  },

  // 开始运行所有测试
  async startTests() {
    if (this.data.isRunning) return;
    
    this.setData({
      isRunning: true,
      testResults: [],
      progress: 0,
      successCount: 0,
      totalCount: 0,
      allTestsPassed: false
    });

    await this.runAllTests();
  },

  // 运行所有测试
  async runAllTests() {
    const tests = [
      { name: '测试1：房主单人无法开始', test: this.testHostAloneCannotStart },
      { name: '测试2：2人未准备无法开始', test: this.testTwoPlayersNotReadyCannotStart },
      { name: '测试3：2人已准备无法开始', test: this.testTwoPlayersReadyCannotStart },
      { name: '测试4：3人未全部准备无法开始', test: this.testThreePlayersNotAllReadyCannotStart },
      { name: '测试5：3人全部准备可以开始', test: this.testThreePlayersAllReadyCanStart },
      { name: '测试6：6人全部准备可以开始', test: this.testSixPlayersAllReadyCanStart },
      { name: '测试7：准备状态切换测试', test: this.testReadyToggle },
      { name: '测试8：房主不需要准备', test: this.testHostNoNeedReady }
    ];

    for (let i = 0; i < tests.length; i++) {
      const testCase = tests[i];
      await this.runSingleTest(testCase);
      
      // 更新进度
      this.setData({ 
        progress: Math.round(((i + 1) / tests.length) * 100)
      });
    }

    this.showFinalResults();
  },

  // 运行单个测试
  async runSingleTest(testCase) {
    this.setData({ currentTest: testCase.name });
    
    try {
      const result = await testCase.test.call(this);
      this.addTestResult(testCase.name, true, result.message);
      console.log(`✅ ${testCase.name}: ${result.message}`);
    } catch (error) {
      this.addTestResult(testCase.name, false, error.message);
      console.error(`❌ ${testCase.name}: ${error.message}`);
    }

    // 每个测试之间暂停一下
    await new Promise(resolve => setTimeout(resolve, 300));
  },

  // 测试1：房主单人无法开始
  async testHostAloneCannotStart() {
    const { roomId } = roomManager.createRoom({ maxPlayers: 6 });
    const canStart = roomManager.canStartGame(roomId);
    
    if (canStart) {
      throw new Error('房主单人不应该能开始游戏');
    }
    
    return { message: '房主单人正确无法开始游戏' };
  },

  // 测试2：2人未准备无法开始
  async testTwoPlayersNotReadyCannotStart() {
    const { roomId } = roomManager.createRoom({ maxPlayers: 6 });
    
    // 添加第二个玩家
    const user2 = { id: 'user2', nickname: '玩家2' };
    roomManager.joinRoom(roomId, user2, 1);
    
    const canStart = roomManager.canStartGame(roomId);
    
    if (canStart) {
      throw new Error('2人未准备不应该能开始游戏');
    }
    
    return { message: '2人未准备正确无法开始游戏' };
  },

  // 测试3：2人已准备无法开始（人数不足）
  async testTwoPlayersReadyCannotStart() {
    const { roomId } = roomManager.createRoom({ maxPlayers: 6 });
    
    // 添加第二个玩家并准备
    const user2 = { id: 'user2', nickname: '玩家2' };
    roomManager.joinRoom(roomId, user2, 1);
    roomManager.togglePlayerReady(roomId, user2.id);
    
    const canStart = roomManager.canStartGame(roomId);
    
    if (canStart) {
      throw new Error('2人已准备不应该能开始游戏（人数不足）');
    }
    
    return { message: '2人已准备正确无法开始游戏（人数不足）' };
  },

  // 测试4：3人未全部准备无法开始
  async testThreePlayersNotAllReadyCannotStart() {
    const { roomId } = roomManager.createRoom({ maxPlayers: 6 });
    
    // 添加两个玩家
    const user2 = { id: 'user2', nickname: '玩家2' };
    const user3 = { id: 'user3', nickname: '玩家3' };
    roomManager.joinRoom(roomId, user2, 1);
    roomManager.joinRoom(roomId, user3, 2);
    
    // 只有一个玩家准备
    roomManager.togglePlayerReady(roomId, user2.id);
    
    const canStart = roomManager.canStartGame(roomId);
    
    if (canStart) {
      throw new Error('3人未全部准备不应该能开始游戏');
    }
    
    return { message: '3人未全部准备正确无法开始游戏' };
  },

  // 测试5：3人全部准备可以开始
  async testThreePlayersAllReadyCanStart() {
    const { roomId } = roomManager.createRoom({ maxPlayers: 6 });
    
    // 添加两个玩家
    const user2 = { id: 'user2', nickname: '玩家2' };
    const user3 = { id: 'user3', nickname: '玩家3' };
    roomManager.joinRoom(roomId, user2, 1);
    roomManager.joinRoom(roomId, user3, 2);
    
    // 所有非房主玩家准备
    roomManager.togglePlayerReady(roomId, user2.id);
    roomManager.togglePlayerReady(roomId, user3.id);
    
    const canStart = roomManager.canStartGame(roomId);
    
    if (!canStart) {
      throw new Error('3人全部准备应该能开始游戏');
    }
    
    return { message: '3人全部准备正确可以开始游戏' };
  },

  // 测试6：6人全部准备可以开始
  async testSixPlayersAllReadyCanStart() {
    const { roomId } = roomManager.createRoom({ maxPlayers: 6 });
    
    // 添加5个玩家
    for (let i = 2; i <= 6; i++) {
      const user = { id: `user${i}`, nickname: `玩家${i}` };
      roomManager.joinRoom(roomId, user, i - 1);
      roomManager.togglePlayerReady(roomId, user.id);
    }
    
    const canStart = roomManager.canStartGame(roomId);
    
    if (!canStart) {
      throw new Error('6人全部准备应该能开始游戏');
    }
    
    return { message: '6人全部准备正确可以开始游戏' };
  },

  // 测试7：准备状态切换测试
  async testReadyToggle() {
    const { roomId } = roomManager.createRoom({ maxPlayers: 6 });
    
    const user2 = { id: 'user2', nickname: '玩家2' };
    roomManager.joinRoom(roomId, user2, 1);
    
    // 测试准备
    let room = roomManager.togglePlayerReady(roomId, user2.id);
    let player = room.players.get(user2.id);
    
    if (!player.isReady) {
      throw new Error('玩家准备状态切换失败');
    }
    
    // 测试取消准备
    room = roomManager.togglePlayerReady(roomId, user2.id);
    player = room.players.get(user2.id);
    
    if (player.isReady) {
      throw new Error('玩家取消准备状态切换失败');
    }
    
    return { message: '准备状态切换功能正常' };
  },

  // 测试8：房主不需要准备
  async testHostNoNeedReady() {
    const { roomId } = roomManager.createRoom({ maxPlayers: 6 });
    const currentUser = roomManager.getCurrentUser();
    
    // 房主尝试准备（应该无效果）
    const room = roomManager.togglePlayerReady(roomId, currentUser.id);
    const hostPlayer = room.players.get(currentUser.id);
    
    // 房主的准备状态应该保持为true（房主默认准备）
    if (!hostPlayer.isReady) {
      throw new Error('房主应该默认准备状态');
    }
    
    return { message: '房主默认准备状态正常' };
  },

  // 添加测试结果
  addTestResult(testName, success, message) {
    const results = this.data.testResults;
    results.push({
      name: testName,
      success,
      message,
      time: new Date().toLocaleTimeString()
    });

    // 计算统计数据
    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;
    const allTestsPassed = successCount === totalCount;

    this.setData({
      testResults: results,
      successCount,
      totalCount,
      allTestsPassed
    });
  },

  // 显示最终结果
  showFinalResults() {
    const { successCount, totalCount, allTestsPassed } = this.data;

    const title = allTestsPassed ? '🎉 所有测试通过！' : '⚠️ 部分测试失败';
    const content = `通过: ${successCount}/${totalCount}`;

    wx.showModal({
      title,
      content,
      showCancel: false
    });

    this.setData({
      currentTest: '测试完成',
      isRunning: false
    });
  },

  // 清除测试结果
  clearResults() {
    this.setData({
      testResults: [],
      currentTest: '',
      progress: 0,
      successCount: 0,
      totalCount: 0,
      allTestsPassed: false
    });
  }
});
