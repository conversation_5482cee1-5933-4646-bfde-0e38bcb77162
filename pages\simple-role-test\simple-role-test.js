// 简单的角色分配测试页面
Page({
  data: {
    testResult: '',
    isLoading: false
  },

  onLoad() {
    console.log('简单角色分配测试页面加载');
  },

  // 测试角色分配功能
  async testRoleAssignment() {
    this.setData({ 
      isLoading: true,
      testResult: '开始测试...'
    });

    try {
      // 1. 测试AI服务加载
      this.updateResult('1. 加载AI服务...');
      const aiService = require('../../utils/ai-service-simple');
      this.updateResult('✅ AI服务加载成功');

      // 2. 测试房间管理器
      this.updateResult('2. 测试房间管理器...');
      const roomManager = require('../../utils/room-manager');
      
      // 创建测试房间
      const roomResult = roomManager.createRoom({
        gameMode: '经典推理模式',
        maxPlayers: 6
      });
      this.updateResult(`✅ 房间创建成功: ${roomResult.roomId}`);

      // 添加测试玩家
      const testPlayers = [
        { id: 'player_2', nickname: '玩家2' },
        { id: 'player_3', nickname: '玩家3' },
        { id: 'player_4', nickname: '玩家4' },
        { id: 'player_5', nickname: '玩家5' },
        { id: 'player_6', nickname: '玩家6' }
      ];

      for (const player of testPlayers) {
        roomManager.joinRoom(roomResult.roomId, player);
      }
      this.updateResult('✅ 测试玩家添加完成');

      // 3. 测试剧本生成
      this.updateResult('3. 生成测试剧本...');
      const scriptData = await aiService.generateScript({
        storyType: 'mystery',
        playerCount: 6,
        difficulty: 'medium',
        theme: '神秘庄园'
      });
      this.updateResult(`✅ 剧本生成成功: ${scriptData.storyInfo?.title || '未知标题'}`);

      // 4. 测试角色分配
      this.updateResult('4. 测试角色分配...');
      const room = roomManager.getRoomInfo(roomResult.roomId);
      const playerIds = Array.from(room.players.keys());
      
      const assignmentResult = await aiService.generateRoleAssignment(scriptData, playerIds);
      this.updateResult(`✅ 角色分配成功: ${assignmentResult.assignments.length}个角色已分配`);

      // 5. 验证结果
      this.updateResult('5. 验证分配结果...');
      
      if (assignmentResult.assignments.length !== scriptData.characters.length) {
        throw new Error(`角色数量不匹配: ${assignmentResult.assignments.length} vs ${scriptData.characters.length}`);
      }

      // 显示分配详情
      let details = '\n📋 分配详情:\n';
      assignmentResult.assignments.forEach((assignment, index) => {
        const player = room.players.get(assignment.playerId);
        const character = scriptData.characters.find(c => c.id === assignment.characterId);
        details += `${index + 1}. ${player?.nickname || assignment.playerId} → ${character?.name || assignment.characterId}\n`;
      });

      this.updateResult('✅ 所有测试通过!' + details);

    } catch (error) {
      console.error('测试失败:', error);
      this.updateResult(`❌ 测试失败: ${error.message}`);
    } finally {
      this.setData({ isLoading: false });
    }
  },

  // 更新测试结果显示
  updateResult(message) {
    const currentResult = this.data.testResult;
    this.setData({
      testResult: currentResult + '\n' + message
    });
    console.log(message);
  },

  // 清空结果
  clearResult() {
    this.setData({
      testResult: ''
    });
  },

  // 进入角色分配页面
  goToRoleAssignment() {
    wx.navigateTo({
      url: '/pages/role-assignment/role-assignment?roomId=test123'
    });
  },

  // 进入完整测试页面
  goToFullTest() {
    wx.navigateTo({
      url: '/pages/test-role-assignment/test-role-assignment'
    });
  }
});
