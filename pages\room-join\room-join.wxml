<!--加入房间页面-->
<view class="join-room-container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <t-icon name="chevron-left" size="48rpx" color="white" bind:tap="goBack" />
      <view class="navbar-title">加入房间</view>
      <view style="width: 48rpx;"></view>
    </view>
  </view>

  <view class="join-content">
    <!-- 输入房间号 -->
    <view class="input-section">
      <view class="section-title">
        <t-icon name="door-open" size="40rpx" color="#667eea" />
        <text>输入房间号</text>
      </view>
      
      <view class="room-input-container">
        <t-input 
          placeholder="请输入6位房间号" 
          value="{{roomId}}"
          bind:change="onRoomIdChange"
          maxlength="6"
          type="number"
          class="room-input"
          align="center"
        />
      </view>
      
      <view class="input-tips">
        <t-icon name="info-circle" size="24rpx" color="#999" />
        <text>房间号由房主创建房间时生成</text>
      </view>
    </view>

    <!-- 房间密码（可选） -->
    <view class="password-section" wx:if="{{showPasswordInput}}">
      <view class="section-title">
        <t-icon name="lock" size="40rpx" color="#ffc107" />
        <text>房间密码</text>
      </view>
      
      <view class="password-input-container">
        <t-input 
          placeholder="请输入房间密码" 
          value="{{password}}"
          bind:change="onPasswordChange"
          type="password"
          maxlength="10"
          class="password-input"
        />
      </view>
    </view>

    <!-- 快速加入 -->
    <view class="quick-join-section">
      <view class="section-title">
        <t-icon name="flash" size="40rpx" color="#52c41a" />
        <text>快速加入</text>
      </view>
      
      <view class="quick-join-desc">随机加入一个等待中的房间</view>
      
      <t-button 
        theme="default" 
        size="large" 
        bind:tap="quickJoin"
        loading="{{quickJoining}}"
        class="quick-join-btn"
      >
        <t-icon name="shuffle" slot="prefixIcon" />
        {{quickJoining ? '搜索中...' : '快速加入'}}
      </t-button>
    </view>

    <!-- 最近房间 -->
    <view class="recent-rooms-section" wx:if="{{recentRooms.length > 0}}">
      <view class="section-title">
        <t-icon name="history" size="40rpx" color="#722ed1" />
        <text>最近房间</text>
      </view>
      
      <view class="recent-rooms-list">
        <view 
          class="recent-room-item"
          wx:for="{{recentRooms}}" 
          wx:key="roomId"
          bind:tap="joinRecentRoom"
          data-room-id="{{item.roomId}}"
        >
          <view class="room-info">
            <view class="room-name">{{item.roomName}}</view>
            <view class="room-details">
              <text class="room-id">房间号: {{item.roomId}}</text>
              <text class="room-time">{{item.lastJoinTime}}</text>
            </view>
          </view>
          <view class="room-status">
            <t-tag 
              theme="{{item.status === 'waiting' ? 'success' : 'warning'}}" 
              size="small"
            >
              {{item.status === 'waiting' ? '等待中' : '游戏中'}}
            </t-tag>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-actions">
    <t-button 
      theme="primary" 
      size="large" 
      bind:tap="joinRoom"
      loading="{{joining}}"
      disabled="{{!roomId || roomId.length !== 6}}"
    >
      {{joining ? '加入中...' : '加入房间'}}
    </t-button>
  </view>
</view>

<!-- 房间信息预览弹窗 -->
<t-dialog 
  id="room-preview-dialog"
  title="房间信息"
  visible="{{showRoomPreview}}"
  bind:close="closeRoomPreview"
  bind:confirm="confirmJoinRoom"
  confirmText="加入房间"
>
  <view class="room-preview" wx:if="{{previewRoomInfo}}">
    <view class="preview-item">
      <text class="preview-label">房间名称:</text>
      <text class="preview-value">{{previewRoomInfo.roomName}}</text>
    </view>
    <view class="preview-item">
      <text class="preview-label">游戏风格:</text>
      <text class="preview-value">{{previewRoomInfo.gameStyle}}</text>
    </view>
    <view class="preview-item">
      <text class="preview-label">难度等级:</text>
      <text class="preview-value">{{previewRoomInfo.difficulty}}</text>
    </view>
    <view class="preview-item">
      <text class="preview-label">当前人数:</text>
      <text class="preview-value">{{previewRoomInfo.currentPlayers}}/{{previewRoomInfo.maxPlayers}}</text>
    </view>
    <view class="preview-item">
      <text class="preview-label">房间状态:</text>
      <t-tag 
        theme="{{previewRoomInfo.status === 'waiting' ? 'success' : 'warning'}}" 
        size="small"
      >
        {{previewRoomInfo.status === 'waiting' ? '等待中' : '游戏中'}}
      </t-tag>
    </view>
  </view>
</t-dialog>

<!-- 提示组件 -->
<t-toast id="t-toast" />
