// AI功能演示页面
const aiService = require('../../utils/ai-service');
const errorHandler = require('../../utils/error-handler');

Page({
  data: {
    // 演示状态
    currentDemo: '',
    loading: false,
    
    // 剧本生成演示
    scriptParams: {
      storyType: 'mystery',
      playerCount: 6,
      difficulty: 'medium',
      theme: '现代都市悬疑',
      specialRequirements: '',
      gameSettings: {
        duration: '60-90分钟',
        rounds: '3轮',
        truthDare: true,
        miniGame: true
      }
    },
    generatedScript: null,
    
    // 角色分配演示
    mockPlayerIds: ['player_1', 'player_2', 'player_3', 'player_4', 'player_5', 'player_6'],
    assignmentResult: null,
    
    // 动态内容演示
    gameContext: {
      storyBackground: '神秘庄园谋杀案',
      currentPhase: '第二轮讨论',
      aliveCharacters: ['艾米丽', '约翰', '玛丽', '汤姆'],
      eliminatedCharacters: ['杰克', '莉莉']
    },
    dynamicContent: null,
    
    // 演示选项
    storyTypes: [
      { value: 'mystery', label: '悬疑推理' },
      { value: 'horror', label: '恐怖惊悚' },
      { value: 'romance', label: '浪漫爱情' },
      { value: 'adventure', label: '冒险探索' },
      { value: 'comedy', label: '轻松喜剧' },
      { value: 'fantasy', label: '奇幻魔法' }
    ],
    difficulties: [
      { value: 'easy', label: '简单' },
      { value: 'medium', label: '中等' },
      { value: 'hard', label: '困难' }
    ]
  },

  onLoad() {
    console.log('AI功能演示页面加载');
  },

  // 演示剧本生成
  async demoScriptGeneration() {
    this.setData({ 
      currentDemo: 'script',
      loading: true,
      generatedScript: null
    });

    try {
      console.log('开始演示剧本生成...');
      const startTime = Date.now();
      
      const scriptData = await aiService.generateScript(this.data.scriptParams);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`剧本生成完成，耗时: ${duration}ms`);
      console.log('生成的剧本:', scriptData);
      
      this.setData({
        generatedScript: scriptData,
        loading: false
      });

      wx.showToast({
        title: `生成成功！耗时${Math.round(duration/1000)}秒`,
        icon: 'success',
        duration: 2000
      });

    } catch (error) {
      console.error('剧本生成演示失败:', error);
      this.setData({ loading: false });
      
      errorHandler.showError(error, 'AI剧本生成演示', this);
    }
  },

  // 演示角色分配
  async demoRoleAssignment() {
    if (!this.data.generatedScript) {
      wx.showToast({
        title: '请先生成剧本',
        icon: 'none'
      });
      return;
    }

    this.setData({ 
      currentDemo: 'assignment',
      loading: true,
      assignmentResult: null
    });

    try {
      console.log('开始演示角色分配...');
      const startTime = Date.now();
      
      const assignmentResult = await aiService.generateRoleAssignment(
        this.data.generatedScript, 
        this.data.mockPlayerIds
      );
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`角色分配完成，耗时: ${duration}ms`);
      console.log('分配结果:', assignmentResult);
      
      this.setData({
        assignmentResult: assignmentResult,
        loading: false
      });

      wx.showToast({
        title: `分配成功！耗时${Math.round(duration/1000)}秒`,
        icon: 'success',
        duration: 2000
      });

    } catch (error) {
      console.error('角色分配演示失败:', error);
      this.setData({ loading: false });
      
      errorHandler.showError(error, 'AI角色分配演示', this);
    }
  },

  // 演示动态内容生成
  async demoDynamicContent() {
    this.setData({ 
      currentDemo: 'dynamic',
      loading: true,
      dynamicContent: null
    });

    try {
      console.log('开始演示动态内容生成...');
      const startTime = Date.now();
      
      const dynamicContent = await aiService.generateDynamicContent(
        'truthQuestions', 
        this.data.gameContext
      );
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`动态内容生成完成，耗时: ${duration}ms`);
      console.log('生成内容:', dynamicContent);
      
      this.setData({
        dynamicContent: dynamicContent,
        loading: false
      });

      wx.showToast({
        title: `生成成功！耗时${Math.round(duration/1000)}秒`,
        icon: 'success',
        duration: 2000
      });

    } catch (error) {
      console.error('动态内容生成演示失败:', error);
      this.setData({ loading: false });
      
      errorHandler.showError(error, 'AI动态内容生成演示', this);
    }
  },

  // 更新剧本参数
  onStoryTypeChange(e) {
    this.setData({
      'scriptParams.storyType': this.data.storyTypes[e.detail.value].value
    });
  },

  onDifficultyChange(e) {
    this.setData({
      'scriptParams.difficulty': this.data.difficulties[e.detail.value].value
    });
  },

  onPlayerCountChange(e) {
    this.setData({
      'scriptParams.playerCount': parseInt(e.detail.value)
    });
  },

  onThemeInput(e) {
    this.setData({
      'scriptParams.theme': e.detail.value
    });
  },

  onRequirementsInput(e) {
    this.setData({
      'scriptParams.specialRequirements': e.detail.value
    });
  },

  // 清除演示数据
  clearDemo() {
    this.setData({
      currentDemo: '',
      generatedScript: null,
      assignmentResult: null,
      dynamicContent: null
    });

    wx.showToast({
      title: '演示数据已清除',
      icon: 'success'
    });
  },

  // 查看详细数据
  viewScriptDetail() {
    if (this.data.generatedScript) {
      wx.showModal({
        title: '剧本详情',
        content: JSON.stringify(this.data.generatedScript, null, 2),
        showCancel: false,
        confirmText: '确定'
      });
    }
  },

  viewAssignmentDetail() {
    if (this.data.assignmentResult) {
      wx.showModal({
        title: '分配详情',
        content: JSON.stringify(this.data.assignmentResult, null, 2),
        showCancel: false,
        confirmText: '确定'
      });
    }
  },

  viewDynamicDetail() {
    if (this.data.dynamicContent) {
      wx.showModal({
        title: '动态内容详情',
        content: JSON.stringify(this.data.dynamicContent, null, 2),
        showCancel: false,
        confirmText: '确定'
      });
    }
  },

  // 返回首页
  goHome() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  }
});
