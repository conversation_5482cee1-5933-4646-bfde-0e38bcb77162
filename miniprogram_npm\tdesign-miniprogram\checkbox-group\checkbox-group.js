import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";const{prefix:prefix}=config,name=`${prefix}-checkbox-group`;let CheckBoxGroup=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`],this.relations={"../checkbox/checkbox":{type:"descendant"}},this.data={prefix:prefix,classPrefix:name,checkboxOptions:[]},this.properties=props,this.observers={value(){this.updateChildren()},options(){this.initWithOptions()},disabled(e){var t;(null===(t=this.data.options)||void 0===t?void 0:t.length)?this.initWithOptions():this.getChildren().forEach(t=>{t.setDisabled(e)})}},this.lifetimes={ready(){this.setCheckall()}},this.controlledProps=[{key:"value",event:"change"}],this.$checkAll=null,this.methods={getChildren(){let e=this.$children;return e.length||(e=this.selectAllComponents(`.${prefix}-checkbox-option`)),e||[]},updateChildren(){const e=this.getChildren(),{value:t}=this.data;e.length>0&&(e.forEach(e=>{!e.data.checkAll&&e.setData({checked:null==t?void 0:t.includes(e.data.value)})}),e.some(e=>e.data.checkAll)&&this.setCheckall())},updateValue({value:e,checked:t,checkAll:l,item:i,indeterminate:a}){let{value:s}=this.data;const{max:n}=this.data,c=new Set(this.getChildren().map(e=>e.data.value));if(s=s.filter(e=>c.has(e)),!n||!t||s.length!==n){if(l){const e=this.getChildren();s=!t&&a?e.filter(({data:e})=>!(e.disabled&&!s.includes(e.value))).map(e=>e.data.value):e.filter(({data:e})=>e.disabled?s.includes(e.value):t&&!e.checkAll).map(({data:e})=>e.value)}else if(t)s=s.concat(e);else{const t=s.findIndex(t=>t===e);s.splice(t,1)}this._trigger("change",{value:s,context:i})}},initWithOptions(){const{options:e,value:t,keys:l}=this.data;if(!(null==e?void 0:e.length)||!Array.isArray(e))return;const i=e.map(e=>{var i,a,s;return["number","string"].includes(typeof e)?{label:`${e}`,value:e,checked:null==t?void 0:t.includes(e)}:Object.assign(Object.assign({},e),{label:e[null!==(i=null==l?void 0:l.label)&&void 0!==i?i:"label"],value:e[null!==(a=null==l?void 0:l.value)&&void 0!==a?a:"value"],checked:null==t?void 0:t.includes(e[null!==(s=null==l?void 0:l.value)&&void 0!==s?s:"value"])})});this.setData({checkboxOptions:i})},handleInnerChildChange(e){var t;const{item:l}=e.target.dataset,{checked:i}=e.detail,a={};l.checkAll&&(a.indeterminate=null===(t=this.$checkAll)||void 0===t?void 0:t.data.indeterminate),this.updateValue(Object.assign(Object.assign(Object.assign({},l),{checked:i,item:l}),a))},setCheckall(){const e=this.getChildren();if(this.$checkAll||(this.$checkAll=e.find(e=>e.data.checkAll)),!this.$checkAll)return;const{value:t}=this.data,l=new Set(null==t?void 0:t.filter(e=>e!==this.$checkAll.data.value)),i=e.every(e=>!!e.data.checkAll||l.has(e.data.value));this.$checkAll.setData({checked:l.size>0,indeterminate:!i})}}}};CheckBoxGroup=__decorate([wxComponent()],CheckBoxGroup);export default CheckBoxGroup;