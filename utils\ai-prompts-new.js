/**
 * AI提示词生成器 - 完全自由创作版本
 * 移除固定创意库，改为完全自由的提示词指导
 */
class AIPrompts {
  /**
   * 生成剧本创作提示词
   * @param {Object} params - 参数对象
   * @returns {string} 生成的提示词
   */
  static getScriptGenerationPrompt(params) {
    console.log('🎨 生成自由创作提示词...');
    
    // 使用时间戳作为种子，确保每次都有不同的创意指导
    const seed = Date.now() + Math.random();
    console.log('🎲 使用创意种子:', seed);

    // 移除固定创意库，改为完全自由的提示词指导
    return this.buildDynamicPrompt(params);
  }

  /**
   * 构建动态提示词
   */
  static buildDynamicPrompt(params) {
    const {
      storyType = 'mystery',
      playerCount = 6,
      difficulty = 'medium',
      theme = '现代都市',
      specialRequirements = '',
      gameSettings = {}
    } = params;

    const difficultyDescriptions = {
      'easy': '简单难度 - 线索明显直接，推理逻辑清晰，适合新手玩家快速上手',
      'medium': '中等难度 - 线索适中隐晦，需要一定推理能力，适合有经验的玩家',
      'hard': '困难难度 - 线索复杂隐晦，推理链条复杂，适合资深推理游戏爱好者'
    };

    const storyTypeDescriptions = {
      'mystery': '悬疑推理 - 经典推理剧情，注重逻辑推理和线索分析，营造紧张悬疑的氛围',
      'horror': '恐怖惊悚 - 营造紧张恐怖氛围，但避免过于血腥暴力，重点在心理恐怖',
      'romance': '浪漫爱情 - 融入丰富情感元素，温馨浪漫的故事背景，适合情侣和朋友',
      'adventure': '冒险探索 - 充满未知和探索元素的刺激剧情，强调团队合作和冒险精神',
      'comedy': '轻松喜剧 - 幽默风趣，轻松愉快的游戏氛围，适合聚会娱乐',
      'fantasy': '奇幻魔法 - 魔法世界背景，充满想象力的奇幻设定，脱离现实的奇妙体验'
    };

    // 移除固定创意库，改为完全自由发挥

    return `请为我创建一个前所未有的${storyTypeDescriptions[storyType]}类型多人推理游戏剧本。

## 🎨 背景创意自由发挥指导：
**请完全自由创作故事背景，不要局限于传统套路！可以考虑以下创新方向：**

### 🌟 现代生活场景创新：
- 结合当下热门的生活方式（如直播、外卖、共享经济、远程办公等）
- 利用现代科技元素（智能设备、社交媒体、移动支付、大数据等）
- 融入年轻人熟悉的场景（网咖、密室逃脱、剧本杀店、24小时便利店等）

### 🎭 职业背景多样化：
- 新兴职业（网红、游戏主播、宠物美容师、无人机操作员等）
- 特殊工作环境（深夜值班、节假日加班、出差途中、培训期间等）
- 跨行业合作场景（不同职业的人因某个事件聚集）

### 🎪 情境设定突破：
- 打破时间限制（不一定是一夜之间，可以是几小时、几天等）
- 打破空间限制（不一定是封闭空间，可以是开放但特殊的环境）
- 打破人物关系（不一定都是熟人，可以是陌生人因缘际会）

### 💡 剧情元素创新：
- 现代社会热点话题（但避免敏感内容）
- 生活中的真实矛盾和冲突
- 科技发展带来的新型问题
- 代际差异、文化冲突等社会现象

## 🎯 创意原则：
1. **贴近现实但不平凡** - 基于真实生活但加入戏剧性元素
2. **新颖有趣不老套** - 避免"古堡暴风雨夜"等传统设定
3. **逻辑合理有深度** - 背景设定要经得起推敲
4. **角色动机多元化** - 每个角色都有独特且合理的动机
5. **现代感与趣味性并重** - 既要有时代感，又要有游戏性

## 🚀 背景创作自由发挥示例：
**不要照搬以下示例，而是从中获得灵感，创造全新的背景！**

### 现代生活场景示例：
- 深夜24小时健身房里的健身爱好者们
- 正在进行通宵直播的网红工作室
- 被困在地铁最后一班车上的乘客
- 深夜营业的宠物医院里的紧急情况
- 24小时自助洗衣店里的偶遇事件

### 职业背景示例：
- 游戏公司的通宵测试团队
- 深夜值班的广播电台工作人员
- 正在进行夜间拍摄的短视频团队
- 24小时便利店的夜班员工和顾客
- 深夜加班的外卖配送站点

**请在这些启发下，创造一个完全原创、符合现代生活、有趣且合理的故事背景！**

## 📋 基本设定：
- **剧本类型**: ${storyTypeDescriptions[storyType]}
- **玩家人数**: ${playerCount}人（**必须精确生成${playerCount}个角色，不能多也不能少！**）
- **难度等级**: ${difficultyDescriptions[difficulty]}
- **故事主题**: ${theme}
- **特殊要求**: ${specialRequirements || '无特殊要求，请自由发挥创意'}

## 📝 剧本创作要求：

### 1. 故事背景设计：
- **🎨 创新背景**：创造一个前所未有的故事背景，避免传统套路
- **🌍 现实基础**：基于现代生活，但加入戏剧性和趣味性
- **🎭 氛围营造**：根据剧本类型营造相应的氛围和情绪
- **⏰ 时间设定**：合理安排故事发生的时间框架
- **🏢 空间设计**：设计有利于互动和推理的空间环境

### 2. 角色设计要求：
- **🎯 角色数量：必须精确生成${playerCount}个角色，不能多也不能少**
- **👥 身份多样**：每个角色都有独特的身份、职业和背景
- **💭 动机明确**：每个角色都有清晰的行动动机和目标
- **🔗 关系网络**：角色之间要有合理的关系网络
- **🎪 个性鲜明**：每个角色都要有独特的性格特点
- **🎭 角色深度**：避免"工具人"角色，每个人都要有故事

### 3. 线索与推理设计：
- **🔍 线索分布**：线索要合理分布，每个角色都掌握重要信息
- **🧩 推理逻辑**：确保推理链条清晰合理，符合逻辑
- **🎯 难度适中**：根据设定的难度等级调整线索的隐晦程度
- **🔄 互动设计**：设计促进玩家互动的游戏机制
- **⚡ 节奏控制**：合理安排剧情节奏，保持紧张感

### 4. 游戏机制设计：
- **🗣️ 讨论环节**：设计有效的讨论和信息交换机制
- **🗳️ 投票规则**：制定公平合理的投票和决策规则
- **🎮 小游戏**：设计与剧情相关的小游戏或挑战
- **📱 现代元素**：巧妙融入现代科技和生活元素
- **🏆 胜利条件**：为不同阵营设计明确的胜利条件

## 🚨 关键要求（必须严格遵守）：
1. **🎯 角色数量：必须精确生成${playerCount}个角色，不能多也不能少！**
2. **📝 JSON格式：必须输出标准JSON格式，不能包含任何注释（// 或 /* */）**
3. **🚫 禁止添加：不要在JSON前后添加任何解释文字、说明或注释**
4. **✅ 字符转义：确保所有字符串都正确转义，特别是反斜杠和引号**
5. **🎭 角色质量：所有角色都要有明确的动机和目标，避免"工具人"角色**
6. **🔍 线索分布：线索分布要均匀，每个角色都要有重要信息**
7. **🎪 故事质量：故事要有多个可能的嫌疑人，增加推理难度**
8. **📖 语言风格：语言要生动有趣，避免枯燥的描述**

## 🎯 输出要求：
**请直接输出标准JSON格式，不要添加任何其他内容、注释或解释！**
**JSON必须可以直接被JSON.parse()解析，不能有任何语法错误！**

现在请开始创作这个令人着迷的推理游戏剧本！`;
  }

  // 移除固定创意库，改为完全自由的AI创作
}

module.exports = AIPrompts;
