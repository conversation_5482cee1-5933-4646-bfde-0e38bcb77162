// pages/persistence-demo/persistence-demo.js
const aiService = require('../../utils/ai-service');
const errorHandler = require('../../utils/error-handler');

Page({
  data: {
    // 演示数据
    currentInfluence: 65,
    playerRole: '管家',
    roleStatus: '中等',
    voteResult: '深入调查神秘访客',
    currentRound: 2,
    plotImportance: '高',
    plotImportanceClass: 'high', // 用于CSS类名
    
    // 坚持机制选择
    persistenceOptions: [],
    selectedOption: null,
    
    // 影响力分析
    influenceAnalysis: null,
    
    // UI状态
    loading: false,
    showInfluenceDetail: false,
    countdown: 30,
    countdownTimer: null,
    
    // 演示配置
    demoConfigs: [
      {
        name: '高影响力贵族',
        config: {
          currentInfluence: 85,
          playerRole: '庄园主',
          roleStatus: '极高',
          voteResult: '立即搜查所有房间',
          characterBackground: '庄园的主人，拥有绝对权威'
        }
      },
      {
        name: '中等影响力管家',
        config: {
          currentInfluence: 55,
          playerRole: '管家',
          roleStatus: '中等',
          voteResult: '深入调查神秘访客',
          characterBackground: '庄园的忠实管家，掌握许多秘密'
        }
      },
      {
        name: '低影响力仆人',
        config: {
          currentInfluence: 25,
          playerRole: '女仆',
          roleStatus: '低等',
          voteResult: '继续观察等待',
          characterBackground: '年轻的女仆，刚来庄园不久'
        }
      }
    ]
  },

  onLoad() {
    console.log('个人坚持机制演示页面加载');
    this.updatePlotImportanceClass();
    this.loadDefaultPersistenceOptions();
  },

  /**
   * 更新剧情重要程度的CSS类名
   */
  updatePlotImportanceClass() {
    const importanceMap = {
      '高': 'high',
      '中': 'medium',
      '低': 'low'
    };

    this.setData({
      plotImportanceClass: importanceMap[this.data.plotImportance] || 'medium'
    });
  },

  onUnload() {
    // 清理定时器
    if (this.data.countdownTimer) {
      clearInterval(this.data.countdownTimer);
    }
  },

  /**
   * 加载默认的坚持机制选择
   */
  loadDefaultPersistenceOptions() {
    const persistenceContext = {
      voteResult: this.data.voteResult,
      playerRole: this.data.playerRole,
      currentInfluence: this.data.currentInfluence,
      roleStatus: this.data.roleStatus,
      currentRound: this.data.currentRound,
      plotImportance: this.data.plotImportance,
      characterBackground: '庄园的忠实管家，掌握许多秘密'
    };

    const defaultOptions = aiService.getDefaultPersistenceOptions(persistenceContext);
    
    this.setData({
      persistenceOptions: defaultOptions.persistenceOptions,
      influenceAnalysis: defaultOptions.influenceAnalysis,
      selectedOption: defaultOptions.persistenceOptions[0] // 默认选择第一个
    });
  },

  /**
   * 生成AI坚持机制选择
   */
  async generateAIPersistenceOptions() {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const persistenceContext = {
        voteResult: this.data.voteResult,
        playerRole: this.data.playerRole,
        currentInfluence: this.data.currentInfluence,
        roleStatus: this.data.roleStatus,
        currentRound: this.data.currentRound,
        plotImportance: this.data.plotImportance,
        characterBackground: this.getCurrentCharacterBackground()
      };

      console.log('生成AI坚持机制选择，上下文:', persistenceContext);

      const result = await aiService.generatePersistenceOptions(persistenceContext);
      
      console.log('AI坚持机制选择结果:', result);

      this.setData({
        persistenceOptions: result.persistenceOptions || [],
        influenceAnalysis: result.influenceAnalysis,
        selectedOption: result.persistenceOptions?.[0] || null
      });

      wx.showToast({
        title: 'AI选择生成成功',
        icon: 'success'
      });

    } catch (error) {
      console.error('生成AI坚持机制选择失败:', error);
      errorHandler.showError(error, 'AI坚持机制生成', this);
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 选择坚持机制选项
   */
  selectPersistenceOption(e) {
    const index = e.currentTarget.dataset.index;
    const option = this.data.persistenceOptions[index];
    
    if (!option) return;

    // 检查是否有足够的影响力
    if (option.influenceCost > this.data.currentInfluence) {
      wx.showToast({
        title: '影响力不足',
        icon: 'error'
      });
      return;
    }

    this.setData({
      selectedOption: option
    });

    // 添加选择动画效果
    const animation = wx.createAnimation({
      duration: 200,
      timingFunction: 'ease-out'
    });
    
    animation.scale(1.05).step();
    animation.scale(1).step();
    
    this.setData({
      [`optionAnimation${index}`]: animation.export()
    });
  },

  /**
   * 确认坚持选择
   */
  confirmPersistenceChoice() {
    if (!this.data.selectedOption) {
      wx.showToast({
        title: '请先选择一个选项',
        icon: 'error'
      });
      return;
    }

    const option = this.data.selectedOption;
    const newInfluence = this.data.currentInfluence - option.influenceCost;

    wx.showModal({
      title: '确认选择',
      content: `确定选择"${option.name}"吗？\n将消耗${option.influenceCost}点影响力\n剩余影响力：${newInfluence}点`,
      success: (res) => {
        if (res.confirm) {
          this.executePersistenceChoice(option, newInfluence);
        }
      }
    });
  },

  /**
   * 执行坚持选择
   */
  executePersistenceChoice(option, newInfluence) {
    // 更新影响力
    this.setData({
      currentInfluence: newInfluence
    });

    // 显示选择结果
    const effects = option.effects.join('\n• ');
    
    wx.showModal({
      title: '选择生效',
      content: `${option.name}已生效！\n\n效果：\n• ${effects}`,
      showCancel: false,
      success: () => {
        // 重新加载选项（基于新的影响力）
        this.loadDefaultPersistenceOptions();
      }
    });
  },

  /**
   * 切换演示配置
   */
  switchDemoConfig(e) {
    const index = e.currentTarget.dataset.index;
    const config = this.data.demoConfigs[index].config;

    this.setData({
      ...config,
      selectedOption: null
    });

    // 更新CSS类名映射
    this.updatePlotImportanceClass();

    // 重新加载选项
    this.loadDefaultPersistenceOptions();

    wx.showToast({
      title: `切换到${this.data.demoConfigs[index].name}`,
      icon: 'success'
    });
  },

  /**
   * 开始倒计时
   */
  startCountdown() {
    if (this.data.countdownTimer) {
      clearInterval(this.data.countdownTimer);
    }

    this.setData({ countdown: 30 });

    const timer = setInterval(() => {
      const countdown = this.data.countdown - 1;
      this.setData({ countdown });

      if (countdown <= 0) {
        clearInterval(timer);
        this.setData({ countdownTimer: null });
        
        // 自动选择当前选中的选项
        if (this.data.selectedOption) {
          this.confirmPersistenceChoice();
        }
      }
    }, 1000);

    this.setData({ countdownTimer: timer });
  },

  /**
   * 停止倒计时
   */
  stopCountdown() {
    if (this.data.countdownTimer) {
      clearInterval(this.data.countdownTimer);
      this.setData({ 
        countdownTimer: null,
        countdown: 30
      });
    }
  },

  /**
   * 切换影响力详情显示
   */
  toggleInfluenceDetail() {
    this.setData({
      showInfluenceDetail: !this.data.showInfluenceDetail
    });
  },

  /**
   * 计算影响力
   */
  async calculateInfluence() {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const influenceContext = {
        characterName: this.data.playerRole,
        characterType: '特殊角色',
        roleStatus: this.data.roleStatus,
        currentInfluence: this.data.currentInfluence,
        performanceScore: 75,
        speakCount: 8,
        clueShared: 3,
        agreementCount: 5,
        correctDeductions: 2,
        voteAccuracy: 80,
        teamworkScore: 70,
        roleplayScore: 85,
        persistenceUsed: 1
      };

      const result = await aiService.calculateInfluence(influenceContext);
      
      console.log('影响力计算结果:', result);

      wx.showModal({
        title: '影响力分析',
        content: `当前影响力：${result.currentInfluence}点\n影响力等级：${result.influenceLevel}\n等级描述：${result.levelDescription}`,
        showCancel: false
      });

    } catch (error) {
      console.error('计算影响力失败:', error);
      errorHandler.showError(error, '影响力计算', this);
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 获取当前角色背景
   */
  getCurrentCharacterBackground() {
    const backgrounds = {
      '庄园主': '庄园的主人，拥有绝对权威和决策权',
      '管家': '庄园的忠实管家，掌握许多秘密和内部信息',
      '女仆': '年轻的女仆，刚来庄园不久，观察力敏锐',
      '医生': '受人尊敬的医生，具有专业知识和判断力',
      '律师': '精明的律师，善于逻辑推理和辩论',
      '侦探': '经验丰富的私家侦探，推理能力超群'
    };
    
    return backgrounds[this.data.playerRole] || '神秘的角色，身份不明';
  }
});
