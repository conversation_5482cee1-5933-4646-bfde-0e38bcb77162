// 通用工具函数
const common = {
  
  // 格式化时间
  formatTime(timestamp, format = 'YYYY-MM-DD HH:mm:ss') {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');
    const second = date.getSeconds().toString().padStart(2, '0');

    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hour)
      .replace('mm', minute)
      .replace('ss', second);
  },

  // 格式化相对时间
  formatRelativeTime(timestamp) {
    const now = Date.now();
    const diff = now - timestamp;
    const minute = 60 * 1000;
    const hour = 60 * minute;
    const day = 24 * hour;

    if (diff < minute) {
      return '刚刚';
    } else if (diff < hour) {
      return `${Math.floor(diff / minute)}分钟前`;
    } else if (diff < day) {
      return `${Math.floor(diff / hour)}小时前`;
    } else if (diff < 7 * day) {
      return `${Math.floor(diff / day)}天前`;
    } else {
      return this.formatTime(timestamp, 'MM-DD');
    }
  },

  // 生成随机ID
  generateId(length = 8) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  // 生成房间号
  generateRoomId() {
    return Math.floor(100000 + Math.random() * 900000).toString();
  },

  // 防抖函数
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  // 节流函数
  throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },

  // 深拷贝
  deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (obj instanceof Array) return obj.map(item => this.deepClone(item));
    if (typeof obj === 'object') {
      const clonedObj = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = this.deepClone(obj[key]);
        }
      }
      return clonedObj;
    }
  },

  // 存储数据
  setStorage(key, data) {
    try {
      wx.setStorageSync(key, data);
      return true;
    } catch (error) {
      console.error('存储数据失败:', error);
      return false;
    }
  },

  // 获取存储数据
  getStorage(key, defaultValue = null) {
    try {
      const data = wx.getStorageSync(key);
      return data || defaultValue;
    } catch (error) {
      console.error('获取存储数据失败:', error);
      return defaultValue;
    }
  },

  // 移除存储数据
  removeStorage(key) {
    try {
      wx.removeStorageSync(key);
      return true;
    } catch (error) {
      console.error('移除存储数据失败:', error);
      return false;
    }
  },

  // 显示Toast
  showToast(title, icon = 'none', duration = 2000) {
    wx.showToast({
      title,
      icon,
      duration
    });
  },

  // 显示Loading
  showLoading(title = '加载中...') {
    wx.showLoading({
      title,
      mask: true
    });
  },

  // 隐藏Loading
  hideLoading() {
    wx.hideLoading();
  },

  // 显示确认对话框
  showConfirm(content, title = '提示') {
    return new Promise((resolve) => {
      wx.showModal({
        title,
        content,
        success: (res) => {
          resolve(res.confirm);
        }
      });
    });
  },

  // 复制到剪贴板
  copyToClipboard(data, showToast = true) {
    wx.setClipboardData({
      data,
      success: () => {
        if (showToast) {
          this.showToast('已复制到剪贴板');
        }
      },
      fail: () => {
        if (showToast) {
          this.showToast('复制失败');
        }
      }
    });
  },

  // 获取系统信息 - 使用新API
  getSystemInfo() {
    return new Promise((resolve, reject) => {
      try {
        // 使用新的API组合获取完整系统信息
        const windowInfo = wx.getWindowInfo ? wx.getWindowInfo() : {};
        const deviceInfo = wx.getDeviceInfo ? wx.getDeviceInfo() : {};
        const appBaseInfo = wx.getAppBaseInfo ? wx.getAppBaseInfo() : {};

        // 合并信息，兼容旧版本
        const systemInfo = {
          ...windowInfo,
          ...deviceInfo,
          ...appBaseInfo,
          // 如果新API不存在，回退到同步API
          ...((!wx.getWindowInfo || !wx.getDeviceInfo || !wx.getAppBaseInfo) ? wx.getSystemInfoSync() : {})
        };

        resolve(systemInfo);
      } catch (error) {
        // 如果新API都不支持，使用旧API作为兜底
        wx.getSystemInfo({
          success: resolve,
          fail: reject
        });
      }
    });
  },

  // 检查网络状态
  checkNetworkStatus() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          resolve(res.networkType !== 'none');
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  },

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // 验证手机号
  validatePhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  },

  // 验证邮箱
  validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // 获取URL参数
  getUrlParams(url) {
    const params = {};
    const urlParts = url.split('?');
    if (urlParts.length > 1) {
      const queryString = urlParts[1];
      const pairs = queryString.split('&');
      pairs.forEach(pair => {
        const [key, value] = pair.split('=');
        params[decodeURIComponent(key)] = decodeURIComponent(value || '');
      });
    }
    return params;
  }
};

module.exports = common;
