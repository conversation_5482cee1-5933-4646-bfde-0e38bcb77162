/* 完整流程测试页面样式 */
.container {
  padding: 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 测试步骤 */
.test-steps {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
}

.steps-title {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  text-align: center;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  padding: 20rpx;
  border-radius: 15rpx;
  background: rgba(255, 255, 255, 0.05);
  border: 2rpx solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.step-item.running {
  background: rgba(255, 193, 7, 0.2);
  border-color: #ffc107;
  animation: pulse 2s infinite;
}

.step-item.success {
  background: rgba(76, 175, 80, 0.2);
  border-color: #4CAF50;
}

.step-item.failed {
  background: rgba(244, 67, 54, 0.2);
  border-color: #f44336;
}

.step-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.step-number {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 24rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-number.active {
  background: #667eea;
  box-shadow: 0 0 20rpx rgba(102, 126, 234, 0.5);
}

.step-status {
  font-size: 32rpx;
}

.step-content {
  flex: 1;
}

.step-name {
  display: block;
  color: white;
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.step-desc {
  display: block;
  color: rgba(255, 255, 255, 0.7);
  font-size: 26rpx;
  margin-bottom: 8rpx;
}

.step-result {
  display: block;
  color: #4CAF50;
  font-size: 24rpx;
  font-weight: bold;
}

/* 控制按钮 */
.controls {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.control-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

.control-btn.primary {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.control-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

/* 测试结果 */
.test-result {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid rgba(255, 255, 255, 0.1);
}

.result-title {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

.result-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.result-status.success {
  background: rgba(76, 175, 80, 0.3);
  color: #4CAF50;
}

.result-status.failed {
  background: rgba(244, 67, 54, 0.3);
  color: #f44336;
}

.result-content {
  margin-bottom: 20rpx;
}

.result-message {
  display: block;
  color: white;
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.result-details {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 15rpx;
  padding: 20rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 26rpx;
}

.detail-value {
  color: white;
  font-size: 26rpx;
  font-weight: bold;
}

.result-actions {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
}

.action-btn {
  flex: 1;
  min-width: 150rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: rgba(102, 126, 234, 0.8);
  color: white;
  font-size: 24rpx;
  border: none;
}

/* 说明信息 */
.info-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  backdrop-filter: blur(10rpx);
}

.info-title {
  display: block;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.info-item {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
  line-height: 1.4;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}
