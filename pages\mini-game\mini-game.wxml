<!-- 你画我猜小游戏页面 -->
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="nav-left"></view>
      <view class="nav-title">🎨 你画我猜</view>
      <view class="nav-right">
        <text class="timer">{{timeLeft}}</text>
      </view>
    </view>
  </view>

  <!-- 页面内容 -->
  <view class="page-content">
    <!-- 游戏说明卡片 -->
    <view class="game-info-card artistic-card">
      <view class="section-header">
        <text class="section-icon">🎯</text>
        <text class="section-title">游戏规则</text>
      </view>
      <view class="game-rules">
        <text class="rule-text">• 当前题目：{{currentTopic}}</text>
        <text class="rule-text">• 画家需要画出题目内容</text>
        <text class="rule-text">• 其他玩家猜测画的内容</text>
        <text class="rule-text">• 猜对可获得线索奖励</text>
      </view>
    </view>

    <!-- 绘画区域 -->
    <view class="drawing-area artistic-card" wx:if="{{isDrawer}}">
      <view class="section-header">
        <text class="section-icon">🖌️</text>
        <text class="section-title">绘画区域</text>
      </view>
      <canvas
        class="drawing-canvas"
        canvas-id="drawingCanvas"
        bindtouchstart="startDraw"
        bindtouchmove="drawing"
        bindtouchend="endDraw"
      ></canvas>
      <view class="drawing-tools">
        <button class="tool-btn" bindtap="clearCanvas">清除</button>
        <button class="tool-btn" bindtap="changeBrushSize">画笔</button>
        <button class="tool-btn" bindtap="changeColor">颜色</button>
      </view>
    </view>

    <!-- 猜测区域 -->
    <view class="guess-area artistic-card" wx:if="{{!isDrawer}}">
      <view class="section-header">
        <text class="section-icon">🤔</text>
        <text class="section-title">猜测答案</text>
      </view>
      <view class="guess-input">
        <input
          class="guess-field"
          placeholder="输入你的猜测..."
          value="{{guessText}}"
          bindinput="onGuessInput"
          bindconfirm="submitGuess"
        />
        <button class="guess-btn" bindtap="submitGuess">提交</button>
      </view>
    </view>

    <!-- 猜测记录 -->
    <view class="guess-history artistic-card">
      <view class="section-header">
        <text class="section-icon">📝</text>
        <text class="section-title">猜测记录</text>
      </view>
      <view class="guess-list">
        <view class="guess-item" wx:for="{{guessHistory}}" wx:key="index">
          <view class="guess-player">{{item.player}}</view>
          <view class="guess-content {{item.correct ? 'correct' : ''}}">
            {{item.guess}}
            <text class="guess-result" wx:if="{{item.correct}}">✅</text>
          </view>
        </view>
        <view class="no-guess" wx:if="{{guessHistory.length === 0}}">
          暂无猜测记录
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <button class="action-btn secondary" bindtap="showGameHelp">
      <text class="btn-icon">❓</text>
      <text>帮助</text>
    </button>
    <button class="action-btn primary" bindtap="skipGame">
      <text class="btn-icon">⏭️</text>
      <text>跳过游戏</text>
    </button>
  </view>
</view>