/* 房间大厅页面样式 */
.lobby-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.lobby-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 30% 40%, rgba(255, 255, 255, 0.15) 0%, transparent 70%),
    radial-gradient(circle at 70% 60%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: rgba(102, 126, 234, 0.9);
  backdrop-filter: blur(20rpx);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  padding-top: calc(20rpx + env(safe-area-inset-top));
}

.room-title {
  color: white;
  font-size: 36rpx;
  font-weight: 600;
}

/* 房间信息区域 */
.room-info-section {
  margin-top: calc(120rpx + env(safe-area-inset-top));
  padding: 40rpx 32rpx;
  position: relative;
  z-index: 1;
}

.room-info-card {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20rpx);
  border-radius: 30rpx;
  padding: 32rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.game-mode-info {
  text-align: center;
}

.mode-title {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.mode-details {
  display: flex;
  justify-content: center;
  gap: 16rpx;
}

/* 玩家网格区域 */
.players-section {
  padding: 0 32rpx;
  position: relative;
  z-index: 1;
  flex: 1;
}

.players-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  max-width: 680rpx;
  margin: 0 auto;
}

.player-slot {
  aspect-ratio: 1;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border-radius: 24rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
}

.player-slot.occupied {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.player-slot:active {
  transform: scale(0.95);
}

.player-status-indicator {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 16rpx;
  height: 16rpx;
  background: #52c41a;
  border-radius: 50%;
  border: 2rpx solid white;
}

.player-avatar {
  margin-bottom: 12rpx;
}

.empty-slot {
  margin-bottom: 12rpx;
  opacity: 0.5;
}

.player-info {
  text-align: center;
}

.player-name {
  color: white;
  font-size: 24rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
  max-width: 120rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.empty-info {
  text-align: center;
}

.empty-text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 20rpx;
}

/* 底部操作区 */
.lobby-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  padding: 32rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  display: flex;
  gap: 24rpx;
  z-index: 100;
}

.ready-btn {
  flex: 2;
}

.invite-btn {
  flex: 1;
}

/* 房间状态 */
.room-status {
  position: fixed;
  bottom: 140rpx;
  left: 32rpx;
  right: 32rpx;
  display: flex;
  justify-content: space-between;
  z-index: 50;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
  background: rgba(0, 0, 0, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  backdrop-filter: blur(10rpx);
}

/* 房间菜单 */
.room-menu {
  background: white;
  border-radius: 16rpx 16rpx 0 0;
  overflow: hidden;
}

/* TDesign组件样式覆盖 */
.t-button--theme-primary {
  background: linear-gradient(45deg, #667eea, #764ba2) !important;
  border: none !important;
}

.t-button--theme-default {
  background: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  border: 2rpx solid rgba(255, 255, 255, 0.3) !important;
  backdrop-filter: blur(10rpx);
}

.t-tag--theme-primary {
  background: rgba(255, 255, 255, 0.3) !important;
  color: white !important;
  border: 1rpx solid rgba(255, 255, 255, 0.4) !important;
}

.t-tag--theme-warning {
  background: rgba(255, 215, 0, 0.8) !important;
  color: #333 !important;
  border: none !important;
}

.t-avatar {
  border: 3rpx solid rgba(255, 255, 255, 0.5) !important;
}

/* 游戏阶段导航 */
.game-phases-section {
  margin: 32rpx;
  margin-bottom: 160rpx;
}

.phases-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: white;
}

.phases-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.phase-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 24rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  min-height: 140rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.phase-card:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.25);
}

.phase-icon {
  margin-bottom: 12rpx;
  text-align: center;
}

.phase-info {
  text-align: center;
}

.phase-name {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 4rpx;
}

.phase-desc {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}

.phase-status {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.phase-status.completed {
  background: rgba(82, 196, 26, 0.2);
  border: 2rpx solid rgba(82, 196, 26, 0.5);
}

.phase-status.active {
  background: rgba(255, 193, 7, 0.2);
  border: 2rpx solid rgba(255, 193, 7, 0.5);
  animation: pulse 2s infinite;
}

/* 按钮样式优化 */
.start-btn, .continue-btn {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%) !important;
  border: none !important;
  font-weight: 600 !important;
  box-shadow: 0 8rpx 24rpx rgba(82, 196, 26, 0.3) !important;
}

.menu-btn {
  background: rgba(255, 255, 255, 0.15) !important;
  border: 2rpx solid rgba(255, 255, 255, 0.3) !important;
  color: white !important;
  backdrop-filter: blur(10rpx) !important;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* ==================== 测试功能面板样式 ==================== */
.test-panel {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx;
  max-height: 80vh;
  overflow-y: auto;
}

.test-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.test-panel-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.test-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.test-option-card {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  border: 2rpx solid #e9ecef;
}

.option-header {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.option-icon {
  font-size: 48rpx;
  width: 60rpx;
  text-align: center;
}

.option-info {
  flex: 1;
}

.option-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.option-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.option-details {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #e9ecef;
}

.detail-text {
  display: block;
  font-size: 24rpx;
  color: #52c41a;
  line-height: 1.6;
  margin-bottom: 8rpx;
}

.detail-text:first-child {
  font-weight: bold;
  margin-bottom: 12rpx;
}

.test-actions {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

.test-action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

.test-action-btn.primary {
  background: linear-gradient(135deg, #52c41a, #389e0d);
  color: white;
}

.test-action-btn.secondary {
  background: #f0f0f0;
  color: #666;
}

.test-action-btn[disabled] {
  background: #f5f5f5;
  color: #ccc;
}
