<!--AI剧情生成页面-->
<view class="story-generator-container">
  <!-- 返回按钮 -->
  <view class="back-button" bind:tap="goBack">
    <text class="back-icon">←</text>
    <text class="back-text">返回</text>
  </view>

  <!-- 标题区域 -->
  <view class="header-section">
    <view class="title-icon">🎭</view>
    <view class="main-title">{{isSingleTestMode ? '单人测试 - AI剧情生成' : 'AI剧情生成'}}</view>
    <view class="subtitle">{{isSingleTestMode ? '第1步：选择剧情类型并生成个性化剧本' : '智能创作，无限可能'}}</view>
  </view>

  <!-- 剧情类型选择 -->
  <view class="story-types-section artistic-card">
    <view class="section-title">
      <text class="title-icon">📚</text>
      <text class="title-text">选择剧情类型</text>
    </view>

    <view class="story-types-grid">
      <view
        wx:for="{{storyTypes}}"
        wx:key="id"
        class="story-type-card {{selectedType === item.id ? 'selected' : ''}}"
        bind:tap="selectStoryType"
        data-type="{{item.id}}"
      >
        <view class="type-icon">{{item.icon}}</view>
        <view class="type-name">{{item.name}}</view>
        <view class="type-desc">{{item.description}}</view>
      </view>
    </view>
  </view>

  <!-- AI优化按钮 -->
  <view class="ai-optimize-section" wx:if="{{selectedType}}">
    <button
      class="ai-optimize-btn {{optimizing ? 'optimizing' : ''}}"
      bind:tap="optimizeStory"
      disabled="{{optimizing}}"
    >
      <text wx:if="{{!optimizing}}" class="btn-icon">🤖</text>
      <text wx:if="{{optimizing}}" class="btn-icon loading">🔄</text>
      <text class="btn-text">{{optimizing ? 'AI优化中...' : 'AI优化剧情'}}</text>
    </button>
  </view>

  <!-- 优化后的剧情显示 -->
  <view class="optimized-story-section artistic-card" wx:if="{{optimizedStory}}">
    <view class="section-title">
      <text class="title-icon">✨</text>
      <text class="title-text">AI优化剧情</text>
    </view>

    <view class="story-content">
      <textarea
        class="story-textarea"
        value="{{optimizedStory}}"
        placeholder="AI优化的剧情将在这里显示..."
        disabled="{{true}}"
        auto-height="{{true}}"
        maxlength="-1"
      ></textarea>
    </view>

    <view class="action-buttons">
      <button class="action-btn secondary" bind:tap="regenerateOptimizedStory">
        <text class="btn-icon">🔄</text>
        <text class="btn-text">重新优化</text>
      </button>
      <button class="action-btn primary" bind:tap="confirmOptimizedStory">
        <text class="btn-icon">✅</text>
        <text class="btn-text">确认使用</text>
      </button>
    </view>
  </view>

</view>


