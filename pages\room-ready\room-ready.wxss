/* 房间准备页面样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
  color: white;
  padding: 40rpx;
}

/* 头部信息 */
.header {
  margin-bottom: 40rpx;
}

.room-info {
  text-align: center;
  margin-bottom: 30rpx;
}

.room-id {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 8rpx;
}

.room-mode {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* 步骤指示器 */
.step-indicator {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.step-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.step-item:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 25rpx;
  right: -50%;
  width: 100%;
  height: 4rpx;
  background: rgba(255, 255, 255, 0.2);
  z-index: 1;
}

.step-item.active:not(:last-child)::after {
  background: #667eea;
}

.step-number {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 24rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
  position: relative;
  z-index: 2;
}

.step-item.active .step-number {
  background: #667eea;
}

.step-title {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

.step-item.active .step-title {
  color: white;
  font-weight: bold;
}

/* 步骤内容 */
.step-content {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
}

.section-title {
  text-align: center;
  margin-bottom: 30rpx;
}

.title-text {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.title-desc {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* 玩家列表 */
.players-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  margin-bottom: 30rpx;
}

.player-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.1);
}

.player-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.avatar-text {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.player-info {
  flex: 1;
}

.player-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 5rpx;
}

.player-status {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}

.ready-indicator {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.5);
}

.ready-indicator.ready {
  background: #4CAF50;
  border-color: #4CAF50;
  color: white;
}

/* 剧本生成 */
.script-generation {
  text-align: center;
}

.generation-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.loading-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.loading-icon.spinning {
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.status-text {
  font-size: 32rpx;
  font-weight: bold;
}

.script-info {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 15rpx;
  padding: 25rpx;
  margin-top: 20rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 28rpx;
}

.info-value {
  color: white;
  font-size: 28rpx;
  font-weight: bold;
}

.preview-btn {
  width: 200rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: rgba(102, 126, 234, 0.8);
  color: white;
  font-size: 26rpx;
  border: none;
  margin-top: 20rpx;
}

/* 准备检查 */
.ready-check {
  text-align: center;
}

.players-ready {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.player-ready-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.player-ready-item.ready {
  background: rgba(76, 175, 80, 0.2);
  border-color: #4CAF50;
}

.player-avatar-small {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
  color: white;
  font-size: 20rpx;
  font-weight: bold;
}

.player-name-small {
  font-size: 24rpx;
  margin-bottom: 8rpx;
}

.ready-status {
  font-size: 28rpx;
  font-weight: bold;
}

.ready-actions {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

.action-btn {
  width: 200rpx;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

.action-btn.primary {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.action-btn[disabled] {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
}

/* 底部操作 */
.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
}

.footer-btn {
  width: 150rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 26rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.room-status {
  flex: 1;
  text-align: center;
}

.status-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 加载遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 40rpx;
  backdrop-filter: blur(10rpx);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
  border-top: 6rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  color: white;
  font-size: 28rpx;
}
