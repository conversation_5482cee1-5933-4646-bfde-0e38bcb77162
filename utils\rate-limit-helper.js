// utils/rate-limit-helper.js
// 频率限制帮助工具

class RateLimitHelper {
  constructor() {
    this.storageKey = 'api_rate_limit_info';
  }

  /**
   * 记录频率限制状态
   */
  recordRateLimit() {
    const rateLimitInfo = {
      timestamp: Date.now(),
      count: this.getRateLimitCount() + 1
    };
    
    try {
      wx.setStorageSync(this.storageKey, rateLimitInfo);
      console.log('📝 记录频率限制:', rateLimitInfo);
    } catch (error) {
      console.warn('无法保存频率限制信息:', error);
    }
  }

  /**
   * 获取频率限制次数
   */
  getRateLimitCount() {
    try {
      const info = wx.getStorageSync(this.storageKey);
      return info ? info.count || 0 : 0;
    } catch (error) {
      return 0;
    }
  }

  /**
   * 获取上次频率限制时间
   */
  getLastRateLimitTime() {
    try {
      const info = wx.getStorageSync(this.storageKey);
      return info ? info.timestamp || 0 : 0;
    } catch (error) {
      return 0;
    }
  }

  /**
   * 检查是否可以发送请求
   */
  canMakeRequest() {
    const lastTime = this.getLastRateLimitTime();
    const now = Date.now();
    const timeSince = now - lastTime;
    
    // 如果距离上次频率限制超过2分钟，认为可以尝试
    return timeSince > 120000; // 2分钟
  }

  /**
   * 获取建议等待时间（毫秒）
   */
  getSuggestedWaitTime() {
    const lastTime = this.getLastRateLimitTime();
    const now = Date.now();
    const timeSince = now - lastTime;
    const waitTime = 120000 - timeSince; // 2分钟总等待时间
    
    return Math.max(0, waitTime);
  }

  /**
   * 获取友好的等待时间描述
   */
  getWaitTimeDescription() {
    const waitTime = this.getSuggestedWaitTime();
    
    if (waitTime <= 0) {
      return '现在可以尝试请求';
    }
    
    const minutes = Math.ceil(waitTime / 60000);
    const seconds = Math.ceil((waitTime % 60000) / 1000);
    
    if (minutes > 0) {
      return `建议等待 ${minutes} 分钟 ${seconds} 秒`;
    } else {
      return `建议等待 ${seconds} 秒`;
    }
  }

  /**
   * 清除频率限制记录
   */
  clearRateLimitInfo() {
    try {
      wx.removeStorageSync(this.storageKey);
      console.log('🧹 清除频率限制记录');
    } catch (error) {
      console.warn('无法清除频率限制记录:', error);
    }
  }

  /**
   * 显示频率限制提示
   */
  showRateLimitTip() {
    const waitTime = this.getWaitTimeDescription();
    const count = this.getRateLimitCount();
    
    wx.showModal({
      title: '请求频率限制',
      content: `检测到API请求频率过高（第${count}次）。\n\n${waitTime}\n\n为了避免继续触发限制，建议：\n1. 等待2分钟后再试\n2. 避免频繁测试\n3. 使用时保持适当间隔`,
      showCancel: true,
      cancelText: '知道了',
      confirmText: '清除记录',
      success: (res) => {
        if (res.confirm) {
          this.clearRateLimitInfo();
          wx.showToast({
            title: '记录已清除',
            icon: 'success'
          });
        }
      }
    });
  }

  /**
   * 获取状态报告
   */
  getStatusReport() {
    const canRequest = this.canMakeRequest();
    const waitTime = this.getWaitTimeDescription();
    const count = this.getRateLimitCount();
    const lastTime = this.getLastRateLimitTime();
    
    return {
      canMakeRequest: canRequest,
      waitTimeDescription: waitTime,
      rateLimitCount: count,
      lastRateLimitTime: lastTime,
      lastRateLimitTimeFormatted: lastTime ? new Date(lastTime).toLocaleString() : '无记录'
    };
  }
}

module.exports = RateLimitHelper;
