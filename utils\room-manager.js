// 房间管理服务
class RoomManager {
  constructor() {
    this.rooms = new Map(); // 存储所有房间
    this.currentUser = null;
  }

  /**
   * 初始化当前用户
   */
  initUser() {
    if (!this.currentUser) {
      this.currentUser = {
        id: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        nickname: `玩家${Math.floor(Math.random() * 1000)}`,
        avatar: ''
      };
    }
    return this.currentUser;
  }

  /**
   * 创建房间
   */
  createRoom(options = {}) {
    const {
      gameMode = '经典推理模式',
      maxPlayers = 12,
      timeLimit = 600,
      rounds = 3
    } = options;

    // 生成房间ID
    const roomId = this.generateRoomId();
    
    // 初始化当前用户
    const user = this.initUser();

    // 创建房间数据
    const room = {
      id: roomId,
      hostId: user.id,
      gameMode,
      maxPlayers,
      timeLimit,
      rounds,
      status: 'waiting', // waiting, playing, finished
      createdAt: new Date(),
      players: new Map(),
      playerSlots: this.initializePlayerSlots(maxPlayers),
      gameStarted: false
    };

    // 先存储房间
    this.rooms.set(roomId, room);

    // 房主自动加入房间
    const updatedRoom = this.joinRoom(roomId, user, 0); // 房主占据第一个位置

    console.log('✅ 房间创建成功:', roomId, updatedRoom);
    return { roomId, room: updatedRoom };
  }

  /**
   * 生成房间ID
   */
  generateRoomId() {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * 初始化玩家座位
   */
  initializePlayerSlots(maxPlayers) {
    const slots = [];
    for (let i = 0; i < maxPlayers; i++) {
      slots.push({
        id: i,
        status: 'empty', // empty, occupied
        player: null
      });
    }
    return slots;
  }

  /**
   * 加入房间
   */
  joinRoom(roomId, player, slotIndex = null) {
    const room = this.rooms.get(roomId);
    if (!room) {
      throw new Error('房间不存在');
    }

    if (room.players.size >= room.maxPlayers) {
      throw new Error('房间已满');
    }

    // 如果没有指定座位，自动分配
    if (slotIndex === null) {
      slotIndex = room.playerSlots.findIndex(slot => slot.status === 'empty');
      if (slotIndex === -1) {
        throw new Error('没有空余座位');
      }
    }

    // 检查座位是否可用
    if (room.playerSlots[slotIndex].status === 'occupied') {
      throw new Error('座位已被占用');
    }

    // 添加玩家到房间
    const playerData = {
      ...player,
      isHost: player.id === room.hostId,
      isReady: player.id === room.hostId, // 房主默认准备
      joinedAt: new Date(),
      slotIndex
    };

    room.players.set(player.id, playerData);
    room.playerSlots[slotIndex] = {
      id: slotIndex,
      status: 'occupied',
      player: playerData
    };

    console.log('✅ 玩家加入房间:', player.nickname, '座位:', slotIndex);
    return room;
  }

  /**
   * 移动座位
   */
  movePlayerSlot(roomId, playerId, newSlotIndex) {
    const room = this.rooms.get(roomId);
    if (!room) {
      throw new Error('房间不存在');
    }

    const player = room.players.get(playerId);
    if (!player) {
      throw new Error('玩家不在房间中');
    }

    // 检查新座位是否可用
    if (room.playerSlots[newSlotIndex].status === 'occupied') {
      throw new Error('目标座位已被占用');
    }

    // 清空原座位
    const oldSlotIndex = player.slotIndex;
    room.playerSlots[oldSlotIndex] = {
      id: oldSlotIndex,
      status: 'empty',
      player: null
    };

    // 占据新座位
    player.slotIndex = newSlotIndex;
    room.playerSlots[newSlotIndex] = {
      id: newSlotIndex,
      status: 'occupied',
      player: player
    };

    console.log('✅ 玩家移动座位:', player.nickname, oldSlotIndex, '->', newSlotIndex);
    return room;
  }

  /**
   * 获取房间信息
   */
  getRoomInfo(roomId) {
    const room = this.rooms.get(roomId);
    if (!room) {
      throw new Error('房间不存在');
    }
    return room;
  }

  /**
   * 检查是否可以开始游戏
   */
  canStartGame(roomId) {
    const room = this.rooms.get(roomId);
    if (!room) return false;

    const occupiedSlots = room.playerSlots.filter(slot => slot.status === 'occupied');
    const readyPlayers = occupiedSlots.filter(slot => slot.player.isReady);
    
    return occupiedSlots.length >= 3 && readyPlayers.length === occupiedSlots.length;
  }

  /**
   * 开始游戏
   */
  startGame(roomId) {
    const room = this.rooms.get(roomId);
    if (!room) {
      throw new Error('房间不存在');
    }

    if (!this.canStartGame(roomId)) {
      throw new Error('无法开始游戏：玩家数量不足或未全部准备');
    }

    room.status = 'playing';
    room.gameStarted = true;
    room.gameStartedAt = new Date();

    console.log('✅ 游戏开始:', roomId);
    return room;
  }

  /**
   * 玩家准备/取消准备
   */
  togglePlayerReady(roomId, playerId) {
    const room = this.rooms.get(roomId);
    if (!room) {
      throw new Error('房间不存在');
    }

    const player = room.players.get(playerId);
    if (!player) {
      throw new Error('玩家不在房间中');
    }

    // 房主不需要准备
    if (player.isHost) {
      return room;
    }

    player.isReady = !player.isReady;
    
    // 更新座位中的玩家信息
    const slot = room.playerSlots[player.slotIndex];
    if (slot && slot.player) {
      slot.player.isReady = player.isReady;
    }

    console.log('✅ 玩家准备状态:', player.nickname, player.isReady);
    return room;
  }

  /**
   * 获取当前用户
   */
  getCurrentUser() {
    return this.currentUser;
  }

  /**
   * 离开房间
   */
  leaveRoom(roomId, playerId) {
    const room = this.rooms.get(roomId);
    if (!room) return;

    const player = room.players.get(playerId);
    if (!player) return;

    // 清空座位
    room.playerSlots[player.slotIndex] = {
      id: player.slotIndex,
      status: 'empty',
      player: null
    };

    // 移除玩家
    room.players.delete(playerId);

    // 如果房主离开，删除房间
    if (player.isHost) {
      this.rooms.delete(roomId);
      console.log('✅ 房主离开，房间已删除:', roomId);
    } else {
      console.log('✅ 玩家离开房间:', player.nickname);
    }

    return room;
  }
}

// 创建全局实例
const roomManager = new RoomManager();

module.exports = roomManager;
