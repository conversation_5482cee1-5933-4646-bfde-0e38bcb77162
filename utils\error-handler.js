// 统一错误处理机制
class ErrorHandler {
  constructor() {
    this.errorCodes = {
      // 网络错误
      NETWORK_ERROR: { code: 'NETWORK_ERROR', message: '网络连接失败，请检查网络设置' },
      TIMEOUT_ERROR: { code: 'TIMEOUT_ERROR', message: '请求超时，请重试' },
      
      // 认证错误
      AUTH_FAILED: { code: 'AUTH_FAILED', message: '身份验证失败，请重新登录' },
      TOKEN_EXPIRED: { code: 'TOKEN_EXPIRED', message: '登录已过期，请重新登录' },
      PERMISSION_DENIED: { code: 'PERMISSION_DENIED', message: '权限不足' },
      
      // 房间错误
      ROOM_NOT_FOUND: { code: 'ROOM_NOT_FOUND', message: '房间不存在或已解散' },
      ROOM_FULL: { code: 'ROOM_FULL', message: '房间已满，无法加入' },
      ROOM_PASSWORD_ERROR: { code: 'ROOM_PASSWORD_ERROR', message: '房间密码错误' },
      ROOM_ALREADY_STARTED: { code: 'ROOM_ALREADY_STARTED', message: '游戏已开始，无法加入' },
      
      // 游戏错误
      GAME_NOT_STARTED: { code: 'GAME_NOT_STARTED', message: '游戏尚未开始' },
      GAME_ALREADY_ENDED: { code: 'GAME_ALREADY_ENDED', message: '游戏已结束' },
      INVALID_OPERATION: { code: 'INVALID_OPERATION', message: '当前阶段无法执行此操作' },
      VOTE_ALREADY_SUBMITTED: { code: 'VOTE_ALREADY_SUBMITTED', message: '您已经投过票了' },
      
      // 数据错误
      INVALID_PARAMS: { code: 'INVALID_PARAMS', message: '参数格式错误' },
      DATA_NOT_FOUND: { code: 'DATA_NOT_FOUND', message: '数据不存在' },
      
      // 服务器错误
      SERVER_ERROR: { code: 'SERVER_ERROR', message: '服务器内部错误，请稍后重试' },
      SERVICE_UNAVAILABLE: { code: 'SERVICE_UNAVAILABLE', message: '服务暂时不可用' },
      
      // 通用错误
      UNKNOWN_ERROR: { code: 'UNKNOWN_ERROR', message: '未知错误，请重试' }
    };
  }

  // 处理API错误
  handleApiError(error, context = '') {
    console.error(`API Error in ${context}:`, error);
    
    let errorInfo = this.errorCodes.UNKNOWN_ERROR;
    
    if (error.code && this.errorCodes[error.code]) {
      errorInfo = this.errorCodes[error.code];
    } else if (error.message) {
      // 根据错误消息匹配错误类型
      if (error.message.includes('网络')) {
        errorInfo = this.errorCodes.NETWORK_ERROR;
      } else if (error.message.includes('超时')) {
        errorInfo = this.errorCodes.TIMEOUT_ERROR;
      } else if (error.message.includes('认证') || error.message.includes('登录')) {
        errorInfo = this.errorCodes.AUTH_FAILED;
      } else if (error.message.includes('房间')) {
        errorInfo = this.errorCodes.ROOM_NOT_FOUND;
      } else {
        errorInfo = { ...this.errorCodes.UNKNOWN_ERROR, message: error.message };
      }
    }
    
    return errorInfo;
  }

  // 显示错误提示
  showError(error, context = '', page = null) {
    const errorInfo = this.handleApiError(error, context);
    
    if (page && page.selectComponent) {
      // 使用页面的Toast组件
      const toast = page.selectComponent('#t-toast');
      if (toast) {
        toast.showToast({
          theme: 'error',
          message: errorInfo.message,
          duration: 3000
        });
        return;
      }
    }
    
    // 降级到系统提示
    wx.showToast({
      title: errorInfo.message,
      icon: 'none',
      duration: 3000
    });
  }

  // 显示加载错误对话框
  showErrorDialog(error, context = '', page = null, options = {}) {
    const errorInfo = this.handleApiError(error, context);
    
    const dialogOptions = {
      title: '操作失败',
      content: errorInfo.message,
      showCancel: false,
      confirmText: '确定',
      ...options
    };
    
    if (page && page.selectComponent) {
      const dialog = page.selectComponent('#t-dialog');
      if (dialog) {
        dialog.showDialog(dialogOptions);
        return;
      }
    }
    
    // 降级到系统对话框
    wx.showModal(dialogOptions);
  }

  // 处理网络请求错误
  handleRequestError(error, retryCallback = null) {
    if (error.message && error.message.includes('网络')) {
      return {
        type: 'network',
        message: '网络连接失败',
        canRetry: true,
        retryCallback
      };
    }
    
    if (error.message && error.message.includes('超时')) {
      return {
        type: 'timeout',
        message: '请求超时',
        canRetry: true,
        retryCallback
      };
    }
    
    return {
      type: 'unknown',
      message: error.message || '请求失败',
      canRetry: false,
      retryCallback: null
    };
  }

  // 安全获取系统信息
  getSystemInfoSafe() {
    try {
      // 优先使用新API
      if (wx.getWindowInfo && wx.getDeviceInfo && wx.getAppBaseInfo) {
        return {
          ...wx.getWindowInfo(),
          ...wx.getDeviceInfo(),
          ...wx.getAppBaseInfo()
        };
      }
      // 回退到同步API
      return wx.getSystemInfoSync();
    } catch (error) {
      console.warn('获取系统信息失败:', error);
      return {
        platform: 'unknown',
        version: 'unknown'
      };
    }
  }

  // 记录错误日志
  logError(error, context = '', additionalInfo = {}) {
    const errorLog = {
      timestamp: new Date().toISOString(),
      context,
      error: {
        message: error.message,
        stack: error.stack,
        code: error.code
      },
      additionalInfo,
      userAgent: this.getSystemInfoSafe(),
      appVersion: wx.getAccountInfoSync().miniProgram.version
    };
    
    console.error('Error Log:', errorLog);
    
    // 这里可以添加错误上报逻辑
    // this.reportError(errorLog);
  }

  // 上报错误到服务器（可选）
  reportError(errorLog) {
    // 实现错误上报逻辑
    wx.request({
      url: 'https://api.example.com/error-report',
      method: 'POST',
      data: errorLog,
      fail: (err) => {
        console.warn('Error reporting failed:', err);
      }
    });
  }
}

// 创建全局错误处理器实例
const errorHandler = new ErrorHandler();

// 全局错误监听
wx.onError((error) => {
  errorHandler.logError(error, 'Global Error');
});

// 全局未处理的Promise拒绝监听
wx.onUnhandledRejection((res) => {
  errorHandler.logError(res.reason, 'Unhandled Promise Rejection');
});

module.exports = errorHandler;
