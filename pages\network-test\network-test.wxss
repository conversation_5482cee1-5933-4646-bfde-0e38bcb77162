/* pages/network-test/network-test.wxss */

.container {
  padding: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* 页面标题 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 40rpx 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 状态卡片 */
.status-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.status-header {
  margin-bottom: 20rpx;
}

.status-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.status-content {
  text-align: center;
}

.status-value {
  font-size: 36rpx;
  font-weight: bold;
  padding: 20rpx;
  border-radius: 10rpx;
  display: inline-block;
}

.status-success {
  color: #4CAF50;
  background: rgba(76, 175, 80, 0.1);
}

.status-error {
  color: #f44336;
  background: rgba(244, 67, 54, 0.1);
}

/* 操作按钮 */
.action-buttons {
  margin-bottom: 40rpx;
}

.action-btn {
  width: 100%;
  margin-bottom: 20rpx;
  padding: 25rpx;
  border-radius: 15rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
}

.action-btn.secondary {
  background: linear-gradient(45deg, #2196F3, #1976D2);
  color: white;
}

.action-btn.tertiary {
  background: linear-gradient(45deg, #FF9800, #F57C00);
  color: white;
}

.action-btn:disabled {
  opacity: 0.6;
  transform: none;
}

.action-btn:not(:disabled):active {
  transform: scale(0.98);
}

/* 结果部分 */
.results-section, .diagnostic-section, .help-section {
  margin-bottom: 40rpx;
}

.section-header {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 测试结果 */
.test-results {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.test-item {
  padding: 20rpx;
  margin-bottom: 15rpx;
  border-radius: 10rpx;
  border-left: 8rpx solid;
}

.test-success {
  background: rgba(76, 175, 80, 0.1);
  border-left-color: #4CAF50;
}

.test-error {
  background: rgba(244, 67, 54, 0.1);
  border-left-color: #f44336;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.test-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.test-time {
  font-size: 24rpx;
  color: #666;
}

.test-message {
  font-size: 26rpx;
  color: #555;
  line-height: 1.4;
}

/* 诊断卡片 */
.diagnostic-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  margin-bottom: 20rpx;
  border-bottom: 2rpx solid #eee;
  padding-bottom: 15rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.card-content {
  line-height: 1.6;
}

.info-text {
  display: block;
  font-size: 28rpx;
  color: #555;
  margin-bottom: 10rpx;
}

/* 域名项目 */
.domain-item {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
}

.domain-status {
  margin-right: 15rpx;
  font-size: 24rpx;
}

.domain-name {
  flex: 1;
  font-size: 26rpx;
  color: #333;
}

.domain-time {
  font-size: 24rpx;
  color: #666;
}

/* 建议项目 */
.recommendation-item {
  display: flex;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
}

.rec-icon {
  margin-right: 15rpx;
  font-size: 32rpx;
}

.rec-content {
  flex: 1;
}

.rec-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.rec-description {
  display: block;
  font-size: 26rpx;
  color: #555;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.rec-action {
  display: block;
  font-size: 26rpx;
  color: #2196F3;
  font-weight: bold;
}

/* 诊断操作 */
.diagnostic-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}

.diagnostic-actions .action-btn {
  flex: 1;
  margin-bottom: 0;
}

/* 帮助部分 */
.help-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.help-item {
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #eee;
}

.help-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.help-question {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.help-answer {
  display: block;
  font-size: 26rpx;
  color: #555;
  line-height: 1.5;
}
