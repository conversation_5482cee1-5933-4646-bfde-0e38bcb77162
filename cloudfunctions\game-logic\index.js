// cloudfunctions/game-logic/index.js
// 游戏逻辑云函数

const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV // 使用当前环境
});

const db = cloud.database();

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  const { action, data } = event;
  
  console.log('游戏逻辑云函数被调用:', { action, data });
  
  try {
    switch (action) {
      case 'createRoom':
        return await createRoom(data);
      case 'joinRoom':
        return await joinRoom(data);
      case 'startGame':
        return await startGame(data);
      case 'submitAnswer':
        return await submitAnswer(data);
      case 'getRoomInfo':
        return await getRoomInfo(data);
      default:
        throw new Error(`未知的操作: ${action}`);
    }
  } catch (error) {
    console.error('游戏逻辑处理失败:', error);
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * 创建游戏房间
 */
async function createRoom(data) {
  const { hostId, hostName, gameType = 'reasoning' } = data;
  
  const roomCode = generateRoomCode();
  const room = {
    roomCode,
    hostId,
    hostName,
    gameType,
    status: 'waiting', // waiting, playing, finished
    players: [{
      id: hostId,
      name: hostName,
      isHost: true,
      joinTime: new Date()
    }],
    currentRound: 0,
    maxRounds: 5,
    createdAt: new Date(),
    updatedAt: new Date()
  };
  
  const result = await db.collection('game_rooms').add({
    data: room
  });
  
  return {
    success: true,
    data: {
      roomId: result._id,
      roomCode,
      room
    }
  };
}

/**
 * 加入游戏房间
 */
async function joinRoom(data) {
  const { roomCode, playerId, playerName } = data;
  
  // 查找房间
  const roomQuery = await db.collection('game_rooms')
    .where({ roomCode })
    .get();
    
  if (roomQuery.data.length === 0) {
    throw new Error('房间不存在');
  }
  
  const room = roomQuery.data[0];
  
  if (room.status !== 'waiting') {
    throw new Error('房间已开始游戏，无法加入');
  }
  
  // 检查是否已在房间中
  const existingPlayer = room.players.find(p => p.id === playerId);
  if (existingPlayer) {
    return {
      success: true,
      data: { room, message: '已在房间中' }
    };
  }
  
  // 添加玩家
  const newPlayer = {
    id: playerId,
    name: playerName,
    isHost: false,
    joinTime: new Date()
  };
  
  await db.collection('game_rooms').doc(room._id).update({
    data: {
      players: db.command.push(newPlayer),
      updatedAt: new Date()
    }
  });
  
  return {
    success: true,
    data: {
      room: {
        ...room,
        players: [...room.players, newPlayer]
      }
    }
  };
}

/**
 * 开始游戏
 */
async function startGame(data) {
  const { roomId, hostId } = data;
  
  const room = await db.collection('game_rooms').doc(roomId).get();
  
  if (!room.data) {
    throw new Error('房间不存在');
  }
  
  if (room.data.hostId !== hostId) {
    throw new Error('只有房主可以开始游戏');
  }
  
  if (room.data.players.length < 2) {
    throw new Error('至少需要2名玩家才能开始游戏');
  }
  
  await db.collection('game_rooms').doc(roomId).update({
    data: {
      status: 'playing',
      currentRound: 1,
      startTime: new Date(),
      updatedAt: new Date()
    }
  });
  
  return {
    success: true,
    data: { message: '游戏已开始' }
  };
}

/**
 * 提交答案
 */
async function submitAnswer(data) {
  const { roomId, playerId, answer, round } = data;
  
  // 这里可以添加答案验证逻辑
  const submission = {
    roomId,
    playerId,
    answer,
    round,
    submittedAt: new Date()
  };
  
  await db.collection('game_answers').add({
    data: submission
  });
  
  return {
    success: true,
    data: { message: '答案已提交' }
  };
}

/**
 * 获取房间信息
 */
async function getRoomInfo(data) {
  const { roomCode } = data;
  
  const roomQuery = await db.collection('game_rooms')
    .where({ roomCode })
    .get();
    
  if (roomQuery.data.length === 0) {
    throw new Error('房间不存在');
  }
  
  return {
    success: true,
    data: { room: roomQuery.data[0] }
  };
}

/**
 * 生成房间码
 */
function generateRoomCode() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
