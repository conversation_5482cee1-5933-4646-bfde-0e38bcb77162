/* 简化版地球星空背景首页 - 确保小程序兼容性 */
.home-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #000428 0%, #004e92 100%);
  padding: 0;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 地球元素 */
.home-container::before {
  content: '';
  position: absolute;
  bottom: -100rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 400rpx;
  height: 400rpx;
  background: radial-gradient(circle, rgba(135, 206, 235, 0.7) 0%, rgba(65, 105, 225, 0.8) 70%, rgba(25, 25, 112, 0.9) 100%);
  border-radius: 50%;
  box-shadow: 0 0 100rpx rgba(135, 206, 235, 0.3);
  z-index: 1;
}

/* 星星装饰 */
.stars {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.star {
  position: absolute;
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
  animation: twinkle 3s ease-in-out infinite;
}

.star-1 {
  top: 15%;
  left: 20%;
  animation-delay: 0s;
}

.star-2 {
  top: 25%;
  right: 25%;
  animation-delay: 1s;
}

.star-3 {
  top: 40%;
  left: 15%;
  animation-delay: 2s;
}

.star-4 {
  top: 60%;
  right: 20%;
  animation-delay: 0.5s;
}

.star-5 {
  top: 80%;
  left: 30%;
  animation-delay: 1.5s;
}

/* 背景遮罩层 - 确保文字可读性 */
.home-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 1;
}

/* 极简主标题区域 */
.hero-section {
  text-align: center;
  padding: 200rpx 40rpx 120rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 2;
}

/* 应用Logo */
.app-logo {
  margin-bottom: 32rpx;
}

.logo-text {
  font-size: 64rpx;
  font-weight: 300;
  color: #ffffff;
  letter-spacing: 2rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'PingFang SC', sans-serif;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
}

/* 应用副标题 */
.app-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
  letter-spacing: 1rpx;
  margin-bottom: 80rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 极简主操作按钮 */
.main-actions {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  padding: 0 40rpx;
  margin-bottom: 80rpx;
  position: relative;
  z-index: 2;
}

.minimal-btn {
  width: 100%;
  height: 96rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
}

.primary-btn {
  background: #000000;
  color: #ffffff;
}

.primary-btn:active {
  background: #333333;
  transform: scale(0.98);
}

.secondary-btn {
  background: rgba(255, 255, 255, 0.9);
  color: #000000;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.secondary-btn:active {
  background: rgba(255, 255, 255, 0.8);
  transform: scale(0.98);
}

/* 快速加入区域 */
.quick-join-section {
  display: flex;
  gap: 16rpx;
  padding: 0 40rpx;
  margin-bottom: 60rpx;
  position: relative;
  z-index: 2;
}

.minimal-input {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  background: rgba(255, 255, 255, 0.9);
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.minimal-input:focus {
  border-color: #000000;
}

.join-btn {
  width: 120rpx;
  height: 80rpx;
  background: #000000;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
}

.join-btn:active {
  background: #333333;
  transform: scale(0.98);
}

.join-btn.disabled {
  background: #cccccc;
  color: #999999;
}

/* 房间列表 */
.rooms-section {
  padding: 0 40rpx;
  margin-bottom: 60rpx;
  position: relative;
  z-index: 2;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 32rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'PingFang SC', sans-serif;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
}

.rooms-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.room-card {
  background: rgba(255, 255, 255, 0.95);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s ease;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

.room-card:active {
  background: rgba(255, 255, 255, 0.9);
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.3);
}

.room-info {
  flex: 1;
}

.room-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #000000;
  margin-bottom: 8rpx;
  display: block;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
}

.room-players {
  font-size: 24rpx;
  color: #666666;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
}

.room-status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-weight: 500;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
}

.room-status.waiting {
  background: #e8f5e8;
  color: #2e7d32;
}

.room-status.playing {
  background: #fff3e0;
  color: #f57c00;
}

/* 开发测试区域 */
.dev-test-section {
  padding: 0 40rpx;
  margin-bottom: 60rpx;
  position: relative;
  z-index: 2;
}

.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.test-btn {
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
  transition: all 0.2s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.test-btn:active {
  background: rgba(255, 255, 255, 0.8);
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.test-btn.danger {
  background: #ffebee;
  color: #d32f2f;
  border-color: #ffcdd2;
}

.test-btn.danger:active {
  background: #ffcdd2;
}

/* 开发测试切换按钮 */
.dev-toggle-btn {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  width: 120rpx;
  height: 80rpx;
  background: #000000;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
  z-index: 1000;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
}

.dev-toggle-btn:active {
  background: #333333;
  transform: scale(0.95);
}

/* 加载状态 */
.loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 32rpx 48rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  z-index: 9999;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
}

/* 闪烁动画 */
@keyframes twinkle {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.7; }
}
