/* 历史记录页面样式 */
.history-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f0f5ff 0%, #e6f0ff 100%);
}

/* 标签页区域 */
.tabs-section {
  padding: 32rpx 0 0;
}

.history-tabs {
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
  margin: 0 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

/* 通用区域样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.total-count {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
}

/* 游戏历史列表 */
.games-list {
  background: white;
}

.game-cards {
  padding: 0 32rpx 32rpx;
}

.game-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  border-left: 8rpx solid #ddd;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.game-card.win {
  border-left-color: #52c41a;
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.05) 0%, rgba(255, 255, 255, 0.9) 100%);
}

.game-card.lose {
  border-left-color: #ff6b6b;
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.05) 0%, rgba(255, 255, 255, 0.9) 100%);
}

.game-card.draw {
  border-left-color: #ffc107;
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.05) 0%, rgba(255, 255, 255, 0.9) 100%);
}

.game-card:active {
  transform: scale(0.98);
}

.game-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.game-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.game-time {
  font-size: 24rpx;
  color: #666;
}

.game-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.role-info {
  font-size: 26rpx;
  color: #666;
}

.result-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.influence-change {
  font-size: 24rpx;
  font-weight: 500;
}

.game-card.win .influence-change {
  color: #52c41a;
}

.game-card.lose .influence-change {
  color: #ff6b6b;
}

.game-card.draw .influence-change {
  color: #ffc107;
}

.game-tags {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

/* 成就系统 */
.achievements-section {
  background: white;
}

.achievements-grid {
  padding: 0 32rpx 32rpx;
  display: grid;
  grid-template-columns: 1fr;
  gap: 24rpx;
}

.achievement-card {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 24rpx;
  border: 2rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.achievement-card.unlocked {
  border-color: #52c41a;
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.05) 0%, rgba(255, 255, 255, 0.9) 100%);
}

.achievement-card.locked {
  opacity: 0.6;
}

.achievement-card:active {
  transform: scale(0.98);
}

.achievement-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  background: rgba(255, 215, 0, 0.1);
  border-radius: 50%;
}

.achievement-emoji {
  font-size: 48rpx;
}

.achievement-info {
  flex: 1;
}

.achievement-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.achievement-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.achievement-progress {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.progress-text {
  font-size: 22rpx;
  color: #666;
  white-space: nowrap;
}

.achievement-unlocked {
  display: flex;
  align-items: center;
  gap: 8rpx;
  color: #52c41a;
  font-size: 24rpx;
  font-weight: 500;
}

/* 好友互动 */
.friends-section {
  background: white;
}

.interaction-list {
  padding: 0 32rpx 32rpx;
}

.interaction-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  margin-bottom: 16rpx;
  border: 2rpx solid rgba(114, 46, 209, 0.1);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.friend-avatar {
  margin-right: 24rpx;
}

.interaction-content {
  flex: 1;
}

.interaction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.friend-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.interaction-time {
  font-size: 22rpx;
  color: #666;
}

.interaction-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.interaction-type {
  margin-left: 16rpx;
}

/* 游戏详情弹窗 */
.game-detail-popup {
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  overflow: hidden;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.game-detail-content {
  padding: 32rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

/* TDesign组件样式覆盖 */
.t-tabs__header {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
}

.t-tab-panel {
  min-height: 60vh;
}

.t-progress {
  flex: 1;
}

.t-avatar {
  border: 2rpx solid rgba(114, 46, 209, 0.2);
}
