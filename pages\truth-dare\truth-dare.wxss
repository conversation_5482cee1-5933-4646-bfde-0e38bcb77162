/* 真心话时间页面样式 - 艺术化设计 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #2c1810 100%);
  position: relative;
  overflow: hidden;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

.container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 30%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(0, 191, 255, 0.08) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(40rpx);
  border-bottom: 2rpx solid rgba(255, 255, 255, 0.2);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 40rpx;
  padding-top: calc(20rpx + env(safe-area-inset-top));
  height: 88rpx;
}

.nav-left, .nav-right {
  width: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-title {
  color: white;
  font-size: 36rpx;
  font-weight: 600;
  text-shadow: 0 0 20rpx rgba(255, 255, 255, 0.5);
}

.timer {
  color: #ffd700;
  font-size: 28rpx;
  font-weight: 600;
  background: rgba(255, 215, 0, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 2rpx solid rgba(255, 215, 0, 0.3);
}

/* 页面内容 */
.page-content {
  padding: 240rpx 40rpx 240rpx;
  position: relative;
  z-index: 1;
}

/* 艺术化卡片 */
.artistic-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(50rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 48rpx;
  padding: 48rpx;
  margin-bottom: 40rpx;
  box-shadow:
    0 24rpx 80rpx rgba(0, 0, 0, 0.15),
    0 8rpx 32rpx rgba(0, 0, 0, 0.1),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 章节标题 */
.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.section-icon {
  font-size: 40rpx;
  margin-right: 16rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  text-shadow: 0 0 20rpx rgba(255, 255, 255, 0.6);
}

/* 介绍内容 */
.intro-content {
  padding: 16rpx 0;
}

.intro-text {
  font-size: 28rpx;
  line-height: 1.8;
  color: rgba(255, 255, 255, 0.9);
  text-align: justify;
}

/* 问题内容 */
.question-content {
  padding: 16rpx 0;
}

.current-player {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 24rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.player-avatar {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-right: 24rpx;
}

.player-info {
  flex: 1;
}

.player-name {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 8rpx;
}

.player-role {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

.question-text {
  font-size: 32rpx;
  line-height: 1.6;
  color: white;
  background: rgba(255, 255, 255, 0.1);
  padding: 32rpx;
  border-radius: 24rpx;
  border-left: 8rpx solid #ffd700;
}

/* 回答区域 */
.answer-input {
  padding: 16rpx 0;
  position: relative;
}

.answer-field {
  width: 100%;
  min-height: 200rpx;
  padding: 32rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  font-size: 32rpx;
  color: #333;
  line-height: 1.6;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.answer-counter {
  position: absolute;
  bottom: 24rpx;
  right: 24rpx;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}

/* 回答历史 */
.answer-history {
  padding: 16rpx 0;
}

.history-item {
  margin-bottom: 32rpx;
  padding: 32rpx;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 24rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.15);
}

.history-player {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.history-avatar {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 16rpx;
}

.history-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #ffd700;
}

.history-qa {
  padding-left: 76rpx;
}

.history-question {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8rpx;
  line-height: 1.5;
}

.history-answer {
  font-size: 28rpx;
  color: white;
  line-height: 1.6;
  background: rgba(255, 255, 255, 0.1);
  padding: 16rpx;
  border-radius: 16rpx;
}

.no-history {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  font-size: 28rpx;
  padding: 40rpx 0;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 40rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(40rpx);
  border-top: 2rpx solid rgba(255, 255, 255, 0.2);
  display: flex;
  gap: 24rpx;
  z-index: 1000;
}

.action-btn {
  flex: 1;
  padding: 32rpx;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(20rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.action-btn.primary {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #333;
  box-shadow: 0 12rpx 40rpx rgba(255, 215, 0, 0.4);
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.action-btn:disabled {
  background: rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.5) !important;
  cursor: not-allowed;
}

.action-btn:hover:not(:disabled) {
  transform: translateY(-4rpx);
  box-shadow: 0 16rpx 50rpx rgba(0, 0, 0, 0.2);
}

.btn-icon {
  font-size: 28rpx;
}
