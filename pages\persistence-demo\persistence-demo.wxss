/* pages/persistence-demo/persistence-demo.wxss */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  padding: 20rpx;
  color: #ffffff;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #ffc107;
  margin-bottom: 10rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: #b0b0b0;
  opacity: 0.8;
}

/* 状态卡片 */
.status-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.status-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffc107;
}

.influence-badge {
  background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  text-align: center;
}

.influence-text {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #000;
}

.influence-level {
  display: block;
  font-size: 22rpx;
  color: #333;
}

.status-content {
  space-y: 15rpx;
}

.status-item {
  display: flex;
  margin-bottom: 15rpx;
}

.status-label {
  font-size: 28rpx;
  color: #b0b0b0;
  min-width: 200rpx;
}

.status-value {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 500;
}

.vote-result {
  color: #4caf50;
  font-weight: bold;
}

.importance-high {
  color: #f44336;
  font-weight: bold;
}

.importance-medium {
  color: #ff9800;
  font-weight: bold;
}

.importance-low {
  color: #4caf50;
  font-weight: bold;
}

/* 影响力详情 */
.influence-detail {
  background: rgba(255, 193, 7, 0.1);
  border: 2rpx solid rgba(255, 193, 7, 0.3);
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.influence-detail.show {
  max-height: 200rpx;
}

.detail-header {
  margin-bottom: 10rpx;
}

.detail-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #ffc107;
}

.detail-text {
  display: block;
  font-size: 26rpx;
  color: #ffffff;
  margin-bottom: 5rpx;
}

.detail-desc {
  display: block;
  font-size: 24rpx;
  color: #b0b0b0;
}

/* 坚持机制面板 */
.persistence-panel {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.2) 0%, rgba(255, 152, 0, 0.15) 100%);
  backdrop-filter: blur(20rpx);
  border: 2rpx solid rgba(255, 193, 7, 0.4);
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.panel-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffc107;
}

.countdown-timer {
  background: rgba(244, 67, 54, 0.8);
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
}

.countdown-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #ffffff;
}

/* 选择选项 */
.options-list {
  space-y: 20rpx;
}

.persistence-choice {
  background: rgba(255, 255, 255, 0.9);
  border: 2rpx solid rgba(255, 193, 7, 0.6);
  border-radius: 16rpx;
  padding: 25rpx;
  margin-bottom: 20rpx;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.persistence-choice:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 25rpx rgba(255, 193, 7, 0.3);
}

.persistence-choice.selected {
  border-color: #ffc107;
  background: rgba(255, 193, 7, 0.1);
  box-shadow: 0 0 20rpx rgba(255, 193, 7, 0.5);
}

.persistence-choice.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.choice-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.choice-icon {
  font-size: 40rpx;
  margin-right: 15rpx;
}

.choice-title-area {
  flex: 1;
}

.choice-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.choice-cost {
  margin-top: 5rpx;
}

.cost-text {
  font-size: 24rpx;
  color: #666;
}

.cost-text.free {
  color: #4caf50;
  font-weight: bold;
}

.choice-risk {
  padding: 5rpx 15rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: bold;
}

.risk-none {
  background: #e8f5e8;
  color: #4caf50;
}

.risk-low {
  background: #fff3e0;
  color: #ff9800;
}

.risk-medium {
  background: #fce4ec;
  color: #e91e63;
}

.risk-high {
  background: #ffebee;
  color: #f44336;
}

.choice-description {
  margin-bottom: 15rpx;
}

.description-text {
  font-size: 28rpx;
  color: #555;
  line-height: 1.5;
}

.choice-effects {
  margin-bottom: 15rpx;
}

.effects-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.effects-list {
  padding-left: 20rpx;
}

.effect-item {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.choice-recommendation {
  margin-bottom: 10rpx;
}

.recommendation-text {
  font-size: 24rpx;
  color: #888;
  font-style: italic;
}

.choice-requirements {
  border-top: 1rpx solid #eee;
  padding-top: 10rpx;
}

.requirements-title {
  font-size: 24rpx;
  font-weight: bold;
  color: #f44336;
  display: block;
  margin-bottom: 5rpx;
}

.requirements-text {
  display: block;
  font-size: 22rpx;
  color: #f44336;
  margin-bottom: 3rpx;
}

/* 操作按钮 */
.action-buttons {
  margin-bottom: 30rpx;
}

.button-row {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.demo-button {
  flex: 1;
  padding: 25rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
}

.demo-button.primary {
  background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
  color: #000;
}

.demo-button.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.demo-button:disabled {
  opacity: 0.5;
}

.confirm-button {
  width: 100%;
  margin-top: 10rpx;
}

/* 演示配置 */
.demo-configs {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16rpx;
  padding: 25rpx;
  margin-bottom: 30rpx;
}

.config-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #ffc107;
  display: block;
  margin-bottom: 15rpx;
}

.config-buttons {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
}

.config-button {
  padding: 15rpx 25rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.config-button.active {
  background: #ffc107;
  color: #000;
  border-color: #ffc107;
}

/* 帮助说明 */
.help-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16rpx;
  padding: 25rpx;
}

.help-header {
  margin-bottom: 15rpx;
}

.help-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #ffc107;
}

.help-content {
  space-y: 10rpx;
}

.help-text {
  display: block;
  font-size: 26rpx;
  color: #b0b0b0;
  margin-bottom: 15rpx;
  line-height: 1.5;
}

.help-item {
  display: block;
  font-size: 24rpx;
  color: #d0d0d0;
  margin-bottom: 8rpx;
  padding-left: 20rpx;
}
