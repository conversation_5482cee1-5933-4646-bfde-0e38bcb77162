/* 个人中心页面样式 */
.profile-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f6f0ff 0%, #f0e6ff 100%);
}

/* 用户信息头部 */
.profile-header {
  background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
  padding: 60rpx 32rpx 40rpx;
  position: relative;
  overflow: hidden;
}

.profile-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.2) 0%, transparent 60%);
  pointer-events: none;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  position: relative;
  z-index: 1;
}

.user-avatar {
  margin-right: 24rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.user-name {
  color: white;
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.user-desc {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
}

.user-stats {
  display: flex;
  justify-content: space-around;
  position: relative;
  z-index: 1;
}

.stat-item {
  text-align: center;
  color: white;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.stat-item:active {
  transform: scale(0.95);
}

.stat-value {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 数据统计区域 */
.stats-section {
  padding: 32rpx;
}

.stats-card {
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32rpx;
  margin-bottom: 32rpx;
}

.stats-item {
  text-align: center;
  padding: 24rpx;
  border-radius: 16rpx;
  position: relative;
}

.stats-item.win {
  background: rgba(82, 196, 26, 0.1);
  border: 2rpx solid rgba(82, 196, 26, 0.2);
}

.stats-item.lose {
  background: rgba(255, 107, 107, 0.1);
  border: 2rpx solid rgba(255, 107, 107, 0.2);
}

.stats-item.rating {
  background: rgba(24, 144, 255, 0.1);
  border: 2rpx solid rgba(24, 144, 255, 0.2);
}

.stats-item.mvp {
  background: rgba(255, 193, 7, 0.1);
  border: 2rpx solid rgba(255, 193, 7, 0.2);
}

.stats-number {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stats-item.win .stats-number {
  color: #52c41a;
}

.stats-item.lose .stats-number {
  color: #ff6b6b;
}

.stats-item.rating .stats-number {
  color: #1890ff;
}

.stats-item.mvp .stats-number {
  color: #ffc107;
}

.stats-text {
  font-size: 24rpx;
  color: #666;
}

.role-types {
  padding-top: 32rpx;
  border-top: 2rpx solid #f0f0f0;
}

.role-type-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.role-tags {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

/* 功能菜单区域 */
.menu-section {
  padding: 0 32rpx 32rpx;
}

.t-cell-group {
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.t-cell {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
}

/* 退出登录区域 */
.logout-section {
  padding: 0 32rpx 32rpx;
}

.logout-btn {
  border-radius: 24rpx;
}

/* 版本信息 */
.version-info {
  text-align: center;
  padding: 32rpx;
  color: #999;
  font-size: 24rpx;
}

/* 对话框内容 */
.dialog-content {
  padding: 32rpx 0;
}

/* TDesign组件样式覆盖 */
.t-card__header {
  background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
  color: white;
  font-weight: 600;
}

.t-avatar {
  box-shadow: 0 4rpx 16rpx rgba(114, 46, 209, 0.3);
}

.t-tag--theme-primary {
  background: rgba(114, 46, 209, 0.1) !important;
  color: #722ed1 !important;
  border: 1rpx solid rgba(114, 46, 209, 0.3) !important;
}

.t-button--theme-danger {
  background: linear-gradient(45deg, #ff6b6b, #ee5a52) !important;
  border: none !important;
}

.t-switch--checked {
  background-color: #722ed1 !important;
}

.t-cell__left-icon {
  color: #722ed1 !important;
}

.t-cell__right-icon {
  color: #ccc !important;
}
