// pages/api-key-fix/api-key-fix.js
const ApiKeyUpdater = require('../../utils/api-key-updater');
const apiConfig = require('../../config/api-config');

Page({
  data: {
    currentKey: '',
    newKey: 'sk-rFun7AywY7jUUdJAtUBbFDxhaVTI5okdFpL3mSSeLfOmKCmP',
    updateResult: '',
    testResult: '',
    isUpdating: false,
    isTesting: false
  },

  onLoad() {
    this.loadCurrentKey();
  },

  /**
   * 加载当前密钥
   */
  loadCurrentKey() {
    const config = apiConfig.getCurrentConfig();
    this.setData({
      currentKey: apiConfig.getSafeKeyDisplay(config.apiKey)
    });
  },

  /**
   * 更新API密钥
   */
  async updateApiKey() {
    this.setData({ 
      isUpdating: true,
      updateResult: '正在更新API密钥...'
    });

    try {
      const updater = new ApiKeyUpdater();
      const result = updater.updateApiKey();
      
      this.setData({
        updateResult: result.success ? '✅ 密钥更新成功' : `❌ 更新失败: ${result.error}`,
        isUpdating: false
      });

      if (result.success) {
        this.loadCurrentKey();
        wx.showToast({
          title: '密钥更新成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: '更新失败',
          icon: 'error'
        });
      }
    } catch (error) {
      this.setData({
        updateResult: `❌ 更新异常: ${error.message}`,
        isUpdating: false
      });
    }
  },

  /**
   * 测试API连接
   */
  async testApiConnection() {
    this.setData({ 
      isTesting: true,
      testResult: '正在测试API连接...'
    });

    try {
      const updater = new ApiKeyUpdater();
      const result = await updater.testApiConnection();
      
      if (result.success) {
        this.setData({
          testResult: '✅ API连接测试成功！',
          isTesting: false
        });
        wx.showToast({
          title: '连接成功',
          icon: 'success'
        });
      } else {
        let errorMsg = `❌ 连接失败: ${result.error}`;
        if (result.suggestions) {
          errorMsg += '\n\n建议：\n' + result.suggestions.join('\n');
        }
        
        this.setData({
          testResult: errorMsg,
          isTesting: false
        });
        
        wx.showModal({
          title: '连接失败',
          content: result.error + '\n\n请检查密钥是否有效',
          showCancel: false
        });
      }
    } catch (error) {
      this.setData({
        testResult: `❌ 测试异常: ${error.message}`,
        isTesting: false
      });
    }
  },

  /**
   * 执行完整更新流程
   */
  async performFullUpdate() {
    wx.showLoading({
      title: '正在更新...'
    });

    try {
      const updater = new ApiKeyUpdater();
      const result = await updater.performFullUpdate();
      
      wx.hideLoading();
      
      if (result.overall === 'success') {
        wx.showModal({
          title: '更新成功',
          content: '✅ API密钥已成功更新并测试通过！\n\n现在可以正常使用AI功能了。',
          showCancel: false,
          success: () => {
            // 返回上一页
            wx.navigateBack();
          }
        });
      } else {
        wx.showModal({
          title: '更新失败',
          content: `❌ 更新过程中出现问题：\n\n${result.testResult.error}\n\n请检查API密钥是否有效。`,
          showCancel: false
        });
      }
      
      this.setData({
        updateResult: result.updateResult.success ? '✅ 密钥更新成功' : `❌ ${result.updateResult.error}`,
        testResult: result.testResult.success ? '✅ 连接测试成功' : `❌ ${result.testResult.error}`
      });
      
    } catch (error) {
      wx.hideLoading();
      wx.showModal({
        title: '操作失败',
        content: `发生异常：${error.message}`,
        showCancel: false
      });
    }
  },

  /**
   * 手动输入新密钥
   */
  onNewKeyInput(e) {
    this.setData({
      newKey: e.detail.value.trim()
    });
  },

  /**
   * 使用自定义密钥更新
   */
  async updateWithCustomKey() {
    const { newKey } = this.data;
    
    if (!newKey) {
      wx.showToast({
        title: '请输入密钥',
        icon: 'error'
      });
      return;
    }

    // 验证密钥格式
    const validation = apiConfig.validateApiKey(newKey);
    if (!validation.valid) {
      wx.showModal({
        title: '密钥格式错误',
        content: validation.message,
        showCancel: false
      });
      return;
    }

    wx.showLoading({
      title: '正在更新...'
    });

    try {
      // 保存自定义密钥
      const success = apiConfig.setApiKey(newKey);
      
      if (success) {
        // 测试连接
        const updater = new ApiKeyUpdater();
        const testResult = await updater.testApiConnection();
        
        wx.hideLoading();
        
        if (testResult.success) {
          wx.showModal({
            title: '更新成功',
            content: '✅ 自定义API密钥已成功设置并测试通过！',
            showCancel: false
          });
          this.loadCurrentKey();
        } else {
          wx.showModal({
            title: '密钥无效',
            content: `❌ 密钥设置成功但连接测试失败：\n\n${testResult.error}`,
            showCancel: false
          });
        }
      } else {
        wx.hideLoading();
        wx.showToast({
          title: '保存失败',
          icon: 'error'
        });
      }
    } catch (error) {
      wx.hideLoading();
      wx.showModal({
        title: '操作失败',
        content: error.message,
        showCancel: false
      });
    }
  },

  /**
   * 清除所有密钥
   */
  clearAllKeys() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有保存的API密钥吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            apiConfig.clearApiKey();
            wx.removeStorageSync('api_key');
            wx.removeStorageSync('openai_api_key');
            
            this.setData({
              currentKey: '未设置',
              updateResult: '✅ 已清除所有密钥',
              testResult: ''
            });
            
            wx.showToast({
              title: '清除成功',
              icon: 'success'
            });
          } catch (error) {
            wx.showToast({
              title: '清除失败',
              icon: 'error'
            });
          }
        }
      }
    });
  }
});
