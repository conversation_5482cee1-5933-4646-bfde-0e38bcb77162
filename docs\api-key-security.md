# API密钥安全指南

## 🚨 重要提醒

**Moonshot AI会自动检测和禁用泄露的API密钥！**

## 📋 密钥泄露的常见原因

1. **代码仓库泄露**
   - 将密钥直接写在代码中
   - 提交到Git仓库
   - 上传到GitHub等公开平台

2. **文档泄露**
   - 在文档中展示完整密钥
   - 截图包含密钥信息
   - 分享配置文件

3. **日志泄露**
   - 在控制台输出完整密钥
   - 错误日志包含密钥
   - 调试信息泄露

## ✅ 安全使用指南

### 1. 生成新密钥
- 访问 [Moonshot AI控制台](https://platform.moonshot.cn/console/api-keys)
- 删除已泄露的旧密钥
- 生成全新的API密钥
- 立即复制并安全保存

### 2. 安全存储
- ✅ 使用本应用的API设置页面
- ✅ 存储在本地加密存储中
- ✅ 使用环境变量（生产环境）
- ❌ 不要写在代码中
- ❌ 不要提交到版本控制
- ❌ 不要在截图中展示

### 3. 安全传输
- ✅ 通过HTTPS传输
- ✅ 使用Authorization头
- ❌ 不要在URL中传递
- ❌ 不要在GET参数中传递

### 4. 安全使用
- ✅ 定期轮换密钥
- ✅ 监控使用情况
- ✅ 设置使用限制
- ❌ 不要共享给他人
- ❌ 不要在多个项目中重复使用

## 🔧 本应用的安全措施

### 1. 安全配置系统
```javascript
// ✅ 正确方式 - 使用配置系统
const apiConfig = require('../config/api-config');
const config = apiConfig.getCurrentConfig();

// ❌ 错误方式 - 硬编码
const apiKey = 'sk-xxxxx'; // 永远不要这样做！
```

### 2. 本地存储加密
- 密钥存储在微信小程序的本地存储中
- 不会被同步到云端
- 只在本设备上可访问

### 3. 安全显示
- 在界面上只显示密钥的前后几位
- 日志中使用脱敏显示
- 错误信息不包含完整密钥

### 4. 自动回退机制
- 当API密钥无效时自动切换到模拟服务
- 保证应用功能正常运行
- 提供友好的错误提示

## 🚨 密钥泄露应急处理

### 1. 立即行动
1. **停止使用** - 立即停止使用泄露的密钥
2. **删除密钥** - 在控制台中删除泄露的密钥
3. **生成新密钥** - 创建全新的API密钥
4. **更新配置** - 在应用中更新为新密钥

### 2. 检查影响
1. **查看使用记录** - 检查是否有异常使用
2. **监控账单** - 确认是否有异常费用
3. **联系客服** - 如有异常及时联系Moonshot AI客服

### 3. 预防措施
1. **代码审查** - 确保代码中没有硬编码密钥
2. **文档清理** - 清理所有包含密钥的文档
3. **团队培训** - 确保团队成员了解安全规范

## 📞 获取帮助

如果遇到API密钥相关问题：

1. **查看控制台** - [https://platform.moonshot.cn/console](https://platform.moonshot.cn/console)
2. **阅读文档** - [https://platform.moonshot.cn/docs](https://platform.moonshot.cn/docs)
3. **联系客服** - 通过官方渠道联系技术支持

## 🔄 密钥轮换建议

- **开发环境**: 每月轮换一次
- **测试环境**: 每2周轮换一次  
- **生产环境**: 每周轮换一次
- **泄露后**: 立即轮换

记住：**安全第一，预防胜于治疗！**
