// 简化版AI服务 - 用于测试模块加载
const MockAIService = require('./mock-ai-service');

class AIService {
  constructor() {
    console.log('🚀 初始化简化版AI服务...');

    // 初始化模拟服务作为回退
    this.mockService = new MockAIService();

    // 尝试获取API配置
    try {
      const apiConfig = require('../config/api-config');
      const config = apiConfig.getCurrentConfig();
      this.apiKey = config.apiKey;
      this.baseUrl = config.baseUrl;
      this.model = config.model;

      // 检查API密钥是否有效
      const validation = apiConfig.validateApiKey(this.apiKey);

      // 如果API密钥有效，默认使用AI服务，否则使用模拟服务
      this.useMockService = !validation.valid;

      console.log('API密钥状态:', validation.valid ? '有效' : validation.message);
      console.log('服务模式:', this.useMockService ? '模拟服务' : 'AI服务');
    } catch (error) {
      console.warn('无法加载API配置，使用模拟服务:', error.message);
      this.useMockService = true;
    }

    console.log('✅ 简化版AI服务初始化完成，使用:', this.useMockService ? '模拟服务' : 'AI服务');

    // 请求限制相关
    this.lastRequestTime = 0;
    this.minRequestInterval = 5000; // 最小请求间隔5秒，避免429错误

    // 缓存机制
    this.scriptCache = new Map();
    this.cacheExpireTime = 10 * 60 * 1000; // 缓存10分钟
  }

  /**
   * 设置服务模式
   * @param {boolean} useMock - 是否使用模拟服务
   */
  setServiceMode(useMock) {
    this.useMockService = useMock;
    console.log('🔄 切换服务模式:', useMock ? '模拟服务' : 'AI服务');
  }

  /**
   * 强制使用AI服务（如果API密钥有效）
   */
  forceUseAI() {
    if (this.apiKey) {
      this.useMockService = false;
      console.log('🤖 强制使用AI服务');
    } else {
      console.warn('⚠️ 无有效API密钥，无法使用AI服务');
    }
  }

  /**
   * 强制使用模拟服务
   */
  forceUseMock() {
    this.useMockService = true;
    console.log('🎭 强制使用模拟服务');
  }

  /**
   * 生成剧本故事
   * @param {Object} params - 生成参数
   * @returns {Promise<Object>} 生成的剧本内容
   */
  async generateScript(params) {
    console.log('📝 使用简化版AI服务生成剧本...');
    console.log('参数:', params);

    // 生成缓存键
    const cacheKey = this.generateCacheKey(params);

    // 检查缓存
    const cachedResult = this.getFromCache(cacheKey);
    if (cachedResult) {
      console.log('✅ 使用缓存的剧本数据');
      return cachedResult;
    }

    // 如果使用模拟服务
    if (this.useMockService) {
      console.log('🎭 使用模拟服务生成剧本');
      try {
        const scriptData = await this.mockService.generateStoryPrompt(params);
        const result = {
          success: true,
          scriptData: scriptData,
          source: 'mock'
        };
        console.log('✅ 模拟剧本生成成功');
        // 缓存结果
        this.saveToCache(cacheKey, result);
        return result;
      } catch (error) {
        console.error('❌ 模拟剧本生成失败:', error);
        return {
          success: false,
          error: error.message,
          source: 'mock'
        };
      }
    }

    // 尝试使用真正的AI服务
    console.log('🤖 尝试使用真正的AI服务');
    try {
      const aiResult = await this.callRealAI(params);
      const result = {
        success: true,
        scriptData: aiResult,
        source: 'ai'
      };
      console.log('✅ AI剧本生成成功');
      // 缓存结果
      this.saveToCache(cacheKey, result);
      return result;
    } catch (error) {
      console.error('❌ AI服务失败，回退到模拟服务:', error.message);
      this.useMockService = true;
      try {
        const scriptData = await this.mockService.generateStoryPrompt(params);
        const result = {
          success: true,
          scriptData: scriptData,
          source: 'mock_fallback'
        };
        // 缓存结果
        this.saveToCache(cacheKey, result);
        return result;
      } catch (fallbackError) {
        return {
          success: false,
          error: fallbackError.message,
          source: 'mock_fallback'
        };
      }
    }
  }

  /**
   * 生成缓存键
   */
  generateCacheKey(params) {
    const key = `${params.storyType || 'mystery'}_${params.playerCount || 6}_${params.difficulty || 'medium'}_${params.theme || 'default'}`;
    return key;
  }

  /**
   * 从缓存获取数据
   */
  getFromCache(key) {
    const cached = this.scriptCache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheExpireTime) {
      return cached.data;
    }
    // 清理过期缓存
    if (cached) {
      this.scriptCache.delete(key);
    }
    return null;
  }

  /**
   * 保存到缓存
   */
  saveToCache(key, data) {
    this.scriptCache.set(key, {
      data: data,
      timestamp: Date.now()
    });

    // 限制缓存大小
    if (this.scriptCache.size > 10) {
      const firstKey = this.scriptCache.keys().next().value;
      this.scriptCache.delete(firstKey);
    }
  }

  /**
   * 检查请求限制
   */
  async checkRequestLimit() {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    if (timeSinceLastRequest < this.minRequestInterval) {
      const waitTime = this.minRequestInterval - timeSinceLastRequest;
      console.log(`⏳ 请求限制：等待 ${waitTime}ms`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    this.lastRequestTime = Date.now();
  }

  /**
   * 调用真正的AI服务
   */
  async callRealAI(params) {
    // 检查请求限制
    await this.checkRequestLimit();

    // 构建提示词
    const systemPrompt = this.getSystemPrompt();
    const userPrompt = this.getScriptPrompt(params);

    const messages = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt }
    ];

    // 发送请求
    const response = await this.sendAIRequest(messages);

    // 解析响应
    return this.parseAIResponse(response, params.playerCount);
  }

  /**
   * 发送AI请求
   */
  async sendAIRequest(messages) {
    // 应用请求限制
    await this.checkRequestLimit();

    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.baseUrl}/chat/completions`,
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        data: {
          model: this.model,
          messages: messages,
          temperature: 0.8,
          max_tokens: 3000,
          stream: false
        },
        timeout: 120000,
        success: (res) => {
          if (res.statusCode === 200 && res.data && res.data.choices) {
            resolve(res.data.choices[0].message.content);
          } else if (res.statusCode === 429) {
            // 429错误，自动切换到模拟服务
            console.warn('⚠️ API请求频率限制，自动切换到模拟服务');
            this.useMockService = true;
            reject(new Error(`API请求频率限制: ${res.statusCode}`));
          } else {
            reject(new Error(`API请求失败: ${res.statusCode}`));
          }
        },
        fail: (error) => {
          // 检查是否是429错误
          if (error.errMsg && error.errMsg.includes('429')) {
            console.warn('⚠️ API请求频率限制，自动切换到模拟服务');
            this.useMockService = true;
          }
          reject(new Error(`网络请求失败: ${error.errMsg}`));
        }
      });
    });
  }

  /**
   * 生成故事背景和设定（兼容旧接口）
   * @param {Object} params - 生成参数
   * @returns {Promise<Object>} 生成的故事内容
   */
  async generateStoryPrompt(params) {
    return await this.generateScript(params);
  }

  /**
   * 测试API连接
   * @returns {Promise<boolean>} 连接是否成功
   */
  async testConnection() {
    console.log('🔗 测试连接（简化版）...');
    // 简化版直接返回成功
    return true;
  }

  /**
   * 检查是否应该使用模拟服务
   */
  shouldUseMockService() {
    return this.useMockService;
  }

  /**
   * 获取系统提示词
   */
  getSystemPrompt() {
    try {
      const AIPrompts = require('./ai-prompts');
      return AIPrompts.getSystemPrompt();
    } catch (error) {
      console.warn('无法加载AI提示词，使用简化版本:', error.message);
      return `你是一位世界顶级的剧本杀游戏设计师，专门为微信小程序创建高质量的多人社交推理游戏剧本。请严格按照JSON格式输出，确保数据结构完整且可解析。`;
    }
  }

  /**
   * 获取剧本生成提示词
   */
  getScriptPrompt(params) {
    try {
      const AIPrompts = require('./ai-prompts');
      return AIPrompts.getScriptGenerationPrompt(params);
    } catch (error) {
      console.warn('无法加载AI提示词，使用简化版本:', error.message);
      const { storyType = 'mystery', playerCount = 6, difficulty = 'medium', theme = '现代都市' } = params;

      return `请创建一个${storyType}类型的推理游戏剧本，${playerCount}个角色，${difficulty}难度，主题是${theme}。

请严格按照以下JSON格式输出：
\`\`\`json
{
  "storyInfo": {
    "title": "剧本标题",
    "background": "故事背景描述",
    "setting": "故事发生地点和时间",
    "coreEvent": "核心事件描述"
  },
  "characters": [
    {
      "id": "character_1",
      "name": "角色姓名",
      "title": "角色身份",
      "background": "角色背景",
      "secrets": ["秘密1", "秘密2"],
      "preview": {
        "tagline": "角色标签",
        "intrigue": "悬念点"
      }
    }
  ],
  "truthQuestions": ["问题1", "问题2", "问题3"],
  "miniGameTopics": ["题目1", "题目2", "题目3"]
}
\`\`\`

必须精确生成${playerCount}个角色，确保JSON格式正确。`;
    }
  }

  /**
   * 解析AI响应
   */
  parseAIResponse(response, playerCount) {
    try {
      console.log('🔍 开始解析AI响应...');
      console.log('原始响应长度:', response.length);

      // 第一步：提取JSON代码块
      let jsonStr = response;
      const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        jsonStr = jsonMatch[1];
        console.log('📦 提取到JSON代码块，长度:', jsonStr.length);
      } else {
        console.log('⚠️ 未找到JSON代码块，尝试直接解析');
      }

      // 第二步：强化清理
      jsonStr = this.superCleanJsonString(jsonStr);
      console.log('🧹 清理后长度:', jsonStr.length);
      console.log('🔍 清理后前100字符:', jsonStr.substring(0, 100));

      // 第三步：检查是否为空或无效
      if (!jsonStr || jsonStr.length < 10) {
        throw new Error('JSON字符串为空或过短');
      }

      // 第四步：尝试解析
      const result = JSON.parse(jsonStr);
      console.log('✅ AI响应解析成功');

      // 第五步：验证角色数量
      if (result.characters && result.characters.length !== playerCount) {
        console.warn(`⚠️ 角色数量不匹配：期望${playerCount}个，实际${result.characters.length}个`);
      }

      return result;
    } catch (error) {
      console.error('❌ AI响应解析失败:', error);
      console.log('原始响应前500字符:', response.substring(0, 500) + '...');

      // 尝试修复常见问题
      try {
        console.log('🔧 尝试修复JSON...');
        const fixedResult = this.tryFixJson(response);
        if (fixedResult) {
          console.log('✅ JSON修复成功');
          return fixedResult;
        }
      } catch (fixError) {
        console.error('❌ JSON修复也失败:', fixError);
      }

      // 最后的尝试：提取部分信息并与模拟服务结合
      try {
        console.log('🎭 尝试提取部分AI信息并结合模拟服务...');
        const hybridResult = this.createHybridResult(response, playerCount);
        if (hybridResult) {
          console.log('✅ 混合结果生成成功');
          return hybridResult;
        }
      } catch (hybridError) {
        console.error('❌ 混合结果生成失败:', hybridError);
      }

      throw new Error('AI响应格式错误');
    }
  }

  /**
   * 强化JSON字符串清理
   */
  superCleanJsonString(jsonStr) {
    let cleaned = jsonStr
      .trim()
      .replace(/\uFEFF/g, '') // 移除BOM
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // 移除控制字符
      .replace(/```json/g, '') // 移除代码块标记
      .replace(/```/g, '') // 移除代码块结束标记
      .replace(/\\(?!["\\/bfnrtu])/g, '') // 移除无效反斜杠
      .replace(/\\\\/g, '\\') // 修复双反斜杠
      .replace(/\\\s/g, ' ') // 反斜杠+空格
      .replace(/\\\n/g, '\n') // 反斜杠+换行
      .replace(/\\\r/g, '\r') // 反斜杠+回车
      .replace(/\\$/gm, '') // 行尾反斜杠
      .replace(/\/\/.*$/gm, '') // 移除单行注释
      .replace(/\/\*[\s\S]*?\*\//g, '') // 移除多行注释
      .trim(); // 再次清理首尾空格

    return cleaned;
  }

  /**
   * 尝试修复JSON格式问题
   */
  tryFixJson(response) {
    console.log('🔧 开始JSON修复尝试...');

    // 方法1：寻找完整的JSON对象
    const jsonStart = response.indexOf('{');
    const jsonEnd = response.lastIndexOf('}');

    if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
      const extractedJson = response.substring(jsonStart, jsonEnd + 1);
      console.log('🎯 提取JSON对象，长度:', extractedJson.length);

      try {
        const cleaned = this.superCleanJsonString(extractedJson);
        const result = JSON.parse(cleaned);
        console.log('✅ 方法1成功');
        return result;
      } catch (error) {
        console.log('❌ 方法1失败:', error.message);
      }
    }

    // 方法2：尝试修复截断的JSON
    try {
      const truncatedJson = this.fixTruncatedJson(response);
      if (truncatedJson) {
        console.log('✅ 方法2成功');
        return truncatedJson;
      }
    } catch (error) {
      console.log('❌ 方法2失败:', error.message);
    }

    return null;
  }

  /**
   * 修复截断的JSON
   */
  fixTruncatedJson(response) {
    console.log('🔧 开始修复截断的JSON...');

    // 如果JSON被截断，尝试补全基本结构
    let jsonStr = response;

    // 提取到第一个 { 开始的部分
    const startIndex = jsonStr.indexOf('{');
    if (startIndex !== -1) {
      jsonStr = jsonStr.substring(startIndex);
    }

    console.log('📝 处理前JSON长度:', jsonStr.length);
    console.log('📝 JSON结尾:', jsonStr.slice(-50));

    // 智能修复截断的JSON
    jsonStr = this.smartFixTruncation(jsonStr);

    try {
      const cleaned = this.superCleanJsonString(jsonStr);
      const result = JSON.parse(cleaned);
      console.log('✅ 截断修复成功');
      return result;
    } catch (error) {
      console.log('❌ 截断修复失败:', error.message);
      return null;
    }
  }

  /**
   * 智能修复JSON截断
   */
  smartFixTruncation(jsonStr) {
    let fixed = jsonStr;

    // 1. 处理字符串截断（如果最后是不完整的字符串）
    if (fixed.match(/[^"]*"[^"]*$/)) {
      // 找到最后一个完整的字段
      const lastCompleteField = fixed.lastIndexOf('",');
      if (lastCompleteField !== -1) {
        fixed = fixed.substring(0, lastCompleteField + 1);
        console.log('🔧 移除了不完整的字符串字段');
      }
    }

    // 2. 处理数组截断（特别是characters数组）
    if (fixed.includes('"characters": [')) {
      // 检查是否有未闭合的数组
      const charactersStart = fixed.indexOf('"characters": [');
      const afterCharacters = fixed.substring(charactersStart + '"characters": ['.length);

      // 计算数组中的对象数量
      const openBraces = (afterCharacters.match(/{/g) || []).length;
      const closeBraces = (afterCharacters.match(/}/g) || []).length;

      if (openBraces > closeBraces) {
        console.log('🔧 检测到未闭合的characters数组');

        // 找到最后一个完整的对象
        let lastCompleteIndex = -1;
        let braceCount = 0;

        for (let i = 0; i < afterCharacters.length; i++) {
          if (afterCharacters[i] === '{') {
            braceCount++;
          } else if (afterCharacters[i] === '}') {
            braceCount--;
            if (braceCount === 0) {
              lastCompleteIndex = i;
            }
          }
        }

        if (lastCompleteIndex !== -1) {
          const beforeCharacters = fixed.substring(0, charactersStart);
          const completeCharacters = afterCharacters.substring(0, lastCompleteIndex + 1);
          fixed = beforeCharacters + '"characters": [' + completeCharacters + ']';
          console.log('🔧 修复了截断的characters数组');
        }
      }
    }

    // 3. 处理对象截断
    if (!fixed.trim().endsWith('}')) {
      // 移除最后一个不完整的字段
      const lastComma = fixed.lastIndexOf(',');
      const lastCompleteField = fixed.lastIndexOf('}');

      if (lastComma > lastCompleteField) {
        fixed = fixed.substring(0, lastComma);
        console.log('🔧 移除了最后一个不完整的字段');
      }

      // 计算并添加缺失的括号
      const openBraces = (fixed.match(/\{/g) || []).length;
      const closeBraces = (fixed.match(/\}/g) || []).length;
      const openBrackets = (fixed.match(/\[/g) || []).length;
      const closeBrackets = (fixed.match(/\]/g) || []).length;

      const missingBraces = openBraces - closeBraces;
      const missingBrackets = openBrackets - closeBrackets;

      if (missingBrackets > 0) {
        fixed += ']'.repeat(missingBrackets);
        console.log(`🔧 添加了${missingBrackets}个结束方括号`);
      }

      if (missingBraces > 0) {
        fixed += '}'.repeat(missingBraces);
        console.log(`🔧 添加了${missingBraces}个结束大括号`);
      }
    }

    return fixed;
  }

  /**
   * 找到最后一个完整的角色对象
   */
  findLastCompleteCharacter(charactersSection) {
    const characters = [];
    let currentChar = '';
    let braceCount = 0;
    let inString = false;
    let escapeNext = false;

    for (let i = 0; i < charactersSection.length; i++) {
      const char = charactersSection[i];

      if (escapeNext) {
        escapeNext = false;
        currentChar += char;
        continue;
      }

      if (char === '\\') {
        escapeNext = true;
        currentChar += char;
        continue;
      }

      if (char === '"') {
        inString = !inString;
      }

      if (!inString) {
        if (char === '{') {
          braceCount++;
        } else if (char === '}') {
          braceCount--;
          if (braceCount === 0 && currentChar.trim()) {
            // 找到一个完整的角色对象
            characters.push(currentChar + '}');
            currentChar = '';
            continue;
          }
        }
      }

      currentChar += char;
    }

    console.log(`🔍 找到${characters.length}个完整的角色对象`);
    return characters.join(',');
  }

  /**
   * 创建混合结果（AI部分信息 + 模拟服务补全）
   */
  async createHybridResult(aiResponse, playerCount) {
    console.log('🎨 创建混合结果...');

    // 尝试提取AI生成的标题和背景
    const extractedInfo = this.extractPartialInfo(aiResponse);

    // 获取模拟服务的基础结果
    const mockResult = await this.mockService.generateStoryPrompt({
      storyType: 'romance',
      playerCount: playerCount,
      difficulty: 'medium'
    });

    // 如果提取到了AI信息，用它替换模拟服务的对应部分
    if (extractedInfo.title || extractedInfo.background) {
      console.log('🔄 使用AI信息增强模拟结果...');

      if (extractedInfo.title) {
        mockResult.storyInfo.title = extractedInfo.title;
        console.log('✅ 使用AI生成的标题:', extractedInfo.title);
      }

      if (extractedInfo.background) {
        mockResult.storyInfo.background = extractedInfo.background;
        console.log('✅ 使用AI生成的背景');
      }

      if (extractedInfo.setting) {
        mockResult.storyInfo.setting = extractedInfo.setting;
        console.log('✅ 使用AI生成的设定');
      }
    }

    return mockResult;
  }

  /**
   * 从AI响应中提取部分信息
   */
  extractPartialInfo(response) {
    const info = {};

    try {
      // 提取标题
      const titleMatch = response.match(/"title":\s*"([^"]+)"/);
      if (titleMatch) {
        info.title = titleMatch[1];
      }

      // 提取背景
      const backgroundMatch = response.match(/"background":\s*"([^"]+)"/);
      if (backgroundMatch) {
        info.background = backgroundMatch[1];
      }

      // 提取设定
      const settingMatch = response.match(/"setting":\s*"([^"]+)"/);
      if (settingMatch) {
        info.setting = settingMatch[1];
      }

      console.log('📝 提取到的信息:', Object.keys(info));
    } catch (error) {
      console.warn('⚠️ 信息提取失败:', error.message);
    }

    return info;
  }

  /**
   * 生成角色分配
   * @param {Object} scriptData - 剧本数据
   * @param {Array} playerIds - 玩家ID列表
   * @returns {Promise<Object>} 角色分配结果
   */
  async generateRoleAssignment(scriptData, playerIds) {
    console.log('🎭 开始生成角色分配...');
    console.log('玩家数量:', playerIds.length);
    console.log('角色数量:', scriptData.characters ? scriptData.characters.length : 0);

    try {
      // 确保角色数量和玩家数量匹配
      if (!scriptData.characters || scriptData.characters.length !== playerIds.length) {
        console.warn('⚠️ 角色数量与玩家数量不匹配，使用随机分配');
        return this.generateRandomAssignment(scriptData, playerIds);
      }

      // 智能角色分配（考虑角色平衡性）
      const assignments = this.generateBalancedAssignment(scriptData.characters, playerIds);

      const result = {
        success: true,
        assignments: assignments,
        timestamp: new Date().toISOString()
      };

      console.log('✅ 角色分配生成成功');
      return result;

    } catch (error) {
      console.error('❌ 角色分配生成失败:', error);
      // 降级到随机分配
      return this.generateRandomAssignment(scriptData, playerIds);
    }
  }

  /**
   * 生成平衡的角色分配
   */
  generateBalancedAssignment(characters, playerIds) {
    console.log('⚖️ 生成平衡角色分配...');

    // 复制数组避免修改原数据
    const availableCharacters = [...characters];
    const availablePlayers = [...playerIds];
    const assignments = [];

    // 随机打乱角色和玩家顺序
    this.shuffleArray(availableCharacters);
    this.shuffleArray(availablePlayers);

    // 逐一分配
    for (let i = 0; i < Math.min(availableCharacters.length, availablePlayers.length); i++) {
      assignments.push({
        playerId: availablePlayers[i],
        characterId: availableCharacters[i].id,
        assignedAt: new Date().toISOString()
      });
    }

    console.log(`✅ 成功分配${assignments.length}个角色`);
    return assignments;
  }

  /**
   * 生成随机角色分配（降级方案）
   */
  generateRandomAssignment(scriptData, playerIds) {
    console.log('🎲 生成随机角色分配（降级方案）...');

    const assignments = [];
    const characters = scriptData.characters || this.getDefaultCharacters(playerIds.length);

    for (let i = 0; i < playerIds.length; i++) {
      const characterIndex = i % characters.length;
      assignments.push({
        playerId: playerIds[i],
        characterId: characters[characterIndex].id,
        assignedAt: new Date().toISOString()
      });
    }

    return {
      success: true,
      assignments: assignments,
      timestamp: new Date().toISOString(),
      fallback: true
    };
  }

  /**
   * 获取默认角色（当没有AI生成的角色时）
   */
  getDefaultCharacters(count) {
    const defaultCharacters = [
      {
        id: 'char_001',
        name: '艾米丽·哈特',
        title: '庄园女主人',
        background: '神秘庄园的女主人，优雅而危险'
      },
      {
        id: 'char_002',
        name: '詹姆斯·威尔逊',
        title: '私人医生',
        background: '庄园主人的私人医生，知道很多秘密'
      },
      {
        id: 'char_003',
        name: '莉莉安·格雷',
        title: '管家',
        background: '忠诚的管家，见证了庄园的所有变迁'
      },
      {
        id: 'char_004',
        name: '维克多·布莱克',
        title: '律师',
        background: '精明的律师，处理庄园的法律事务'
      },
      {
        id: 'char_005',
        name: '索菲亚·罗斯',
        title: '艺术家',
        background: '才华横溢的画家，与庄园有着特殊联系'
      },
      {
        id: 'char_006',
        name: '亚历山大·斯通',
        title: '商人',
        background: '成功的商人，与庄园主人有商业往来'
      }
    ];

    return defaultCharacters.slice(0, count);
  }

  /**
   * 生成线索
   */
  async generateClues(options = {}) {
    const {
      scriptData,
      myRole,
      gamePhase = 'clue_collection',
      clueCount = 5
    } = options;

    console.log('🔍 AI开始生成线索...');

    try {
      // 如果使用模拟服务，直接使用模板生成
      if (this.useMockService) {
        console.log('🎭 使用模拟服务生成线索');
        const clues = this.generateCluesByTheme(
          scriptData?.storyInfo?.title || '神秘庄园',
          myRole?.name || '调查员',
          clueCount
        );

        return {
          success: true,
          clues: clues,
          metadata: {
            theme: scriptData?.storyInfo?.title || '神秘剧本',
            role: myRole?.name || '调查员',
            phase: gamePhase,
            generatedAt: new Date().toISOString()
          },
          source: 'mock'
        };
      }

      // 构建AI提示词
      const prompt = this.buildCluesPrompt(scriptData, myRole, clueCount);

      // 调用AI API生成线索
      const messages = [
        { role: 'system', content: '你是一位专业的剧本杀游戏设计师。' },
        { role: 'user', content: prompt }
      ];

      const aiResponse = await this.sendAIRequest(messages);

      // 解析AI响应
      const clues = this.parseCluesResponse(aiResponse);

      const result = {
        success: true,
        clues: clues,
        metadata: {
          theme: scriptData?.storyInfo?.title || '神秘剧本',
          role: myRole?.name || '调查员',
          phase: gamePhase,
          generatedAt: new Date().toISOString()
        },
        source: 'ai'
      };

      console.log('✅ AI线索生成完成:', result);
      return result;

    } catch (error) {
      console.error('❌ AI线索生成失败:', error);
      // 如果AI生成失败，使用智能模板作为备选
      const fallbackClues = this.generateCluesByTheme(
        scriptData?.storyInfo?.title || '神秘庄园',
        myRole?.name || '调查员',
        clueCount
      );

      return {
        success: false,
        error: error.message,
        clues: fallbackClues,
        fallback: true,
        source: 'fallback'
      };
    }
  }

  /**
   * 构建线索生成提示词
   */
  buildCluesPrompt(scriptData, myRole, clueCount) {
    const storyTitle = scriptData?.storyInfo?.title || '神秘剧本';
    const storyBackground = scriptData?.storyInfo?.background || '一个充满悬疑的故事';
    const roleName = myRole?.name || '调查员';
    const roleTitle = myRole?.title || '神秘角色';
    const roleBackground = myRole?.background || '一个关键的参与者';

    return `你是一位专业的剧本杀游戏设计师，需要为玩家生成${clueCount}条线索。

剧本信息：
- 标题：${storyTitle}
- 背景：${storyBackground}

玩家角色：
- 姓名：${roleName}
- 身份：${roleTitle}
- 背景：${roleBackground}

请生成${clueCount}条线索，要求：
1. 线索要与剧本主题和角色身份相关
2. 包含不同类型：物品线索、人物线索、地点线索
3. 有关键线索和普通线索的区分
4. 每条线索要有吸引力，能推动剧情发展
5. 线索之间要有一定的关联性

请严格按照以下JSON格式输出：
\`\`\`json
{
  "clues": [
    {
      "title": "线索标题",
      "content": "详细的线索描述，要生动有趣，包含关键信息",
      "type": "item|person|location",
      "importance": "key|normal",
      "tags": ["标签1", "标签2", "标签3"],
      "relatedCharacters": ["相关角色1", "相关角色2"],
      "timeLimit": 15
    }
  ]
}
\`\`\``;
  }

  /**
   * 解析AI线索响应
   */
  parseCluesResponse(aiResponse) {
    try {
      // 提取JSON部分
      const jsonMatch = aiResponse.match(/```json\s*([\s\S]*?)\s*```/);
      if (!jsonMatch) {
        throw new Error('AI响应中未找到JSON格式的线索数据');
      }

      const jsonStr = jsonMatch[1];
      const parsed = JSON.parse(jsonStr);

      if (!parsed.clues || !Array.isArray(parsed.clues)) {
        throw new Error('AI响应格式错误：缺少clues数组');
      }

      // 处理和验证每条线索
      const processedClues = parsed.clues.map((clue, index) => {
        return {
          id: `ai_clue_${Date.now()}_${index}`,
          title: clue.title || `线索${index + 1}`,
          content: clue.content || '暂无详细信息',
          type: clue.type || 'item',
          importance: clue.importance || 'normal',
          timeLimit: clue.timeLimit || Math.floor(Math.random() * 20) + 10,
          tags: Array.isArray(clue.tags) ? clue.tags : [],
          relatedCharacters: Array.isArray(clue.relatedCharacters) ? clue.relatedCharacters : [],
          discovered: index < 2, // 前两个线索默认已发现
          discoveredAt: index < 2 ? new Date().toISOString() : null
        };
      });

      return processedClues;

    } catch (error) {
      console.error('解析AI线索响应失败:', error);
      throw new Error(`线索解析失败: ${error.message}`);
    }
  }

  /**
   * 根据主题生成线索（备选方案）
   */
  generateCluesByTheme(theme, roleName, count) {
    console.log(`🎭 生成${count}条线索，主题: ${theme}, 角色: ${roleName}`);
    const clueTemplates = {
      '神秘庄园': [
        {
          title: '神秘的日记',
          content: '在书房的抽屉里发现了一本日记，记录着庄园主人的秘密往事。日记中提到了一个隐藏的宝藏和一段不为人知的恋情...',
          type: 'item',
          importance: 'key',
          tags: ['日记', '秘密', '书房', '宝藏'],
          relatedCharacters: ['庄园主人', '神秘女子']
        },
        {
          title: '破损的照片',
          content: '在壁炉旁发现了一张被撕毁的老照片，照片中有两个人正在争吵。从背景可以看出这是在庄园的花园里拍摄的...',
          type: 'item',
          importance: 'normal',
          tags: ['照片', '争吵', '花园'],
          relatedCharacters: ['不明身份', '庄园访客']
        },
        {
          title: '管家的证词',
          content: '管家声称在案发当晚听到了激烈的争吵声，声音来自二楼的书房。但当他赶到现场时，只看到了打开的窗户和散落的文件...',
          type: 'person',
          importance: 'key',
          tags: ['证词', '争吵', '书房', '窗户'],
          relatedCharacters: ['管家', '不明人员']
        },
        {
          title: '地下室的钥匙',
          content: '在花园的玫瑰丛中找到了一把生锈的钥匙，钥匙上刻着神秘的符号。这把钥匙似乎可以打开庄园地下室的门...',
          type: 'item',
          importance: 'key',
          tags: ['钥匙', '地下室', '符号', '玫瑰丛'],
          relatedCharacters: []
        },
        {
          title: '书房的密室',
          content: '在书房的书架后面发现了一个隐藏的密室，里面存放着重要的文件和一些古老的物品。墙上还有一幅奇怪的画...',
          type: 'location',
          importance: 'key',
          tags: ['密室', '文件', '古物', '画作'],
          relatedCharacters: []
        },
        {
          title: '厨房的毒药',
          content: '在厨房的调料架上发现了一瓶标签模糊的药瓶，瓶子里残留着不明液体。厨师声称从未见过这个瓶子...',
          type: 'item',
          importance: 'normal',
          tags: ['毒药', '厨房', '药瓶'],
          relatedCharacters: ['厨师']
        },
        {
          title: '花园的脚印',
          content: '在花园的泥土中发现了一串清晰的脚印，脚印通向庄园的后门。从脚印的大小判断，这应该是一个成年男性留下的...',
          type: 'location',
          importance: 'normal',
          tags: ['脚印', '花园', '后门'],
          relatedCharacters: ['不明男性']
        },
        {
          title: '女仆的秘密',
          content: '女仆在清理房间时发现了一封未寄出的信件，信中提到了一个重要的约定。女仆表现得很紧张，似乎在隐瞒什么...',
          type: 'person',
          importance: 'normal',
          tags: ['信件', '约定', '秘密'],
          relatedCharacters: ['女仆', '信件收件人']
        }
      ]
    };

    // 获取对应主题的线索模板，如果没有则使用默认
    const templates = clueTemplates[theme] || clueTemplates['神秘庄园'];

    // 随机选择指定数量的线索
    const selectedClues = [];
    const shuffled = [...templates].sort(() => 0.5 - Math.random());

    for (let i = 0; i < Math.min(count, shuffled.length); i++) {
      const template = shuffled[i];
      selectedClues.push({
        ...template,
        id: `clue_${Date.now()}_${i}`,
        timeLimit: Math.floor(Math.random() * 20) + 10, // 10-30分钟前
        discovered: i < 2 // 前两个线索默认已发现
      });
    }

    return selectedClues;
  }

  /**
   * 获取默认线索
   */
  getDefaultClues() {
    return [
      {
        id: 'default_clue_1',
        title: '神秘的日记',
        content: '在书房的抽屉里发现了一本日记，记录着庄园主人的秘密往事...',
        type: 'item',
        importance: 'key',
        timeLimit: 10,
        tags: ['日记', '秘密'],
        relatedCharacters: ['庄园主人'],
        discovered: true
      }
    ];
  }

  /**
   * 数组随机打乱（Fisher-Yates算法）
   */
  shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
  }

  /**
   * 静态方法：提取剧本数据，兼容新旧格式
   * @param {Object} result - AI服务返回的结果
   * @returns {Object} 提取的剧本数据
   */
  static extractScriptData(result) {
    if (!result) {
      throw new Error('结果为空');
    }

    // 新格式：包含success和scriptData字段
    if (result.success && result.scriptData) {
      return result.scriptData;
    }

    // 旧格式：直接返回剧本数据
    if (result.storyInfo || result.title) {
      return result;
    }

    // 错误格式
    throw new Error(result.error || '无法识别的数据格式');
  }
}

// 创建全局AI服务实例
const aiService = new AIService();

// 同时导出类和实例，方便使用静态方法
aiService.AIService = AIService;

module.exports = aiService;
