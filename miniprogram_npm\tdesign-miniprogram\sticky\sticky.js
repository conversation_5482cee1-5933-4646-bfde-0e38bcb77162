import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import props from"./props";import config from"../common/config";import pageScrollMixin from"../mixins/page-scroll";import{getRect}from"../common/utils";const{prefix:prefix}=config,name=`${prefix}-sticky`,ContainerClass=`.${name}`;let Sticky=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`,`${prefix}-class-content`],this.properties=props,this.behaviors=[pageScrollMixin()],this.observers={"offsetTop, disabled, container"(){this.onScroll()}},this.data={prefix:prefix,classPrefix:name,containerStyle:"",contentStyle:""},this.methods={onScroll(t){const{scrollTop:e}=t||{},{container:i,offsetTop:o,disabled:s}=this.properties;s?this.setDataAfterDiff({isFixed:!1,transform:0}):(this.scrollTop=e||this.scrollTop,"function"!=typeof i?getRect(this,ContainerClass).then(t=>{t&&(o>=t.top?(this.setDataAfterDiff({isFixed:!0,height:t.height}),this.transform=0):this.setDataAfterDiff({isFixed:!1}))}):Promise.all([getRect(this,ContainerClass),this.getContainerRect()]).then(([t,e])=>{t&&e&&(o+t.height>e.height+e.top?this.setDataAfterDiff({isFixed:!1,transform:e.height-t.height}):o>=t.top?this.setDataAfterDiff({isFixed:!0,height:t.height,transform:0}):this.setDataAfterDiff({isFixed:!1,transform:0}))}))},setDataAfterDiff(t){const{offsetTop:e}=this.properties,{containerStyle:i,contentStyle:o}=this.data,{isFixed:s,height:r,transform:n}=t;wx.nextTick(()=>{let t="",a="";if(s&&(t+=`height:${r}px;`,a+=`position:fixed;top:${e}px;left:0;right:0;`),n){const t=`translate3d(0, ${n}px, 0)`;a+=`-webkit-transform:${t};transform:${t};`}i===t&&o===a||this.setData({containerStyle:t,contentStyle:a}),this.triggerEvent("scroll",{scrollTop:this.scrollTop,isFixed:s})})},getContainerRect(){const t=this.properties.container();return new Promise(e=>t.boundingClientRect(e).exec())}}}ready(){this.onScroll()}};Sticky=__decorate([wxComponent()],Sticky);export default Sticky;