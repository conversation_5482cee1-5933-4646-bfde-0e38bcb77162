import config from"../common/config";const{prefix:prefix}=config;export default function transition(){return Behavior({properties:{visible:{type:<PERSON><PERSON><PERSON>,value:null,observer:"watchVisible"},appear:<PERSON><PERSON><PERSON>,name:{type:String,value:"fade"},durations:{type:Number,optionalTypes:[Array]}},data:{transitionClass:"",transitionDurations:300,className:"",realVisible:!1},created(){this.status="",this.transitionT=0},attached(){this.durations=this.getDurations(),this.data.visible&&this.enter(),this.inited=!0},detached(){clearTimeout(this.transitionT)},methods:{watchVisible(t,i){this.inited&&t!==i&&(t?this.enter():this.leave())},getDurations(){const{durations:t}=this.data;return Array.isArray(t)?t.map(t=>Number(t)):[Number(t),Number(t)]},enter(){const{name:t}=this.data,[i]=this.durations;this.status="entering",this.setData({realVisible:!0,transitionClass:`${prefix}-${t}-enter ${prefix}-${t}-enter-active`}),setTimeout(()=>{this.setData({transitionClass:`${prefix}-${t}-enter-active ${prefix}-${t}-enter-to`})},30),"number"==typeof i&&i>0&&(this.transitionT=setTimeout(this.entered.bind(this),i+30))},entered(){this.customDuration=!1,clearTimeout(this.transitionT),this.status="entered",this.setData({transitionClass:""})},leave(){const{name:t}=this.data,[,i]=this.durations;this.status="leaving",this.setData({transitionClass:`${prefix}-${t}-leave  ${prefix}-${t}-leave-active`}),clearTimeout(this.transitionT),setTimeout(()=>{this.setData({transitionClass:`${prefix}-${t}-leave-active ${prefix}-${t}-leave-to`})},30),"number"==typeof i&&i>0&&(this.customDuration=!0,this.transitionT=setTimeout(this.leaved.bind(this),i+30))},leaved(){this.customDuration=!1,this.triggerEvent("leaved"),clearTimeout(this.transitionT),this.status="leaved",this.setData({transitionClass:""})},onTransitionEnd(){this.customDuration||(clearTimeout(this.transitionT),"entering"===this.status&&this.data.visible?this.entered():"leaving"!==this.status||this.data.visible||(this.leaved(),this.setData({realVisible:!1})))}}})}