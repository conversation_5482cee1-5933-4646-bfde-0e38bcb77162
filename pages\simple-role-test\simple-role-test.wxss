/* 简单角色分配测试页面样式 */
.container {
  padding: 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 测试控制 */
.test-controls {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.test-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

.test-btn.primary {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.test-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

/* 测试结果 */
.result-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
  overflow: hidden;
}

.result-header {
  background: rgba(0, 0, 0, 0.2);
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.result-title {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

.result-content {
  padding: 30rpx;
  max-height: 600rpx;
  overflow-y: auto;
}

.result-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 26rpx;
  line-height: 1.6;
  white-space: pre-line;
  font-family: 'Courier New', monospace;
}

/* 导航按钮 */
.navigation {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.nav-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  background: rgba(255, 255, 255, 0.15);
  color: white;
  font-size: 28rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
}

/* 说明信息 */
.info-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  backdrop-filter: blur(10rpx);
}

.info-title {
  display: block;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.info-item {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
  line-height: 1.4;
}
