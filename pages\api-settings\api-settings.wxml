<!-- pages/api-settings/api-settings.wxml -->
<view class="container">
  <view class="header">
    <text class="title">API密钥设置</text>
    <text class="subtitle">配置Moonshot AI API密钥</text>
  </view>

  <!-- 当前密钥状态 -->
  <view class="current-key-section">
    <view class="section-title">当前密钥状态</view>
    <view class="key-status">
      <view class="key-display">
        <text class="key-text">{{maskedApiKey}}</text>
        <view class="status-indicator {{isValidKey ? 'valid' : 'invalid'}}">
          <text class="status-text">{{isValidKey ? '已设置' : '未设置'}}</text>
        </view>
      </view>
      <button class="test-btn" bindtap="testConnection" disabled="{{!isValidKey || isTestingConnection}}">
        {{isTestingConnection ? '测试中...' : '测试连接'}}
      </button>
    </view>
  </view>

  <!-- 设置新密钥 -->
  <view class="input-section">
    <view class="section-title">设置新密钥</view>
    <view class="input-group">
      <textarea 
        class="api-key-input" 
        placeholder="请输入新的API密钥 (sk-...)"
        value="{{apiKey}}"
        bindinput="onApiKeyInput"
        maxlength="200"
        auto-height
      ></textarea>
      <view class="validation-message {{isValidKey ? 'valid' : 'invalid'}}" wx:if="{{validationMessage}}">
        {{validationMessage}}
      </view>
    </view>
    
    <view class="button-group">
      <button class="save-btn" bindtap="saveApiKey" disabled="{{!isValidKey}}">
        保存密钥
      </button>
      <button class="clear-btn" bindtap="clearApiKey" disabled="{{!isValidKey}}">
        清除密钥
      </button>
    </view>
  </view>

  <!-- 使用说明 -->
  <view class="instructions-section">
    <view class="section-title">使用说明</view>
    <view class="instruction-item">
      <text class="step-number">1</text>
      <text class="step-text">登录 Moonshot AI 控制台</text>
    </view>
    <view class="instruction-item">
      <text class="step-number">2</text>
      <text class="step-text">生成新的API密钥</text>
    </view>
    <view class="instruction-item">
      <text class="step-number">3</text>
      <text class="step-text">复制密钥并粘贴到上方输入框</text>
    </view>
    <view class="instruction-item">
      <text class="step-number">4</text>
      <text class="step-text">保存并测试连接</text>
    </view>
    
    <button class="help-btn" bindtap="showInstructions">
      查看详细说明
    </button>

    <button class="test-page-btn" bindtap="goToTestPage">
      前往测试页面
    </button>
  </view>

  <!-- 安全提醒 -->
  <view class="security-notice">
    <view class="notice-icon">🚨</view>
    <view class="notice-content">
      <text class="notice-title">重要安全提醒</text>
      <text class="notice-text">⚠️ Moonshot AI会自动检测和禁用泄露的API密钥！</text>
      <text class="notice-text">🔑 如果出现401错误，说明密钥已被禁用，请立即生成新密钥</text>
      <text class="notice-text">📸 不要在截图或文档中展示完整密钥</text>
      <text class="notice-text">🔒 密钥仅存储在本设备，定期更换以确保安全</text>
    </view>
  </view>
</view>
