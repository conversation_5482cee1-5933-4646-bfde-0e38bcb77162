<wxs src="../common/utils.wxs" module="_"/><view class="{{ prefix ? prefix : classPrefix}} class {{componentPrefix}}-class" style="{{_._style([iconStyle, style, customStyle])}}" bind:tap="onTap" aria-hidden="{{ariaHidden}}" aria-label="{{ariaLabel}}" aria-role="{{ariaRole}}"><view wx:if="{{ isImage }}" class="{{classPrefix}}--image"><image src="{{ name }}" mode="aspectFit" class="{{classPrefix}}__image"/></view><label wx:if="{{ _.isValidIconName(name) && !isImage }}" class="{{ prefix ? prefix : classPrefix }}-{{ name}} {{ classPrefix }}-base"></label></view>