// API工具类
const BASE_URL = 'https://api.example.com'; // 替换为实际的API地址

class ApiService {
  constructor() {
    this.baseURL = BASE_URL;
    this.timeout = 10000;
  }

  // 通用请求方法
  request(options) {
    return new Promise((resolve, reject) => {
      const { url, method = 'GET', data = {}, header = {} } = options;
      
      // 添加通用请求头
      const defaultHeader = {
        'Content-Type': 'application/json',
        'Authorization': wx.getStorageSync('token') || '',
        ...header
      };

      wx.request({
        url: this.baseURL + url,
        method,
        data,
        header: defaultHeader,
        timeout: this.timeout,
        success: (res) => {
          if (res.statusCode === 200) {
            if (res.data.code === 0) {
              resolve(res.data);
            } else {
              reject(new Error(res.data.message || '请求失败'));
            }
          } else {
            reject(new Error(`HTTP ${res.statusCode}`));
          }
        },
        fail: (err) => {
          reject(new Error(err.errMsg || '网络请求失败'));
        }
      });
    });
  }

  // GET请求
  get(url, params = {}) {
    const queryString = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');
    
    const fullUrl = queryString ? `${url}?${queryString}` : url;
    
    return this.request({
      url: fullUrl,
      method: 'GET'
    });
  }

  // POST请求
  post(url, data = {}) {
    return this.request({
      url,
      method: 'POST',
      data
    });
  }

  // PUT请求
  put(url, data = {}) {
    return this.request({
      url,
      method: 'PUT',
      data
    });
  }

  // DELETE请求
  delete(url) {
    return this.request({
      url,
      method: 'DELETE'
    });
  }

  // 房间相关API
  getRoomList(params = {}) {
    return this.get('/rooms', params);
  }

  createRoom(roomData) {
    return this.post('/rooms', roomData);
  }

  getRoomInfo(roomId) {
    return this.get(`/rooms/${roomId}`);
  }

  joinRoom(roomId, password = '') {
    return this.post(`/rooms/${roomId}/join`, { password });
  }

  leaveRoom(roomId) {
    return this.post(`/rooms/${roomId}/leave`);
  }

  updateReadyStatus(roomId, isReady) {
    return this.put(`/rooms/${roomId}/ready`, { isReady });
  }

  // 游戏相关API
  generateScript(roomId, settings) {
    return this.post(`/games/${roomId}/generate-script`, settings);
  }

  getGameInfo(roomId) {
    return this.get(`/games/${roomId}`);
  }

  getStoryBackground(roomId) {
    return this.get(`/games/${roomId}/story`);
  }

  getCharacterRelations(roomId) {
    return this.get(`/games/${roomId}/relations`);
  }

  getRoleAssignment(roomId) {
    return this.get(`/games/${roomId}/roles`);
  }

  getPrivateClues(roomId, playerId) {
    return this.get(`/games/${roomId}/clues/${playerId}`);
  }

  submitDiscussion(roomId, content) {
    return this.post(`/games/${roomId}/discussion`, { content });
  }

  submitTruthDare(roomId, answer) {
    return this.post(`/games/${roomId}/truth-dare`, answer);
  }

  playMiniGame(roomId, gameData) {
    return this.post(`/games/${roomId}/mini-game`, gameData);
  }

  submitVote(roomId, voteData) {
    return this.post(`/games/${roomId}/vote`, voteData);
  }

  getVoteResult(roomId) {
    return this.get(`/games/${roomId}/vote-result`);
  }

  submitFinalVote(roomId, finalVote) {
    return this.post(`/games/${roomId}/final-vote`, finalVote);
  }

  getGameResult(roomId) {
    return this.get(`/games/${roomId}/result`);
  }

  // 用户相关API
  getUserInfo() {
    return this.get('/user/info');
  }

  updateUserInfo(userData) {
    return this.put('/user/info', userData);
  }

  getUserHistory(page = 1, limit = 10) {
    return this.get('/user/history', { page, limit });
  }

  // 聊天相关API
  sendMessage(roomId, message) {
    return this.post(`/chat/${roomId}/send`, message);
  }

  getChatHistory(roomId, page = 1) {
    return this.get(`/chat/${roomId}/history`, { page });
  }
}

// 创建API实例
const api = new ApiService();

// 导出API实例
module.exports = api;
