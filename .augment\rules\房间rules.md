---
type: "agent_requested"
description: "Example description"
---
首先明确你的游戏需要哪些数据访问权限：

核心数据表：
rooms - 房间信息
players - 玩家信息
game_records - 游戏记录
messages - 聊天消息
clues - 游戏线索
votes - 投票记录
🔐 数据库安全规则设计
1. 房间表规则 (rooms)
database
{
  "read": "auth != null && (doc.status == 'waiting' || doc.players.playerId.indexOf(auth.openid) > -1)",
  "write": "auth != null && (doc._openid == auth.openid || doc.hostId == auth.openid)",
  "create": "auth != null",
  "update": "auth != null && (doc.
规则说明：

读取：已登录用户可以看到等待中的房间，或自己参与的房间
写入：只有房主可以修改房间基本信息
创建：任何已登录用户都可以创建房间
更新：房主和房间内玩家可以更新房间状态
删除：只有房主可以删除房间
2. 玩家表规则 (players)
database
规则说明：

读取：只能读取自己的信息，或同房间玩家的基本信息
写入/更新：只能修改自己的信息
创建：首次登录时自动创建玩家记录
删除：可以删除自己的账号
3. 游戏记录表规则 (game_records)
database
{
  "read": "auth != null && doc.players.playerId.indexOf(auth.openid) > -1",
  "write": "auth != null && doc.hostId == auth.openid && doc.status == 'finished'",
  "create": "auth != null && doc.hostId == auth.openid",
  "update": "auth != null && doc.
规则说明：

读取：只有参与过该游戏的玩家可以查看记录
写入：只有房主可以在游戏结束后写入记录
删除：游戏记录不允许删除（保证数据完整性）
4. 聊天消息表规则 (messages)
database
{
  "read": "auth != null && (doc.roomId == '' || get('database.rooms.${doc.roomId}').players.playerId.indexOf(auth.openid) > -1)",
  "write": "auth != null && doc._openid == auth.openid",
  "create": "auth != null && doc.senderId == auth.openid",

规则说明：

读取：只有同房间的玩家可以看到消息
创建：只能以自己的身份发送消息
更新：只能在发送后5分钟内修改自己的消息
删除：发送者和房主可以删除消息
5. 游戏线索表规则 (clues)
database
{
  "read": "auth != null && (doc.isPublic == true || doc.ownerId == auth.openid || (doc.sharedWith != null && doc.sharedWith.indexOf(auth.openid) > -1))",
  "write": "auth != null && doc.ownerId == auth.openid",
  "create": "auth != null",

规则说明：

读取：可以看到公开线索、自己的线索、或被分享给自己的线索
写入：只能修改自己拥有的线索
更新：线索拥有者和房主可以更新线索状态
删除：线索拥有者和房主可以删除线索
6. 投票记录表规则 (votes)
database
{
  "read": "auth != null && get('database.rooms.${doc.roomId}').players.playerId.indexOf(auth.openid) > -1",
  "write": "auth != null && doc.voterId == auth.openid && get('database.rooms.${doc.roomId}').status == 'voting'",
  "create": "auth != null && doc.
规则说明：

读取：同房间玩家可以查看投票结果
写入/更新：只能在投票阶段修改自己的投票
删除：投票记录不可删除（保证游戏公平性）
🛡️ 高级安全规则
7. 防刷机制
database
{
  "rooms": {
    "create": "auth != null && query.where({_openid: auth.openid, status: 'waiting'}).count() < 3",
    "update": "auth != null && (now() - doc._updateTime > 1000)"
  },

防护措施：

房间创建限制：每个用户最多同时创建3个等待中的房间
消息频率限制：每分钟最多发送20条消息
投票冷却：投票修改需要间隔5秒
更新频率限制：房间状态更新需要间隔1秒
8. 数据验证规则
database
{
  "rooms": {
    "write": "auth != null && doc.maxPlayers >= 3 && doc.maxPlayers <= 10 && ['classic', 'social', 'puzzle', 'roleplay'].indexOf(doc.gameStyle) > -1"
  },
  "players": {

验证内容：

房间：玩家数量3-10人，游戏风格必须是预定义类型
玩家：昵称长度2-20字符
消息：内容不能为空，最长500字符
🔧 云函数权限配置
管理员权限云函数
cloudfunctions/admin
const cloud = require('wx-server-sdk');
cloud.init();

const db = cloud.database();
const _ = db.command;

// 管理员OpenID列表
const ADMIN_OPENIDS = [
  'your-admin-openid-1',

📊 规则测试
测试用例
test
🎯 部署配置
在云开发控制台配置规则
登录微信云开发控制台
选择你的环境
进入"数据库" → "安全规则"
为每个集合配置对应的规则
规则生效验证
utils
这套规则确保了：

🔒 数据安全：用户只能访问有权限的数据
🚫 防止作弊：严格的投票和游戏状态控制
⚡ 性能优化：合理的查询权限避免全表扫描
🛡️ 防刷保护：频率限制和数据验证