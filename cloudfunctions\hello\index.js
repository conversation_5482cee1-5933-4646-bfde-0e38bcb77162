// cloudfunctions/hello/index.js
// 简单的测试云函数

const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  console.log('Hello云函数被调用 - 更新版本:', event);

  return {
    success: true,
    message: 'Hello from 云开发! - 更新版本',
    timestamp: new Date().toISOString(),
    version: '1.1.0',
    event: event,
    openid: context.OPENID,
    appid: context.APPID,
    unionid: context.UNIONID,
    env: context.ENV
  };
};
