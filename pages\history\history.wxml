<!--历史记录页面-->
<view class="history-container">
  <!-- 标签页切换 -->
  <view class="tabs-section">
    <t-tabs value="{{activeTab}}" bind:change="onTabChange" class="history-tabs">
      <t-tab-panel value="games" label="游戏历史">
        <!-- 游戏历史列表 -->
        <view class="games-list">
          <view class="section-header">
            <view class="section-title">
              <t-icon name="game" color="#1890ff" />
              <text>游戏历史</text>
            </view>
            <view class="total-count">总计 {{gameHistory.length}} 场</view>
          </view>

          <view class="game-cards">
            <view 
              class="game-card {{item.result}}"
              wx:for="{{gameHistory}}" 
              wx:key="id"
              bind:tap="viewGameDetail"
              data-game-id="{{item.id}}"
            >
              <view class="game-header">
                <view class="game-title">{{item.gameName}}</view>
                <view class="game-time">{{item.timeAgo}}</view>
              </view>
              
              <view class="game-info">
                <view class="role-info">扮演角色: {{item.role}}</view>
                <view class="result-info">
                  <t-tag 
                    theme="{{item.result === 'win' ? 'success' : item.result === 'lose' ? 'danger' : 'warning'}}" 
                    size="small"
                  >
                    {{item.result === 'win' ? '胜利' : item.result === 'lose' ? '失败' : '平局'}}
                  </t-tag>
                  <text class="influence-change">{{item.influenceChange}}</text>
                </view>
              </view>
              
              <view class="game-tags">
                <t-tag 
                  theme="primary" 
                  size="small" 
                  variant="outline"
                  wx:for="{{item.tags}}" 
                  wx:key="*this"
                  wx:for-item="tag"
                >
                  {{tag}}
                </t-tag>
              </view>
            </view>
          </view>
        </view>
      </t-tab-panel>

      <t-tab-panel value="achievements" label="成就系统">
        <!-- 成就系统 -->
        <view class="achievements-section">
          <view class="section-header">
            <view class="section-title">
              <t-icon name="medal" color="#ffc107" />
              <text>成就系统</text>
            </view>
          </view>

          <view class="achievements-grid">
            <view 
              class="achievement-card {{item.unlocked ? 'unlocked' : 'locked'}}"
              wx:for="{{achievements}}" 
              wx:key="id"
              bind:tap="viewAchievementDetail"
              data-achievement-id="{{item.id}}"
            >
              <view class="achievement-icon">
                <text class="achievement-emoji">{{item.icon}}</text>
              </view>
              <view class="achievement-info">
                <view class="achievement-name">{{item.name}}</view>
                <view class="achievement-desc">{{item.description}}</view>
                <view class="achievement-progress" wx:if="{{!item.unlocked}}">
                  <t-progress 
                    percentage="{{item.progress}}" 
                    theme="primary" 
                    stroke-width="6rpx"
                    show-info="{{false}}"
                  />
                  <text class="progress-text">{{item.current}}/{{item.target}}</text>
                </view>
                <view class="achievement-unlocked" wx:else>
                  <t-icon name="check-circle" color="#52c41a" size="32rpx" />
                  <text>已获得</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </t-tab-panel>

      <t-tab-panel value="friends" label="好友互动">
        <!-- 好友互动记录 -->
        <view class="friends-section">
          <view class="section-header">
            <view class="section-title">
              <t-icon name="user-group" color="#722ed1" />
              <text>好友互动</text>
            </view>
          </view>

          <view class="interaction-list">
            <view 
              class="interaction-item"
              wx:for="{{friendInteractions}}" 
              wx:key="id"
            >
              <view class="friend-avatar">
                <t-avatar 
                  size="medium" 
                  image="{{item.friend.avatar}}"
                  alt="{{item.friend.nickname}}"
                >
                  {{item.friend.nickname.charAt(0)}}
                </t-avatar>
              </view>
              <view class="interaction-content">
                <view class="interaction-header">
                  <view class="friend-name">{{item.friend.nickname}}</view>
                  <view class="interaction-time">{{item.timeAgo}}</view>
                </view>
                <view class="interaction-desc">{{item.description}}</view>
              </view>
              <view class="interaction-type">
                <t-icon 
                  name="{{item.type === 'like' ? 'thumb-up' : item.type === 'invite' ? 'user-add' : 'chat'}}" 
                  color="{{item.type === 'like' ? '#ff6b6b' : item.type === 'invite' ? '#52c41a' : '#1890ff'}}"
                  size="32rpx"
                />
              </view>
            </view>
          </view>
        </view>
      </t-tab-panel>
    </t-tabs>
  </view>
</view>

<!-- 游戏详情弹窗 -->
<t-popup 
  visible="{{showGameDetail}}" 
  placement="bottom"
  bind:visible-change="onGameDetailVisibleChange"
>
  <view class="game-detail-popup">
    <view class="popup-header">
      <view class="popup-title">游戏详情</view>
      <t-icon name="close" size="48rpx" bind:tap="closeGameDetail" />
    </view>
    <view class="game-detail-content" wx:if="{{selectedGame}}">
      <view class="detail-item">
        <text class="detail-label">游戏名称：</text>
        <text class="detail-value">{{selectedGame.gameName}}</text>
      </view>
      <view class="detail-item">
        <text class="detail-label">扮演角色：</text>
        <text class="detail-value">{{selectedGame.role}}</text>
      </view>
      <view class="detail-item">
        <text class="detail-label">游戏时长：</text>
        <text class="detail-value">{{selectedGame.duration}}</text>
      </view>
      <view class="detail-item">
        <text class="detail-label">参与人数：</text>
        <text class="detail-value">{{selectedGame.playerCount}}人</text>
      </view>
      <view class="detail-item">
        <text class="detail-label">游戏结果：</text>
        <t-tag 
          theme="{{selectedGame.result === 'win' ? 'success' : selectedGame.result === 'lose' ? 'danger' : 'warning'}}" 
          size="small"
        >
          {{selectedGame.result === 'win' ? '胜利' : selectedGame.result === 'lose' ? '失败' : '平局'}}
        </t-tag>
      </view>
    </view>
  </view>
</t-popup>

<!-- 提示组件 -->
<t-toast id="t-toast" />
