// 手动测试指南页面
const roomManager = require('../../utils/room-manager');

Page({
  data: {
    currentStep: 0,
    roomId: '',
    testUsers: [],
    steps: [
      {
        title: '步骤1：创建房间',
        description: '创建一个测试房间，确认开始游戏按钮为灰色不可点击',
        action: 'createRoom',
        expected: '房间创建成功，开始游戏按钮不可点击'
      },
      {
        title: '步骤2：添加第一个玩家',
        description: '添加一个模拟玩家，确认仍然不可点击',
        action: 'addPlayer',
        expected: '玩家加入成功，开始游戏按钮仍不可点击'
      },
      {
        title: '步骤3：添加第二个玩家',
        description: '再添加一个模拟玩家，达到3人',
        action: 'addPlayer',
        expected: '玩家加入成功，开始游戏按钮仍不可点击（未全部准备）'
      },
      {
        title: '步骤4：第一个玩家准备',
        description: '让第一个玩家准备',
        action: 'toggleReady',
        expected: '玩家准备成功，开始游戏按钮仍不可点击'
      },
      {
        title: '步骤5：第二个玩家准备',
        description: '让第二个玩家也准备',
        action: 'toggleReady',
        expected: '所有玩家准备完成，开始游戏按钮变为可点击'
      },
      {
        title: '步骤6：进入房间验证',
        description: '进入房间大厅验证功能',
        action: 'goToRoom',
        expected: '成功进入房间，可以看到准备状态和开始游戏按钮'
      }
    ]
  },

  onLoad() {
    console.log('📋 手动测试指南页面加载');
  },

  // 执行当前步骤
  executeStep() {
    const step = this.data.steps[this.data.currentStep];
    if (!step) return;

    try {
      switch (step.action) {
        case 'createRoom':
          this.createTestRoom();
          break;
        case 'addPlayer':
          this.addTestPlayer();
          break;
        case 'toggleReady':
          this.togglePlayerReady();
          break;
        case 'goToRoom':
          this.goToTestRoom();
          break;
      }
    } catch (error) {
      wx.showToast({
        title: error.message || '操作失败',
        icon: 'error'
      });
    }
  },

  // 创建测试房间
  createTestRoom() {
    const result = roomManager.createRoom({
      gameMode: '经典推理模式',
      maxPlayers: 6,
      timeLimit: 600,
      rounds: 3
    });

    this.setData({
      roomId: result.roomId
    });

    wx.showToast({
      title: '房间创建成功',
      icon: 'success'
    });

    console.log('✅ 测试房间创建:', result.roomId);
    this.nextStep();
  },

  // 添加测试玩家
  addTestPlayer() {
    if (!this.data.roomId) {
      throw new Error('请先创建房间');
    }

    const playerIndex = this.data.testUsers.length + 1;
    const testUser = {
      id: `test_user_${playerIndex}`,
      nickname: `测试玩家${playerIndex}`,
      avatar: ''
    };

    roomManager.joinRoom(this.data.roomId, testUser);
    
    const testUsers = [...this.data.testUsers, testUser];
    this.setData({ testUsers });

    wx.showToast({
      title: `玩家${playerIndex}加入成功`,
      icon: 'success'
    });

    console.log('✅ 测试玩家加入:', testUser.nickname);
    this.nextStep();
  },

  // 切换玩家准备状态
  togglePlayerReady() {
    if (!this.data.roomId || this.data.testUsers.length === 0) {
      throw new Error('没有可操作的玩家');
    }

    // 找到第一个未准备的玩家
    const room = roomManager.getRoomInfo(this.data.roomId);
    const unreadyPlayer = this.data.testUsers.find(user => {
      const player = room.players.get(user.id);
      return player && !player.isReady;
    });

    if (!unreadyPlayer) {
      throw new Error('所有玩家都已准备');
    }

    roomManager.togglePlayerReady(this.data.roomId, unreadyPlayer.id);

    wx.showToast({
      title: `${unreadyPlayer.nickname}已准备`,
      icon: 'success'
    });

    console.log('✅ 玩家准备:', unreadyPlayer.nickname);
    this.nextStep();
  },

  // 进入测试房间
  goToTestRoom() {
    if (!this.data.roomId) {
      throw new Error('请先创建房间');
    }

    wx.navigateTo({
      url: `/pages/room-lobby/room-lobby?roomId=${this.data.roomId}`,
      success: () => {
        console.log('✅ 进入测试房间');
      }
    });
  },

  // 下一步
  nextStep() {
    const nextStep = this.data.currentStep + 1;
    if (nextStep < this.data.steps.length) {
      this.setData({ currentStep: nextStep });
    }
  },

  // 上一步
  prevStep() {
    const prevStep = this.data.currentStep - 1;
    if (prevStep >= 0) {
      this.setData({ currentStep: prevStep });
    }
  },

  // 重置测试
  resetTest() {
    this.setData({
      currentStep: 0,
      roomId: '',
      testUsers: []
    });

    wx.showToast({
      title: '测试已重置',
      icon: 'success'
    });
  },

  // 显示房间状态
  showRoomStatus() {
    if (!this.data.roomId) {
      wx.showToast({
        title: '请先创建房间',
        icon: 'none'
      });
      return;
    }

    try {
      const room = roomManager.getRoomInfo(this.data.roomId);
      const canStart = roomManager.canStartGame(this.data.roomId);
      
      const playerList = Array.from(room.players.values()).map(p => 
        `${p.nickname}(${p.isReady ? '已准备' : '未准备'})`
      ).join('\n');

      wx.showModal({
        title: '房间状态',
        content: `房间ID: ${room.id}\n玩家数量: ${room.players.size}\n可以开始: ${canStart ? '是' : '否'}\n\n玩家列表:\n${playerList}`,
        showCancel: false
      });
    } catch (error) {
      wx.showToast({
        title: error.message || '获取状态失败',
        icon: 'error'
      });
    }
  }
});
