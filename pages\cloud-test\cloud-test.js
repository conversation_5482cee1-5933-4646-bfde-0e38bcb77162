// pages/cloud-test/cloud-test.js
const CloudAIService = require('../../utils/cloud-ai-service');

Page({
  data: {
    cloudStatus: {
      cloudEnabled: false,
      hasWxCloud: false,
      currentMode: 'unknown'
    },
    testResults: {
      cloudService: null,
      mockService: null,
      recommendation: 'unknown'
    },
    isLoading: false,
    testHistory: []
  },

  onLoad() {
    console.log('☁️ 云开发测试页面加载');
    this.cloudAIService = new CloudAIService();
    this.checkCloudStatus();
  },

  onShow() {
    this.loadTestHistory();
  },

  /**
   * 检查云开发状态
   */
  checkCloudStatus() {
    const status = this.cloudAIService.getServiceStatus();
    this.setData({
      cloudStatus: status
    });
    
    console.log('☁️ 云开发状态:', status);
  },

  /**
   * 测试云开发连接
   */
  async testCloudConnection() {
    this.setData({ isLoading: true });
    
    try {
      console.log('🔍 开始测试云开发连接...');
      
      const results = await this.cloudAIService.testConnection();
      
      this.setData({
        testResults: results,
        isLoading: false
      });
      
      // 保存测试记录
      this.saveTestRecord('连接测试', results);
      
      // 显示结果
      const message = results.cloudService?.available 
        ? '☁️ 云AI服务连接成功！' 
        : `🎭 云AI服务不可用，使用模拟服务\n原因: ${results.cloudService?.message}`;
        
      wx.showModal({
        title: '测试结果',
        content: message,
        showCancel: false
      });
      
    } catch (error) {
      console.error('❌ 测试连接失败:', error);
      
      this.setData({ isLoading: false });
      
      wx.showModal({
        title: '测试失败',
        content: `连接测试失败: ${error.message}`,
        showCancel: false
      });
    }
  },

  /**
   * 测试剧本生成
   */
  async testScriptGeneration() {
    this.setData({ isLoading: true });
    
    try {
      console.log('📝 测试剧本生成...');
      
      const params = {
        theme: '推理悬疑',
        playerCount: 4,
        difficulty: '中等'
      };
      
      const result = await this.cloudAIService.generateScript(params);
      
      this.setData({ isLoading: false });
      
      // 保存测试记录
      this.saveTestRecord('剧本生成', { success: true, data: result });
      
      wx.showModal({
        title: '剧本生成成功',
        content: `标题: ${result.title}\n背景: ${result.background?.substring(0, 50)}...`,
        showCancel: false
      });
      
    } catch (error) {
      console.error('❌ 剧本生成失败:', error);
      
      this.setData({ isLoading: false });
      
      wx.showModal({
        title: '生成失败',
        content: `剧本生成失败: ${error.message}`,
        showCancel: false
      });
    }
  },

  /**
   * 测试角色生成
   */
  async testCharacterGeneration() {
    this.setData({ isLoading: true });
    
    try {
      console.log('👤 测试角色生成...');
      
      const params = {
        theme: '古代宫廷',
        characterType: '主角',
        background: '宫廷斗争背景下的推理游戏'
      };
      
      const result = await this.cloudAIService.generateCharacter(params);
      
      this.setData({ isLoading: false });
      
      // 保存测试记录
      this.saveTestRecord('角色生成', { success: true, data: result });
      
      wx.showModal({
        title: '角色生成成功',
        content: `姓名: ${result.name}\n身份: ${result.identity}\n特点: ${result.personality?.substring(0, 30)}...`,
        showCancel: false
      });
      
    } catch (error) {
      console.error('❌ 角色生成失败:', error);
      
      this.setData({ isLoading: false });
      
      wx.showModal({
        title: '生成失败',
        content: `角色生成失败: ${error.message}`,
        showCancel: false
      });
    }
  },

  /**
   * 强制使用模拟服务
   */
  forceUseMockService() {
    this.cloudAIService.forceUseMockService();
    this.checkCloudStatus();
    
    wx.showToast({
      title: '已切换到模拟服务',
      icon: 'success'
    });
  },

  /**
   * 尝试启用云服务
   */
  tryEnableCloudService() {
    this.cloudAIService.tryEnableCloudService();
    this.checkCloudStatus();
    
    const status = this.cloudAIService.getServiceStatus();
    const message = status.cloudEnabled ? '云服务已启用' : '云服务不可用';
    
    wx.showToast({
      title: message,
      icon: status.cloudEnabled ? 'success' : 'none'
    });
  },

  /**
   * 查看云开发控制台
   */
  openCloudConsole() {
    wx.showModal({
      title: '云开发控制台',
      content: '请在微信开发者工具中点击"云开发"按钮，或访问腾讯云控制台查看云开发服务状态。',
      showCancel: false
    });
  },

  /**
   * 查看设置指南
   */
  viewSetupGuide() {
    wx.showModal({
      title: '设置指南',
      content: '请查看项目中的 docs/cloud-setup-guide.md 文件，了解详细的云开发设置步骤。',
      showCancel: false
    });
  },

  /**
   * 保存测试记录
   */
  saveTestRecord(type, result) {
    const record = {
      id: Date.now(),
      type: type,
      timestamp: new Date().toLocaleString(),
      success: result.success !== false,
      details: result
    };
    
    const history = this.data.testHistory;
    history.unshift(record);
    
    // 只保留最近10条记录
    if (history.length > 10) {
      history.splice(10);
    }
    
    this.setData({ testHistory: history });
    
    // 保存到本地存储
    wx.setStorageSync('cloudTestHistory', history);
  },

  /**
   * 加载测试历史
   */
  loadTestHistory() {
    try {
      const history = wx.getStorageSync('cloudTestHistory') || [];
      this.setData({ testHistory: history });
    } catch (error) {
      console.error('加载测试历史失败:', error);
    }
  },

  /**
   * 清除测试历史
   */
  clearTestHistory() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有测试记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({ testHistory: [] });
          wx.removeStorageSync('cloudTestHistory');
          wx.showToast({
            title: '已清除',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 查看测试详情
   */
  viewTestDetails(e) {
    const index = e.currentTarget.dataset.index;
    const record = this.data.testHistory[index];
    
    wx.showModal({
      title: `${record.type} - ${record.timestamp}`,
      content: JSON.stringify(record.details, null, 2),
      showCancel: false
    });
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  }
});
