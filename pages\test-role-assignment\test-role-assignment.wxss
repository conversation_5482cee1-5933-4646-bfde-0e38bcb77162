/* 测试角色分配功能页面样式 */
.container {
  padding: 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 测试进度 */
.progress-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
}

.progress-title {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  text-align: center;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.step-item {
  display: flex;
  align-items: center;
  padding: 15rpx;
  border-radius: 15rpx;
  background: rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.step-item.completed {
  background: rgba(76, 175, 80, 0.2);
  border-left: 6rpx solid #4CAF50;
}

.step-number {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 24rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.step-item.completed .step-number {
  background: #4CAF50;
}

.step-text {
  flex: 1;
  color: white;
  font-size: 28rpx;
}

.step-status {
  font-size: 32rpx;
}

/* 控制按钮 */
.controls {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.control-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

.control-btn.primary {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.control-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

/* 测试结果 */
.results-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
}

.result-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 15rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15rpx;
}

.result-item:last-child {
  margin-bottom: 0;
}

.result-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
  margin-right: 15rpx;
  min-width: 150rpx;
}

.result-value {
  flex: 1;
  color: white;
  font-size: 28rpx;
  font-weight: bold;
}

.detail-btn {
  width: 120rpx;
  height: 50rpx;
  border-radius: 25rpx;
  background: rgba(103, 126, 234, 0.8);
  color: white;
  font-size: 24rpx;
  border: none;
  margin-left: 15rpx;
}

/* 操作按钮 */
.actions {
  text-align: center;
  margin-bottom: 30rpx;
}

.action-btn {
  width: 400rpx;
  height: 88rpx;
  border-radius: 44rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

/* 测试说明 */
.test-info {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  backdrop-filter: blur(10rpx);
}

.info-title {
  display: block;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.info-item {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
  line-height: 1.4;
}
