<import src="../common/template/icon.wxml"/><wxs src="../common/utils.wxs" module="_"/><wxs src="./input.wxs" module="_this"/><view style="{{_._style([style, customStyle])}}" class="{{_.cls(classPrefix, [['border', !borderless]])}} {{classPrefix}}--layout-{{layout}} class {{prefix}}-class" aria-describedby><view class="{{classPrefix}}__wrap--prefix"><view class="{{classPrefix}}__icon--prefix"><slot name="prefix-icon"/><template wx:if="{{_prefixIcon}}" is="icon" data="{{tClass: prefix + '-class-prefix-icon', ariaHidden: true, ..._prefixIcon}}"/></view><view class="{{classPrefix}}__label {{prefix}}-class-label" aria-hidden><slot name="label"/><block wx:if="{{label}}">{{label}}</block></view></view><view class="{{classPrefix}}__wrap"><view class="{{classPrefix}}__content {{classPrefix}}--{{status}}"><input class="{{_this.getInputClass(classPrefix, suffix, align, disabled)}} {{prefix}}-class-input" maxlength="{{allowInputOverMax ? -1 : maxlength}}" disabled="{{disabled || readonly}}" placeholder="{{placeholder}}" placeholder-style="{{placeholderStyle}}" placeholder-class="{{_.cls(classPrefix + '__placeholder',  [['disabled', disabled]])}} {{placeholderClass}}" value="{{value}}" password="{{type === 'password'}}" type="{{type === 'password' ? 'text' : type}}" focus="{{focus}}" confirm-type="{{confirmType}}" confirm-hold="{{confirmHold}}" cursor="{{cursor}}" cursor-color="{{cursorColor}}" cursor-spacing="{{cursorSpacing}}" adjust-position="{{adjustPosition}}" auto-focus="{{autoFocus}}" always-embed="{{alwaysEmbed}}" selection-start="{{selectionStart}}" selection-end="{{selectionEnd}}" hold-keyboard="{{holdKeyboard}}" safe-password-cert-path="{{safePasswordCertPath}}" safe-password-length="{{safePasswordLength}}" safe-password-time-stamp="{{safePasswordTimeStamp}}" safe-password-nonce="{{safePasswordNonce}}" safe-password-salt="{{safePasswordSalt}}" safe-password-custom-hash="{{safePasswordCustomHash}}" aria-role="textbox" aria-label="{{label}}" aria-roledescription="{{label}}" bindinput="onInput" bindfocus="onFocus" bindblur="onBlur" bindconfirm="onConfirm" bindkeyboardheightchange="onKeyboardHeightChange" bindnicknamereview="onNickNameReview"/><view wx:if="{{_clearIcon && value.length > 0 && showClearIcon}}" class="{{classPrefix}}__wrap--clearable-icon" bind:tap="clearInput"><template is="icon" data="{{tClass: prefix + '-class-clearable', ariaRole: 'button', ariaLabel: '清除', ..._clearIcon }}"/></view><view class="{{classPrefix}}__wrap--suffix {{prefix}}-class-suffix" bind:tap="onSuffixClick"><text wx:if="{{suffix}}">{{suffix}}</text><slot name="suffix"/></view><view class="{{classPrefix}}__wrap--suffix-icon" bind:tap="onSuffixIconClick"><slot name="suffix-icon"/><template wx:if="{{_suffixIcon}}" is="icon" data="{{tClass: prefix + '-class-suffix-icon', ariaRole: 'button', ..._suffixIcon }}"/></view></view><view wx:if="{{tips && tips.length > 0}}" class="{{classPrefix}}__tips {{classPrefix}}--{{align}} {{prefix}}-class-tips">{{tips}}</view><slot name="tips"/></view><slot name="extra"/></view>