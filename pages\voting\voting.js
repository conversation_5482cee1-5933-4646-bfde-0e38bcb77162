// 投票页面逻辑
const api = require('../../utils/api');
const common = require('../../utils/common');
const errorHandler = require('../../utils/error-handler');

Page({
  data: {
    roomId: '',
    currentRound: 1,
    timeLeft: 120, // 2分钟投票时间
    selectedOption: null,
    selectedOptionInfo: null,
    voteReason: '',
    hasVoted: false,
    submitting: false,
    showVoteCount: false,
    allowSkip: true,
    showVoteConfirm: false,
    showVoteResult: false,
    
    // 投票阶段信息
    votingPhase: {
      title: '淘汰投票',
      description: '根据讨论结果，投票选择你认为最可疑的玩家'
    },
    
    // 投票选项（玩家列表）
    votingOptions: [
      {
        id: 'player_001',
        name: '艾米丽·哈特',
        role: '庄园女主人',
        playerName: '玩家1',
        avatar: '',
        isEliminated: false,
        suspicion: 85,
        voteCount: 0
      },
      {
        id: 'player_002',
        name: '詹姆斯·布莱克',
        role: '私家侦探',
        playerName: '玩家2',
        avatar: '',
        isEliminated: false,
        suspicion: 45,
        voteCount: 0
      },
      {
        id: 'player_003',
        name: '维多利亚·格林',
        role: '艺术收藏家',
        playerName: '玩家3',
        avatar: '',
        isEliminated: true,
        suspicionLevel: '低',
        voteCount: 0
      },
      {
        id: 'player_004',
        name: '查尔斯·怀特',
        role: '律师',
        playerName: '玩家4',
        avatar: '',
        isEliminated: false,
        suspicionLevel: '中',
        voteCount: 0
      }
    ],
    
    // 投票统计
    voteStats: [],
    
    // 投票结果
    voteResult: null
  },

  onLoad(options) {
    console.log('投票页面加载', options);

    if (options.roomId) {
      this.setData({ roomId: options.roomId });
    }

    if (options.round) {
      this.setData({ currentRound: parseInt(options.round) });
    }

    // 启动计时器
    this.startTimer();

    // 初始化投票统计
    this.initVoteStats();

    // 模拟实时投票更新
    this.simulateRealTimeVoting();
  },

  onUnload() {
    // 清理计时器
    if (this.timer) {
      clearInterval(this.timer);
    }
    if (this.voteUpdateTimer) {
      clearInterval(this.voteUpdateTimer);
    }
  },

  // 返回上一页
  goBack() {
    wx.showModal({
      title: '确认退出',
      content: '退出投票将视为弃权，确定要退出吗？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  },

  // 启动计时器
  startTimer() {
    this.timer = setInterval(() => {
      const timeLeft = this.data.timeLeft - 1;
      this.setData({ timeLeft });
      
      if (timeLeft <= 0) {
        clearInterval(this.timer);
        this.handleTimeUp();
      }
      
      // 最后30秒显示投票统计
      if (timeLeft <= 30 && !this.data.showVoteCount) {
        this.setData({ showVoteCount: true });
      }
    }, 1000);
  },

  // 时间到处理
  handleTimeUp() {
    if (!this.data.hasVoted) {
      wx.showModal({
        title: '投票时间结束',
        content: '时间已到，将自动弃权',
        showCancel: false,
        success: () => {
          this.showVoteResult();
        }
      });
    } else {
      this.showVoteResult();
    }
  },

  // 格式化时间显示
  formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  },

  // 选择投票选项
  selectOption(e) {
    if (this.data.hasVoted) {
      wx.showToast({
        title: '你已经投过票了',
        icon: 'none'
      });
      return;
    }

    const optionId = e.currentTarget.dataset.optionId;
    const option = this.data.votingOptions.find(o => o.id === optionId);

    if (option && option.isEliminated) {
      wx.showToast({
        title: '该玩家已被淘汰，无法投票',
        icon: 'none'
      });
      return;
    }

    this.setData({
      selectedOption: optionId,
      selectedOptionInfo: option
    });

    // 添加触觉反馈
    wx.vibrateShort();
  },

  // 投票理由变化
  onReasonChange(e) {
    this.setData({ voteReason: e.detail.value });
  },

  // 提交投票
  submitVote() {
    if (!this.data.selectedOption) {
      wx.showToast({
        title: '请选择投票对象',
        icon: 'none'
      });
      return;
    }

    this.confirmVote();
  },

  // 关闭投票确认弹窗
  closeVoteConfirm() {
    this.setData({ showVoteConfirm: false });
  },

  // 确认投票
  async confirmVote() {
    try {
      this.setData({ 
        submitting: true,
        showVoteConfirm: false 
      });

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // 这里应该调用实际的API
      // await api.submitVote(this.data.roomId, {
      //   targetId: this.data.selectedOption,
      //   reason: this.data.voteReason
      // });
      
      this.setData({ 
        hasVoted: true,
        submitting: false 
      });
      
      // 更新投票统计
      this.updateVoteCount(this.data.selectedOption);
      
      // 添加触觉反馈
      wx.vibrateShort();
      
      wx.showToast({
        title: '投票成功！',
        icon: 'success'
      });

      // 3秒后显示投票结果
      setTimeout(() => {
        this.showVoteResult();
      }, 3000);

    } catch (error) {
      console.error('投票失败:', error);
      wx.showToast({
        title: '投票失败，请重试',
        icon: 'error'
      });
      this.setData({ submitting: false });
    }
  },

  // 弃权投票
  skipVote() {
    wx.showModal({
      title: '确认弃权',
      content: '确定要弃权本轮投票吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({ hasVoted: true });
          wx.showToast({
            title: '已弃权',
            icon: 'success'
          });
        }
      }
    });
  },

  // 获取选中选项名称
  getSelectedOptionName() {
    if (!this.data.selectedOption) return '';
    const option = this.data.votingOptions.find(o => o.id === this.data.selectedOption);
    return option ? option.name : '';
  },

  // 初始化投票统计
  initVoteStats() {
    const voteStats = this.data.votingOptions
      .filter(option => !option.isEliminated)
      .map(option => ({
        id: option.id,
        name: option.name,
        count: 0,
        percentage: 0,
        color: this.getRandomColor()
      }));
    
    this.setData({ voteStats });
  },

  // 获取随机颜色
  getRandomColor() {
    const colors = ['#667eea', '#764ba2', '#52c41a', '#ffc107', '#ff6b6b', '#13c2c2'];
    return colors[Math.floor(Math.random() * colors.length)];
  },

  // 更新投票统计
  updateVoteCount(targetId) {
    const votingOptions = this.data.votingOptions.map(option => {
      if (option.id === targetId) {
        return { ...option, voteCount: (option.voteCount || 0) + 1 };
      }
      return option;
    });
    
    // 更新统计数据
    const totalVotes = votingOptions.reduce((sum, option) => sum + (option.voteCount || 0), 0);
    const voteStats = this.data.voteStats.map(stat => {
      const option = votingOptions.find(o => o.id === stat.id);
      const count = option ? (option.voteCount || 0) : 0;
      const percentage = totalVotes > 0 ? (count / totalVotes) * 100 : 0;
      
      return {
        ...stat,
        count,
        percentage: Math.round(percentage)
      };
    });
    
    this.setData({ votingOptions, voteStats });
  },

  // 模拟实时投票更新
  simulateRealTimeVoting() {
    this.voteUpdateTimer = setInterval(() => {
      if (this.data.hasVoted && this.data.showVoteCount) {
        // 随机更新其他玩家的投票
        const randomOption = this.data.votingOptions[Math.floor(Math.random() * this.data.votingOptions.length)];
        if (!randomOption.isEliminated && Math.random() > 0.7) {
          this.updateVoteCount(randomOption.id);
        }
      }
    }, 3000);
  },

  // 显示投票结果
  showVoteResult() {
    // 模拟投票结果
    const mockResult = {
      title: '投票结果公布',
      description: '根据投票结果，以下玩家将被淘汰',
      details: [
        { id: 'player_001', name: '艾米丽·哈特', votes: 3, avatar: '' },
        { id: 'player_002', name: '詹姆斯·布莱克', votes: 1, avatar: '' },
        { id: 'player_004', name: '查尔斯·怀特', votes: 0, avatar: '' }
      ],
      eliminatedPlayer: {
        id: 'player_001',
        name: '艾米丽·哈特',
        avatar: ''
      }
    };
    
    this.setData({ 
      voteResult: mockResult,
      showVoteResult: true 
    });
  },

  // 关闭投票结果
  closeVoteResult() {
    this.setData({ showVoteResult: false });
  },

  // 继续游戏
  continueGame() {
    this.setData({ showVoteResult: false });

    // 根据当前轮数决定下一步流程
    if (this.data.currentRound === 1) {
      // 第一轮投票后，进入你画我猜小游戏
      wx.redirectTo({
        url: `/pages/mini-game/mini-game?roomId=${this.data.roomId}&round=${this.data.currentRound}`
      });
    } else if (this.data.currentRound === 2) {
      // 第二轮投票后，进入真心话环节
      wx.redirectTo({
        url: `/pages/truth-dare/truth-dare?roomId=${this.data.roomId}`
      });
    } else if (this.data.currentRound >= 3) {
      // 第三轮投票后，直接进入游戏结果
      wx.redirectTo({
        url: `/pages/game-result/game-result?roomId=${this.data.roomId}`
      });
    } else {
      // 其他情况继续下一轮讨论
      wx.redirectTo({
        url: `/pages/discussion/discussion?roomId=${this.data.roomId}&round=${this.data.currentRound + 1}`
      });
    }
  },

  // 显示投票统计
  showVoteStats() {
    const stats = this.data.votingOptions
      .filter(option => option.voteCount > 0)
      .map(option => `${option.name}: ${option.voteCount}票`)
      .join('\n');

    wx.showModal({
      title: '投票统计',
      content: stats || '暂无投票数据',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 测试跳转 - 直接显示投票结果
  testJump() {
    console.log('测试跳转，当前轮数:', this.data.currentRound);
    this.showVoteResult();
  }
});
