// test/api-key-validator.js
// API密钥验证工具

/**
 * API密钥验证器
 */
class ApiKeyValidator {
  constructor() {
    this.expectedKey = 'sk-rFun7AywY7jUUdJAtUBbFDxhaVTI5okdFpL3mSSeLfOmKCmP';
    this.baseUrl = 'https://api.moonshot.cn/v1';
  }

  /**
   * 验证API密钥格式
   */
  validateKeyFormat(apiKey) {
    const results = {
      isValid: true,
      issues: []
    };

    // 检查是否为空
    if (!apiKey) {
      results.isValid = false;
      results.issues.push('API密钥为空');
      return results;
    }

    // 检查前缀
    if (!apiKey.startsWith('sk-')) {
      results.isValid = false;
      results.issues.push('API密钥应以 "sk-" 开头');
    }

    // 检查长度
    if (apiKey.length !== 51) {
      results.isValid = false;
      results.issues.push(`API密钥长度错误，期望51个字符，实际${apiKey.length}个字符`);
    }

    // 检查字符集
    const validChars = /^sk-[A-Za-z0-9]+$/;
    if (!validChars.test(apiKey)) {
      results.isValid = false;
      results.issues.push('API密钥包含无效字符');
    }

    // 与期望密钥比较
    if (apiKey !== this.expectedKey) {
      results.isValid = false;
      results.issues.push('API密钥与PRD文档中的密钥不匹配');
    }

    return results;
  }

  /**
   * 测试API密钥连接
   */
  async testApiKey(apiKey) {
    return new Promise((resolve) => {
      console.log('🔑 测试API密钥连接...');
      
      wx.request({
        url: `${this.baseUrl}/chat/completions`,
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        data: {
          model: 'kimi-k2-0711-preview',
          messages: [
            {
              role: 'user',
              content: 'Hello, this is a test message.'
            }
          ],
          max_tokens: 10
        },
        timeout: 15000,
        success: (res) => {
          console.log('API响应:', res);
          
          const result = {
            success: false,
            statusCode: res.statusCode,
            message: '',
            details: res.data
          };

          if (res.statusCode === 200) {
            result.success = true;
            result.message = 'API密钥验证成功';
          } else if (res.statusCode === 401) {
            result.message = 'API密钥无效或已过期';
          } else if (res.statusCode === 403) {
            result.message = 'API密钥权限不足';
          } else if (res.statusCode === 429) {
            result.message = 'API请求频率限制';
          } else {
            result.message = `API请求失败，状态码: ${res.statusCode}`;
          }

          resolve(result);
        },
        fail: (error) => {
          console.log('API请求失败:', error);
          
          resolve({
            success: false,
            statusCode: 0,
            message: `网络请求失败: ${error.errMsg}`,
            details: error
          });
        }
      });
    });
  }

  /**
   * 运行完整的API密钥验证
   */
  async runFullValidation(apiKey) {
    console.log('🚀 开始API密钥完整验证...\n');
    
    const results = {
      formatValidation: this.validateKeyFormat(apiKey),
      connectionTest: await this.testApiKey(apiKey),
      timestamp: new Date().toISOString()
    };

    console.log('📊 验证结果总结:');
    console.log('================');
    
    // 格式验证结果
    console.log('\n1. 格式验证:');
    if (results.formatValidation.isValid) {
      console.log('   ✅ API密钥格式正确');
    } else {
      console.log('   ❌ API密钥格式有问题:');
      results.formatValidation.issues.forEach(issue => {
        console.log(`      - ${issue}`);
      });
    }

    // 连接测试结果
    console.log('\n2. 连接测试:');
    if (results.connectionTest.success) {
      console.log('   ✅ API密钥连接成功');
      console.log(`   状态码: ${results.connectionTest.statusCode}`);
    } else {
      console.log('   ❌ API密钥连接失败');
      console.log(`   状态码: ${results.connectionTest.statusCode}`);
      console.log(`   错误信息: ${results.connectionTest.message}`);
    }

    // 生成建议
    console.log('\n💡 建议:');
    if (results.formatValidation.isValid && results.connectionTest.success) {
      console.log('   🎉 API密钥完全正常，可以正常使用AI功能！');
    } else if (!results.formatValidation.isValid) {
      console.log('   ⚠️  请检查API密钥格式:');
      console.log('   1. 确保密钥以 "sk-" 开头');
      console.log('   2. 确保密钥长度为51个字符');
      console.log('   3. 确保密钥与PRD文档中的密钥一致');
    } else if (results.connectionTest.statusCode === 401) {
      console.log('   ⚠️  API密钥认证失败:');
      console.log('   1. 检查密钥是否正确复制');
      console.log('   2. 确认密钥是否已过期');
      console.log('   3. 联系API提供商确认密钥状态');
    } else if (results.connectionTest.statusCode === 403) {
      console.log('   ⚠️  API密钥权限不足:');
      console.log('   1. 确认密钥是否有chat/completions权限');
      console.log('   2. 检查API配额是否已用完');
    } else {
      console.log('   ⚠️  其他问题，请检查网络连接和API服务状态');
    }

    return results;
  }

  /**
   * 比较两个API密钥
   */
  compareKeys(key1, key2) {
    console.log('🔍 比较API密钥...');
    console.log(`密钥1: ${key1}`);
    console.log(`密钥2: ${key2}`);
    console.log(`是否相同: ${key1 === key2 ? '✅' : '❌'}`);
    
    if (key1 !== key2) {
      // 找出不同的位置
      const differences = [];
      const maxLength = Math.max(key1.length, key2.length);
      
      for (let i = 0; i < maxLength; i++) {
        if (key1[i] !== key2[i]) {
          differences.push({
            position: i,
            key1Char: key1[i] || '(缺失)',
            key2Char: key2[i] || '(缺失)'
          });
        }
      }
      
      console.log('差异位置:');
      differences.slice(0, 5).forEach(diff => {
        console.log(`  位置${diff.position}: "${diff.key1Char}" vs "${diff.key2Char}"`);
      });
      
      if (differences.length > 5) {
        console.log(`  ... 还有${differences.length - 5}个差异`);
      }
    }
  }
}

module.exports = ApiKeyValidator;
