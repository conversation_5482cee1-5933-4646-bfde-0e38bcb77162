/* pages/cloud-debug/cloud-debug.wxss */

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  color: white;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
}

.section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #eee;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-family: monospace;
}

.env-list {
  flex: 1;
}

.env-item {
  display: block;
  font-size: 24rpx;
  color: #007aff;
  background: #f0f8ff;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  margin-bottom: 8rpx;
  font-family: monospace;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.btn {
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: bold;
  border: none;
}

.btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn.secondary {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #8b4513;
}

.btn.outline {
  background: transparent;
  color: #666;
  border: 2rpx solid #ddd;
}

.btn[disabled] {
  opacity: 0.5;
}

.empty-state {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #999;
  font-size: 28rpx;
}

.results-list {
  max-height: 800rpx;
  overflow-y: auto;
}

.result-item {
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  background: #fafafa;
  transition: all 0.3s ease;
}

.result-item:active {
  background: #f0f0f0;
  transform: scale(0.98);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.result-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.result-time {
  font-size: 24rpx;
  color: #999;
}

.result-message {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  line-height: 1.4;
}

.result-data {
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 15rpx;
  font-family: monospace;
  font-size: 22rpx;
  color: #555;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 300rpx;
  overflow-y: auto;
}

.help-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.help-text text {
  display: block;
  margin-bottom: 10rpx;
  padding-left: 20rpx;
  position: relative;
}

.help-text text::before {
  content: "•";
  position: absolute;
  left: 0;
  color: #007aff;
  font-weight: bold;
}
