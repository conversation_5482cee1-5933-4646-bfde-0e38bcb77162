// 测试房间功能页面
const roomManager = require('../../utils/room-manager');

Page({
  data: {
    roomId: '',
    roomInfo: null,
    currentUser: null
  },

  onLoad() {
    console.log('🧪 测试页面加载');
    
    // 初始化用户
    const user = roomManager.initUser();
    console.log('✅ 当前用户:', user);
    
    this.setData({
      currentUser: user
    });
  },

  // 创建测试房间
  createTestRoom() {
    try {
      const result = roomManager.createRoom({
        gameMode: '经典推理模式',
        maxPlayers: 6,
        timeLimit: 600,
        rounds: 3
      });

      console.log('✅ 测试房间创建成功:', result);
      
      this.setData({
        roomId: result.roomId,
        roomInfo: result.room
      });

      wx.showToast({
        title: '房间创建成功',
        icon: 'success'
      });

    } catch (error) {
      console.error('❌ 创建测试房间失败:', error);
      wx.showToast({
        title: '创建失败',
        icon: 'error'
      });
    }
  },

  // 跳转到房间大厅
  goToRoomLobby() {
    if (!this.data.roomId) {
      wx.showToast({
        title: '请先创建房间',
        icon: 'error'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/room-lobby/room-lobby?roomId=${this.data.roomId}`
    });
  },

  // 添加模拟玩家
  addMockPlayer() {
    if (!this.data.roomId) {
      wx.showToast({
        title: '请先创建房间',
        icon: 'error'
      });
      return;
    }

    try {
      const mockPlayer = {
        id: `mock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        nickname: `模拟玩家${Math.floor(Math.random() * 100)}`,
        avatar: ''
      };

      const room = roomManager.joinRoom(this.data.roomId, mockPlayer);
      
      this.setData({
        roomInfo: room
      });

      wx.showToast({
        title: '模拟玩家加入成功',
        icon: 'success'
      });

      console.log('✅ 模拟玩家加入成功:', mockPlayer.nickname);

    } catch (error) {
      console.error('❌ 添加模拟玩家失败:', error);
      wx.showToast({
        title: error.message || '添加失败',
        icon: 'error'
      });
    }
  },

  // 显示房间信息
  showRoomInfo() {
    if (!this.data.roomInfo) {
      wx.showToast({
        title: '暂无房间信息',
        icon: 'none'
      });
      return;
    }

    const room = this.data.roomInfo;
    const playerList = Array.from(room.players.values()).map(p => p.nickname).join(', ');
    
    wx.showModal({
      title: '房间信息',
      content: `房间ID: ${room.id}\n游戏模式: ${room.gameMode}\n玩家数量: ${room.players.size}/${room.maxPlayers}\n玩家列表: ${playerList}`,
      showCancel: false
    });
  }
});
