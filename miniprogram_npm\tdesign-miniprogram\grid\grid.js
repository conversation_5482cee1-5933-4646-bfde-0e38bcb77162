import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import{isObject}from"../common/validator";import props from"./props";const{prefix:prefix}=config,name=`${prefix}-grid`;let Grid=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=["t-class"],this.relations={"../grid-item/grid-item":{type:"descendant"}},this.properties=props,this.data={prefix:prefix,classPrefix:name,contentStyle:""},this.observers={"column,hover,align,gutter,border"(){this.updateContentStyle(),this.doForChild(t=>t.updateStyle())}},this.lifetimes={attached(){this.updateContentStyle()}},this.methods={doForChild(t){this.$children.forEach(t)},updateContentStyle(){const t=[],e=this.getContentMargin();e&&t.push(e),this.setData({contentStyle:t.join(";")})},getContentMargin(){const{gutter:t}=this.properties;let{border:e}=this.properties;if(!e)return`margin-bottom:-${t}rpx; margin-right:-${t}rpx`;isObject(e)||(e={});const{width:r=2}=e;return`margin-bottom:-${r}rpx; margin-right:-${r}rpx`}}}};Grid=__decorate([wxComponent()],Grid);export default Grid;