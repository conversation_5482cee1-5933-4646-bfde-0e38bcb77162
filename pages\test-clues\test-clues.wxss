/* 线索收集测试页面样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  padding: 40rpx;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  display: block;
  color: #fff;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  color: #ccc;
  font-size: 28rpx;
}

.test-buttons {
  margin-bottom: 60rpx;
}

.test-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
}

.test-button:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.15);
}

.button-content {
  flex: 1;
}

.button-name {
  display: block;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.button-desc {
  display: block;
  color: #ccc;
  font-size: 26rpx;
  line-height: 1.4;
}

.button-arrow {
  color: #ffd700;
  font-size: 36rpx;
  font-weight: bold;
}

.info-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 30rpx;
}

.info-title {
  display: block;
  color: #ffd700;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.info-item {
  color: #e0e0e0;
  font-size: 26rpx;
  line-height: 1.5;
}
