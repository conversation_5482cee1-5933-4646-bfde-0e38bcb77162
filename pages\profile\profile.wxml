<!--个人中心页面-->
<view class="profile-container">
  <!-- 用户信息头部 -->
  <view class="profile-header">
    <view class="user-info">
      <t-avatar 
        size="large" 
        image="{{userInfo.avatar}}"
        alt="{{userInfo.nickname}}"
        class="user-avatar"
      >
        {{userInfo.nickname.charAt(0)}}
      </t-avatar>
      <view class="user-details">
        <view class="user-name">{{userInfo.nickname}}</view>
        <view class="user-desc">{{userInfo.description || '推理游戏爱好者'}}</view>
      </view>
      <t-icon name="chevron-right" size="32rpx" color="rgba(255,255,255,0.8)" bind:tap="editProfile" />
    </view>
    
    <view class="user-stats">
      <view class="stat-item" bind:tap="viewInfluence">
        <view class="stat-value">{{userInfo.influence}}</view>
        <view class="stat-label">影响力</view>
      </view>
      <view class="stat-item" bind:tap="viewGames">
        <view class="stat-value">{{userInfo.totalGames}}</view>
        <view class="stat-label">游戏场次</view>
      </view>
      <view class="stat-item" bind:tap="viewWinRate">
        <view class="stat-value">{{userInfo.winRate}}%</view>
        <view class="stat-label">胜率</view>
      </view>
    </view>
  </view>

  <!-- 数据统计卡片 -->
  <view class="stats-section">
    <t-card title="数据统计" class="stats-card">
      <view class="stats-grid">
        <view class="stats-item win">
          <view class="stats-number">{{gameStats.wins}}</view>
          <view class="stats-text">胜利场次</view>
        </view>
        <view class="stats-item lose">
          <view class="stats-number">{{gameStats.losses}}</view>
          <view class="stats-text">失败场次</view>
        </view>
        <view class="stats-item rating">
          <view class="stats-number">{{gameStats.avgRating}}</view>
          <view class="stats-text">平均评分</view>
        </view>
        <view class="stats-item mvp">
          <view class="stats-number">{{gameStats.mvpCount}}</view>
          <view class="stats-text">MVP次数</view>
        </view>
      </view>
      
      <view class="role-types">
        <view class="role-type-title">擅长角色类型</view>
        <view class="role-tags">
          <t-tag 
            theme="primary" 
            size="small" 
            wx:for="{{userInfo.skillTags}}" 
            wx:key="*this"
          >
            {{item}}
          </t-tag>
        </view>
      </view>
    </t-card>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <t-cell-group>
      <t-cell 
        title="修改昵称" 
        note="当前: {{userInfo.nickname}}"
        left-icon="user"
        right-icon="chevron-right"
        bind:click="editNickname"
      />
      <t-cell 
        title="头像设置" 
        note="个性化你的形象"
        left-icon="image"
        right-icon="chevron-right"
        bind:click="editAvatar"
      />
      <t-cell 
        title="消息通知" 
        note="游戏邀请和好友消息"
        left-icon="notification"
        right-icon="chevron-right"
        bind:click="notificationSettings"
      >
        <t-switch 
          slot="right" 
          value="{{settings.notification}}"
          bind:change="onNotificationChange"
        />
      </t-cell>
      <t-cell
        title="隐私设置"
        note="控制个人信息可见性"
        left-icon="lock-on"
        right-icon="chevron-right"
        bind:click="privacySettings"
      />
      <t-cell
        title="API设置"
        note="配置AI服务密钥"
        left-icon="setting"
        right-icon="chevron-right"
        bind:click="apiSettings"
      />
      <t-cell
        title="帮助与反馈"
        note="使用指南和问题反馈"
        left-icon="help-circle"
        right-icon="chevron-right"
        bind:click="helpAndFeedback"
      />
      <t-cell 
        title="关于我们" 
        note="版本信息和开发团队"
        left-icon="info-circle"
        right-icon="chevron-right"
        bind:click="aboutUs"
      />
    </t-cell-group>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section">
    <t-button 
      theme="danger" 
      size="large" 
      bind:tap="logout"
      class="logout-btn"
    >
      退出登录
    </t-button>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <text>版本 {{appVersion}} | © 2024 AI推理游戏</text>
  </view>
</view>

<!-- 编辑昵称弹窗 -->
<t-dialog 
  id="nickname-dialog"
  title="修改昵称"
  confirm-btn="确认"
  cancel-btn="取消"
  bind:confirm="confirmNickname"
>
  <view class="dialog-content">
    <t-input 
      placeholder="请输入新昵称" 
      value="{{newNickname}}"
      bind:change="onNicknameChange"
      maxlength="20"
    />
  </view>
</t-dialog>

<!-- 提示组件 -->
<t-toast id="t-toast" />
<t-dialog id="t-dialog" />
