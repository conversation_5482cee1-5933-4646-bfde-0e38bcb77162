import{__decorate}from"tslib";import{wxComponent,SuperComponent}from"../common/src/index";import config from"../common/config";import props from"./props";const{prefix:prefix}=config,classPrefix=`${prefix}-tab-bar`;let Tabbar=class extends SuperComponent{constructor(){super(...arguments),this.relations={"../tab-bar-item/tab-bar-item":{type:"descendant"}},this.externalClasses=[`${prefix}-class`],this.backupValue=-1,this.data={prefix:prefix,classPrefix:classPrefix},this.properties=props,this.controlledProps=[{key:"value",event:"change"}],this.observers={value(){this.updateChildren()}},this.lifetimes={ready(){this.showChildren()}},this.methods={showChildren(){const{value:e}=this.data;this.$children.forEach(r=>{r.setData({crowded:this.$children.length>3}),r.properties.value===e&&r.showSpread()})},updateChildren(){const{value:e}=this.data;this.$children.forEach(r=>{r.checkActive(e)})},updateValue(e){this._trigger("change",{value:e})},changeOtherSpread(e){this.$children.forEach(r=>{r.properties.value!==e&&r.closeSpread()})},initName(){return this.backupValue+=1}}}};Tabbar=__decorate([wxComponent()],Tabbar);export default Tabbar;