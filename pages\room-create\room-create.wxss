/* 创建房间页面样式 */
.create-room-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 32rpx;
}

/* 返回按钮 */
.back-button {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 40rpx;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 32rpx;
  width: fit-content;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.back-button:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

.back-icon {
  font-size: 32rpx;
  color: white;
  font-weight: bold;
}

.back-text {
  font-size: 28rpx;
  color: white;
  font-weight: 500;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 48rpx;
}

.page-title {
  font-size: 48rpx;
  font-weight: 800;
  color: white;
  margin-bottom: 16rpx;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
}

.page-subtitle {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

.form-section {
  padding: 0;
}

.setting-card, .rules-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 40rpx;
}

.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}

/* 表单输入框 */
.form-input {
  background: rgba(255, 255, 255, 0.8);
  border: 2rpx solid #e6e6e6;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}

/* 选择器样式 */
.form-picker {
  background: rgba(255, 255, 255, 0.8);
  border: 2rpx solid #e6e6e6;
  border-radius: 16rpx;
  padding: 24rpx;
  transition: all 0.3s ease;
}

.picker-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}

/* 滑动选择器 */
.slider-container {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin: 24rpx 0;
}

.slider-min, .slider-max {
  font-size: 24rpx;
  color: #999;
  min-width: 60rpx;
  text-align: center;
}

.player-slider {
  flex: 1;
  height: 40rpx;
}

/* 游戏特色展示 */
.features-display {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.feature-tag {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 24rpx;
  font-size: 24rpx;
  color: #667eea;
  border: 2rpx solid rgba(102, 126, 234, 0.2);
}

.features-desc {
  font-size: 22rpx;
  color: #999;
  text-align: center;
  margin-bottom: 32rpx;
}

/* TDesign组件样式覆盖 */
.t-radio-group {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.t-radio {
  margin-bottom: 0 !important;
}

.t-card__header {
  background: linear-gradient(135deg, #9254de 0%, #722ed1 100%);
  color: white;
  font-weight: 600;
}

.t-cell-group {
  border-radius: 16rpx;
  overflow: hidden;
}

.t-cell {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
}

.t-switch--checked {
  background-color: #9254de !important;
}

/* 创建按钮 - 放大版本 */
.create-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  border-radius: 32rpx !important;
  font-size: 40rpx !important;
  font-weight: 700 !important;
  box-shadow: 0 12rpx 32rpx rgba(102, 126, 234, 0.4) !important;
  margin-bottom: 32rpx;
  padding: 32rpx 48rpx !important;
  min-height: 120rpx !important;
  transform: scale(1.1);
  transition: all 0.3s ease;
}

.create-btn:hover {
  transform: scale(1.15);
  box-shadow: 0 16rpx 40rpx rgba(102, 126, 234, 0.5) !important;
}

/* 游戏规则卡片 */
.rules-list {
  padding: 0;
}

.rule-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16rpx;
  padding-left: 16rpx;
}

.rule-item:last-child {
  margin-bottom: 0;
}

/* 输入框和选择器样式覆盖 */
.t-input__control {
  border-radius: 16rpx !important;
  border: 2rpx solid #e6e6e6 !important;
  background: rgba(255, 255, 255, 0.8) !important;
}

.t-input__control:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1) !important;
}

.t-button--theme-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  border-radius: 24rpx !important;
}
