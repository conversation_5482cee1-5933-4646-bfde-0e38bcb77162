// AI剧情生成页面
// 临时使用简化版本
const aiService = require('../../utils/ai-service-simple');
const errorHandler = require('../../utils/error-handler');

Page({
  data: {
    loading: false,
    optimizing: false,
    selectedType: '',
    optimizedStory: '',
    // 单人测试模式相关
    roomId: '',
    isSingleTestMode: false,
    
    // 剧情类型
    storyTypes: [
      {
        id: 'mystery',
        name: '悬疑推理',
        icon: '🔍',
        description: '经典推理剧情，考验逻辑思维'
      },
      {
        id: 'horror',
        name: '恐怖惊悚',
        icon: '👻',
        description: '紧张刺激，心跳加速'
      },
      {
        id: 'romance',
        name: '浪漫爱情',
        icon: '💕',
        description: '温馨浪漫，情感丰富'
      },
      {
        id: 'adventure',
        name: '冒险探索',
        icon: '🗺️',
        description: '刺激冒险，充满未知'
      },
      {
        id: 'comedy',
        name: '轻松喜剧',
        icon: '😄',
        description: '欢声笑语，轻松愉快'
      },
      {
        id: 'fantasy',
        name: '奇幻魔法',
        icon: '🔮',
        description: '魔法世界，奇幻冒险'
      }
    ]
  },

  onLoad(options) {
    console.log('AI剧情生成页面加载', options);
    // 获取房间信息
    this.roomId = options.roomId || '';
    this.maxPlayers = parseInt(options.maxPlayers) || 6;
    const selectedType = options.selectedType || '';
    const mode = options.mode || '';

    // 检查是否为单人测试模式
    const isSingleTestMode = mode === 'singleTest';

    this.setData({
      roomId: this.roomId,
      isSingleTestMode: isSingleTestMode
    });

    console.log('房间ID:', this.roomId, '最大人数:', this.maxPlayers, '选择的剧本类型:', selectedType, '模式:', mode);

    // 剧本类型映射：将选择剧本页面的ID映射到AI剧情生成页面的类型ID
    const typeMapping = {
      'mysterious_manor': 'mystery',    // 神秘庄园 -> 悬疑推理
      'campus_case': 'mystery',         // 校园悬案 -> 悬疑推理
      'urban_mystery': 'mystery',       // 都市迷案 -> 悬疑推理
      'island_secret': 'mystery',       // 海岛密室 -> 悬疑推理
      'horror_night': 'horror',         // 恐怖之夜 -> 恐怖惊悚
      'love_story': 'romance'           // 爱情故事 -> 浪漫爱情
    };

    // 如果有传入的剧本类型，映射并自动选中
    if (selectedType && typeMapping[selectedType]) {
      this.setData({
        selectedType: typeMapping[selectedType]
      });
    }
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },

  // 选择剧情类型
  selectStoryType(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      selectedType: type,
      optimizedStory: '' // 清空之前的优化结果
    });
  },

  // 显示服务选择
  showServiceSelection() {
    wx.showModal({
      title: '🤖 选择生成方式',
      content: '请选择剧本生成方式：\n\n🤖 AI服务：真正的AI生成，个性化程度高\n🎭 模拟服务：快速稳定，质量优秀\n\n注意：AI服务可能有请求限制',
      showCancel: true,
      cancelText: '🎭 模拟服务',
      confirmText: '🤖 AI服务',
      success: (res) => {
        if (res.confirm) {
          // 用户选择AI服务
          this.startOptimization('ai');
        } else {
          // 用户选择模拟服务
          this.startOptimization('mock');
        }
      }
    });
  },

  // AI优化剧情
  async optimizeStory() {
    if (!this.data.selectedType) {
      wx.showToast({
        title: '请先选择剧情类型',
        icon: 'none'
      });
      return;
    }

    // 显示服务选择对话框
    this.showServiceSelection();
  },

  // 开始优化
  async startOptimization(serviceType) {
    this.setData({ optimizing: true });

    // 显示更详细的加载提示
    wx.showLoading({
      title: 'AI正在创作剧本...',
      mask: true
    });

    // 30秒后提示用户耐心等待
    setTimeout(() => {
      if (this.data.optimizing) {
        wx.hideLoading();
        wx.showLoading({
          title: 'AI创作中，请耐心等待...',
          mask: true
        });
      }
    }, 30000);

    // 60秒后再次提示
    setTimeout(() => {
      if (this.data.optimizing) {
        wx.hideLoading();
        wx.showLoading({
          title: '即将完成，请稍候...',
          mask: true
        });
      }
    }, 60000);

    try {
      // 构建AI生成参数
      const generateParams = {
        storyType: this.data.selectedType,
        playerCount: this.maxPlayers,
        difficulty: 'medium',
        theme: this.getThemeByType(this.data.selectedType),
        specialRequirements: '',
        gameSettings: {
          duration: '60-90分钟',
          rounds: '3轮',
          truthDare: true,
          miniGame: true
        }
      };

      console.log('开始AI剧本生成，参数:', generateParams);

      // 设置服务模式
      if (serviceType === 'ai') {
        aiService.forceUseAI();
        console.log('🤖 强制使用AI服务生成剧本');
      } else {
        aiService.forceUseMock();
        console.log('🎭 强制使用模拟服务生成剧本');
      }

      // 调用AI服务生成剧本
      const scriptResult = await aiService.generateScript(generateParams);

      console.log('AI剧本生成结果:', scriptResult);

      // 使用静态方法提取剧本数据
      const scriptData = aiService.AIService.extractScriptData(scriptResult);

      console.log('AI剧本生成成功:', scriptData);

      // 格式化显示内容
      const optimizedContent = this.formatScriptForDisplay(scriptData);

      // 保存完整的剧本数据到全局状态
      this.scriptData = scriptData;

      this.setData({
        optimizedStory: optimizedContent,
        optimizing: false
      });

      wx.showToast({
        title: 'AI剧本生成完成！',
        icon: 'success'
      });

    } catch (error) {
      console.error('AI剧本生成失败:', error);
      this.setData({ optimizing: false });

      // 使用错误处理器显示友好的错误信息
      errorHandler.showError(error, 'AI剧本生成', this);

      // 降级到模拟内容
      const fallbackContent = this.generateOptimizedContent(this.data.selectedType);
      this.setData({
        optimizedStory: fallbackContent
      });
    }
  },

  // 根据剧情类型获取主题
  getThemeByType(type) {
    const themeMap = {
      'mystery': '现代都市悬疑',
      'horror': '恐怖庄园',
      'romance': '浪漫都市',
      'adventure': '冒险探索',
      'comedy': '轻松校园',
      'fantasy': '奇幻魔法世界'
    };
    return themeMap[type] || '现代都市';
  },

  // 格式化剧本数据为显示内容
  formatScriptForDisplay(scriptData) {
    // 检查数据是否存在
    if (!scriptData) {
      console.error('❌ scriptData 为空或未定义');
      return '剧本数据加载失败，请重试。';
    }

    console.log('📋 格式化剧本数据:', scriptData);

    // 处理新的AI服务返回格式
    let actualScriptData = scriptData;
    if (scriptData.success && scriptData.scriptData) {
      // 新格式：AI服务返回的包装格式
      actualScriptData = scriptData.scriptData;
      console.log('🔄 检测到AI服务格式，提取scriptData:', actualScriptData);
    }

    // 兼容不同的数据结构
    let storyInfo, characters;

    if (actualScriptData.storyInfo) {
      // 新格式：有 storyInfo 对象
      storyInfo = actualScriptData.storyInfo;
      characters = actualScriptData.characters;
    } else if (actualScriptData.title) {
      // 旧格式/模拟服务格式：直接在根级别
      storyInfo = {
        title: actualScriptData.title,
        background: actualScriptData.background,
        coreEvent: actualScriptData.objective || actualScriptData.coreEvent || '待揭晓的神秘事件',
        winConditions: actualScriptData.winConditions || '完成各自的目标，找出真相'
      };
      characters = actualScriptData.characters || [];
    } else {
      console.error('❌ 无法识别的剧本数据格式:', actualScriptData);
      return '剧本数据格式错误，请重试。';
    }

    let content = `【${storyInfo.title}】\n\n`;
    content += `📖 故事背景：\n${storyInfo.background}\n\n`;
    content += `🎯 核心事件：${storyInfo.coreEvent}\n\n`;
    content += `🏆 胜利条件：${storyInfo.winConditions}\n\n`;

    if (characters && characters.length > 0) {
      content += `👥 角色预览（共${characters.length}个角色）：\n\n`;
      characters.forEach((char, index) => {
        // 显示所有角色的基本信息
        content += `${index + 1}. ${char.name}（${char.title}）\n`;

        // 如果有角色预览信息，显示吸引人的标签
        if (char.preview && char.preview.tagline) {
          content += `   ✨ ${char.preview.tagline}\n`;
        }

        // 如果有悬念点，显示神秘感
        if (char.preview && char.preview.intrigue) {
          content += `   🤔 ${char.preview.intrigue}\n`;
        }

        // 如果有独特特征，显示出来
        if (char.preview && char.preview.uniqueTrait) {
          content += `   🎭 ${char.preview.uniqueTrait}\n`;
        }

        content += `\n`; // 角色间空行分隔
      });
    }

    content += `\n✨ 这是由AI智能生成的专属剧本，准备好开始推理之旅了吗？`;

    return content;
  },

  // 模拟AI优化过程
  simulateAIOptimization() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 2000); // 模拟2秒的AI处理时间
    });
  },

  // 生成优化内容
  generateOptimizedContent(type) {
    const optimizedContents = {
      mystery: `【神秘庄园谋杀案】

在一个风雨交加的夜晚，著名侦探小说家威廉·布莱克受邀来到偏远的罗斯庄园参加一场神秘的聚会。庄园的主人是富有的收藏家亨利·罗斯，他声称发现了一件价值连城的古董，想要与几位朋友分享这个秘密。

然而，当夜幕降临时，亨利·罗斯却在自己的书房中被发现死亡，房门从内反锁，窗户紧闭，现场没有任何打斗痕迹。更加诡异的是，那件神秘的古董也不翼而飞。

在场的每个人都有不在场证明，但每个人也都有杀害亨利的动机：
- 威廉·布莱克：欠下巨额赌债，急需金钱
- 管家约翰：被怀疑偷窃，面临解雇
- 侄女艾米丽：唯一的继承人，但与叔叔关系紧张
- 医生汤姆：曾与亨利有医疗纠纷
- 律师莎拉：掌握亨利的所有秘密
- 园丁彼得：默默爱着艾米丽，痛恨亨利的冷酷

随着调查的深入，更多的秘密浮出水面：庄园中隐藏着一个古老的密室，亨利生前一直在寻找一份失传的宝藏地图，而在场的某个人可能并不是他们声称的身份...

真相只有一个，但凶手就在你们中间！`,

      horror: `【午夜校园惊魂】

深夜时分，几名大学生因为准备期末考试而留在了图书馆。然而，当他们准备离开时，却发现所有的门都被锁上了，手机也失去了信号。更可怕的是，图书馆里开始出现一些诡异的现象...

书架上的书会自己掉落，走廊里传来莫名的脚步声，而监控录像中出现了一个不应该存在的身影。学校的传说开始在他们心中蔓延：20年前，一名学生在这里神秘失踪，从此这座图书馆就被诅咒了。

随着时间的推移，恐怖事件愈演愈烈：
- 电梯会自动运行到从不开放的地下室
- 镜子中会出现扭曲的面孔
- 某些书页上会出现血红色的字迹
- 温度骤然下降，呼吸都能看到白雾

更令人恐惧的是，他们发现自己并不是被困在这里的唯一人群。在图书馆的深处，似乎还有其他"人"在游荡，而这些"人"的目的却不得而知...

每个人都必须面对自己内心最深的恐惧，同时努力找出逃离这个噩梦的方法。但是，当真相逐渐浮出水面时，他们发现最可怕的敌人可能就在他们中间...

在这个充满恐怖与悬疑的夜晚，谁能活到天亮？`,

      romance: `【樱花飞舞的约定】

春天的大学校园里，樱花盛开如雪。在这个充满青春气息的季节里，几个年轻人的命运因为一次偶然的相遇而交织在一起。

故事的中心是一封神秘的情书，它被风吹到了不同人的手中，引发了一连串浪漫而复杂的情感纠葛。每个人都以为这封信是写给自己的，但真正的收信人究竟是谁呢？

主要角色们各有各的故事：
- 文学社的才女小雨：外表冷漠但内心温柔，一直暗恋着学长
- 篮球队的阳光男孩小明：看似花心但其实专一，正在寻找真爱
- 图书馆的安静女孩小莉：害羞内向，通过书信表达情感
- 音乐系的浪漫王子小杰：用音乐传达爱意，但不善言辞
- 戏剧社的开朗女孩小芳：活泼外向，是大家的开心果
- 摄影社的神秘男孩小林：用镜头记录美好，默默守护着某个人

随着樱花节的临近，大家决定举办一场特别的活动来寻找情书的真正主人。在准备过程中，误会与巧合不断发生，每个人都在重新审视自己的感情。

有人因为误解而伤心，有人因为勇敢表白而获得幸福，有人发现了意想不到的缘分，还有人学会了放手和祝福...

在樱花飞舞的那个黄昏，所有的秘密都将揭晓，所有的情感都将得到回应。这不仅仅是一个关于爱情的故事，更是关于成长、友谊和青春的美好回忆。`,

      adventure: `【失落文明的宝藏】

在南美洲的亚马逊雨林深处，隐藏着一个传说中的古老文明——黄金城埃尔多拉多。几个世纪以来，无数探险家都试图找到这个神秘的地方，但都无功而返。

现在，一支由考古学家、探险家和当地向导组成的小队，获得了一份古老的地图，决定踏上这次危险而充满未知的探险之旅。

队伍成员各有专长：
- 考古学家艾伦博士：知识渊博，精通古代文字和历史
- 探险家杰克：经验丰富，擅长野外生存和危险应对
- 当地向导玛丽亚：熟悉雨林环境，了解当地传说
- 摄影师汤姆：记录探险过程，意外发现重要线索
- 医生苏珊：负责队伍健康，关键时刻救人于危难
- 工程师迈克：负责技术设备，解决机关谜题

在探险过程中，他们遇到了重重困难：
- 危险的野生动物和毒虫
- 复杂的地形和恶劣的天气
- 古老的机关陷阱和谜题
- 神秘的土著部落和他们的守护者
- 队伍内部的分歧和信任危机

随着深入雨林，他们发现这不仅仅是一次寻宝之旅，而是一场关于人性、贪婪和友谊的考验。古老的诅咒似乎真的存在，而黄金城的秘密远比他们想象的更加复杂...

在面对最终的选择时，每个人都必须决定：是追求财富和名声，还是保护这个古老文明的秘密？他们能否团结一致，安全地走出这片神秘的雨林？`,

      comedy: `【爆笑宿舍生活】

在一所普通大学的6号宿舍楼里，住着几个性格迥异但友谊深厚的室友。他们的日常生活充满了各种搞笑的意外和温馨的时刻。

宿舍成员各有特色：
- 学霸小王：成绩优异但生活白痴，经常闹出各种乌龙
- 吃货小李：对美食有着超乎常人的执着，厨艺却令人堪忧
- 运动狂小张：热爱运动但头脑简单，经常被人忽悠
- 文艺青年小陈：喜欢写诗作画，但作品常常让人摸不着头脑
- 游戏宅小刘：技术高超但与现实脱节，经常分不清虚拟和现实
- 社交达人小赵：人缘极好但记性很差，经常搞混各种约会和活动

故事围绕着他们准备一场重要的宿舍文化节表演展开。为了赢得比赛，他们决定排练一出原创话剧，但排练过程却状况百出：

小王负责编剧，但写出的剧本逻辑混乱；小李负责道具，却把所有道具都做成了食物；小张负责动作设计，结果把话剧变成了武打片；小陈负责台词，写出的对白过于文艺让人听不懂；小刘负责音效，却总是播放游戏音乐；小赵负责联络，结果通知错了时间和地点...

在一系列啼笑皆非的事件中，他们不仅要解决表演的问题，还要处理各种生活中的小麻烦：停电、停水、食物中毒、考试周、恋爱问题等等。

最终，虽然他们的表演可能不是最完美的，但他们的友谊和团队精神却感动了所有人。这个故事充满了欢声笑语，同时也传递着关于友谊、成长和青春的温暖主题。`,

      fantasy: `【魔法学院的秘密】

在一个隐藏于现实世界之外的魔法学院——阿卡迪亚学院里，新学期即将开始。这所学院专门培养年轻的魔法师，但今年却发生了一些不寻常的事情。

学院的守护水晶突然失去了光芒，而这个水晶是维持学院隐身结界的核心。如果不能及时修复，学院就会暴露在普通人的世界中，所有的魔法师都将面临危险。

新入学的学生们被分配到不同的学院：
- 智慧之塔的艾莉：聪明好学，擅长理论魔法和古代咒语
- 勇气之盾的马克：勇敢正直，精通战斗魔法和防护咒语
- 自然之心的露娜：温柔善良，能与动植物沟通，擅长治疗魔法
- 神秘之眼的雷文：冷静理智，专精占卜和预言魔法
- 创造之手的索菲：富有创意，擅长炼金术和魔法道具制作
- 平衡之轮的亚历克斯：性格平和，能够调和不同魔法元素

在调查守护水晶失效的原因时，他们发现这并不是一个简单的意外。学院中似乎隐藏着一个古老的秘密，而某个人正在暗中破坏学院的平衡。

随着调查的深入，他们遇到了各种魔法生物和挑战：
- 会说话的魔法书籍和画像
- 迷宫般的移动楼梯和房间
- 需要解开的古老魔法谜题
- 危险的魔法实验和失控的咒语
- 来自黑暗势力的威胁

在这个过程中，每个学生都必须学会控制自己的魔法力量，同时学会团队合作。他们发现，真正的魔法不仅仅是咒语和法术，更重要的是友谊、勇气和智慧。

最终，他们能否找到守护水晶失效的真正原因？能否阻止黑暗势力的阴谋？这个充满魔法和冒险的故事将带领大家进入一个奇幻的世界。`
    };

    return optimizedContents[type] || '暂无该类型的优化内容，请重新选择。';
  },

  // 重新优化
  regenerateOptimizedStory() {
    this.optimizeStory();
  },

  // 确认使用优化后的剧情
  async confirmOptimizedStory() {
    if (!this.data.optimizedStory) {
      wx.showToast({
        title: '请先进行AI优化',
        icon: 'none'
      });
      return;
    }

    try {
      // 如果有完整的剧本数据，保存到本地存储
      if (this.scriptData) {
        wx.setStorageSync(`script_${this.roomId}`, this.scriptData);
        console.log('剧本数据已保存到本地存储');
      }

      wx.showToast({
        title: 'AI剧情生成完成！',
        icon: 'success'
      });

      // 直接跳转到角色分配页面，传递优化后的剧情
      setTimeout(() => {
        wx.redirectTo({
          url: `/pages/role-assignment/role-assignment?roomId=${this.roomId}&maxPlayers=${this.maxPlayers}&storyType=${this.data.selectedType}`
        });
      }, 1500);
    } catch (error) {
      console.error('保存剧本数据失败:', error);
      // 即使保存失败也继续跳转
      wx.showToast({
        title: 'AI剧情生成完成！',
        icon: 'success'
      });

      setTimeout(() => {
        wx.redirectTo({
          url: `/pages/role-assignment/role-assignment?roomId=${this.roomId}&maxPlayers=${this.maxPlayers}&storyType=${this.data.selectedType}`
        });
      }, 1500);
    }
  }
});
