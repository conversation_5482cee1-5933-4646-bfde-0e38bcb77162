// pages/api-settings/api-settings.js
const apiConfig = require('../../config/api-config');

Page({
  data: {
    apiKey: '',
    maskedApiKey: '',
    isValidKey: false,
    validationMessage: '',
    isTestingConnection: false
  },

  onLoad() {
    this.loadCurrentApiKey();
  },

  /**
   * 加载当前API密钥
   */
  loadCurrentApiKey() {
    const config = apiConfig.getCurrentConfig();
    const currentKey = config.apiKey;
    
    if (currentKey && currentKey !== 'YOUR_NEW_API_KEY_HERE') {
      this.setData({
        maskedApiKey: apiConfig.getSafeKeyDisplay(currentKey),
        isValidKey: true
      });
    } else {
      this.setData({
        maskedApiKey: '未设置',
        isValidKey: false
      });
    }
  },

  /**
   * API密钥输入处理
   */
  onApiKeyInput(e) {
    const apiKey = e.detail.value.trim();
    this.setData({ apiKey });
    
    // 实时验证
    const validation = apiConfig.validateApiKey(apiKey);
    this.setData({
      isValidKey: validation.valid,
      validationMessage: validation.message
    });
  },

  /**
   * 保存API密钥
   */
  saveApiKey() {
    const { apiKey } = this.data;
    
    if (!apiKey) {
      wx.showToast({
        title: '请输入API密钥',
        icon: 'error'
      });
      return;
    }

    const validation = apiConfig.validateApiKey(apiKey);
    if (!validation.valid) {
      wx.showModal({
        title: '密钥格式错误',
        content: validation.message,
        showCancel: false
      });
      return;
    }

    // 保存到本地存储
    const success = apiConfig.setApiKey(apiKey);
    
    if (success) {
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });
      
      // 更新显示
      this.setData({
        apiKey: '',
        maskedApiKey: apiConfig.getSafeKeyDisplay(apiKey),
        isValidKey: true
      });
    } else {
      wx.showToast({
        title: '保存失败',
        icon: 'error'
      });
    }
  },

  /**
   * 测试API连接
   */
  async testConnection() {
    const config = apiConfig.getCurrentConfig();
    
    if (!config.apiKey || config.apiKey === 'YOUR_NEW_API_KEY_HERE') {
      wx.showModal({
        title: '未设置API密钥',
        content: '请先设置有效的API密钥',
        showCancel: false
      });
      return;
    }

    this.setData({ isTestingConnection: true });
    wx.showLoading({ title: '测试连接中...' });

    try {
      const testResult = await this.performConnectionTest(config);
      wx.hideLoading();
      
      if (testResult.success) {
        wx.showModal({
          title: '连接测试成功',
          content: `API连接正常\n响应时间: ${testResult.responseTime}ms`,
          showCancel: false
        });
      } else {
        wx.showModal({
          title: '连接测试失败',
          content: testResult.message,
          showCancel: false
        });
      }
    } catch (error) {
      wx.hideLoading();
      wx.showModal({
        title: '测试失败',
        content: `连接测试出错: ${error.message}`,
        showCancel: false
      });
    } finally {
      this.setData({ isTestingConnection: false });
    }
  },

  /**
   * 执行连接测试
   */
  performConnectionTest(config) {
    return new Promise((resolve) => {
      const startTime = Date.now();
      
      wx.request({
        url: `${config.baseUrl}/chat/completions`,
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.apiKey}`
        },
        data: {
          model: config.model,
          messages: [
            { role: 'user', content: 'Hello, this is a connection test.' }
          ],
          max_tokens: 10
        },
        timeout: 15000,
        success: (res) => {
          const responseTime = Date.now() - startTime;

          if (res.statusCode === 200) {
            resolve({
              success: true,
              responseTime: responseTime,
              message: 'API连接成功'
            });
          } else {
            let errorMessage = `API返回错误: ${res.statusCode}`;

            if (res.data?.error?.message) {
              errorMessage += ` - ${res.data.error.message}`;
            }

            // 添加具体的错误提示
            if (res.statusCode === 401) {
              errorMessage += '\n\n可能原因：\n1. API密钥无效或已过期\n2. 新密钥还未生效（请等待几分钟）\n3. 账户未激活或余额不足';
            } else if (res.statusCode === 403) {
              errorMessage += '\n\n可能原因：\n1. API密钥权限不足\n2. 账户被限制访问';
            } else if (res.statusCode === 429) {
              errorMessage += '\n\n可能原因：\n1. 请求频率过高\n2. 账户配额已用完';
            }

            resolve({
              success: false,
              message: errorMessage
            });
          }
        },
        fail: (error) => {
          resolve({
            success: false,
            message: `网络请求失败: ${error.errMsg}`
          });
        }
      });
    });
  },

  /**
   * 清除API密钥
   */
  clearApiKey() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除当前的API密钥吗？',
      success: (res) => {
        if (res.confirm) {
          const success = apiConfig.clearApiKey();
          
          if (success) {
            wx.showToast({
              title: '已清除',
              icon: 'success'
            });
            
            this.setData({
              maskedApiKey: '未设置',
              isValidKey: false
            });
          } else {
            wx.showToast({
              title: '清除失败',
              icon: 'error'
            });
          }
        }
      }
    });
  },

  /**
   * 查看使用说明
   */
  showInstructions() {
    wx.showModal({
      title: 'API密钥安全设置',
      content: '🚨 重要提醒：Moonshot AI会自动禁用泄露的密钥！\n\n✅ 正确步骤：\n1. 访问 platform.moonshot.cn/console/api-keys\n2. 删除旧的/泄露的密钥\n3. 生成全新的API密钥\n4. 立即复制并粘贴到此处\n5. 点击保存并测试\n\n⚠️ 安全提醒：\n- 不要在截图中展示密钥\n- 不要分享给他人\n- 定期更换密钥\n- 如果401错误，说明密钥已被禁用',
      showCancel: false
    });
  },

  /**
   * 前往测试页面
   */
  goToTestPage() {
    wx.navigateTo({
      url: '/pages/api-test/api-test'
    });
  },

  /**
   * 前往云开发测试页面
   */
  goToCloudTestPage() {
    wx.navigateTo({
      url: '/pages/cloud-test/cloud-test'
    });
  }
});
