/* 手动测试指南页面样式 */
.container {
  padding: 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 进度指示器 */
.progress-indicator {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
  text-align: center;
}

.step-counter {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.progress-dots {
  display: flex;
  justify-content: center;
  gap: 15rpx;
}

.dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.dot.active {
  background: #4CAF50;
  transform: scale(1.2);
}

/* 当前步骤 */
.current-step {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
}

.step-header {
  margin-bottom: 20rpx;
}

.step-title {
  color: white;
  font-size: 36rpx;
  font-weight: bold;
}

.step-description {
  display: block;
  color: rgba(255, 255, 255, 0.9);
  font-size: 30rpx;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.expected-result {
  background: rgba(76, 175, 80, 0.2);
  border-radius: 15rpx;
  padding: 20rpx;
  border-left: 6rpx solid #4CAF50;
}

.expected-label {
  display: block;
  color: #4CAF50;
  font-size: 26rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.expected-text {
  display: block;
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
  line-height: 1.4;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

.action-btn.primary {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.action-btn.primary[disabled] {
  background: rgba(158, 158, 158, 0.5);
  color: rgba(255, 255, 255, 0.6);
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.action-btn.secondary[disabled] {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 255, 255, 0.1);
}

/* 导航按钮 */
.navigation {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.nav-btn {
  width: 200rpx;
  height: 70rpx;
  border-radius: 35rpx;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 28rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.nav-btn[disabled] {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 255, 255, 0.1);
}

/* 测试信息 */
.test-info {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
}

.info-value {
  color: white;
  font-size: 28rpx;
  font-weight: bold;
}

/* 重置按钮 */
.reset-section {
  text-align: center;
  margin-bottom: 30rpx;
}

.reset-btn {
  width: 300rpx;
  height: 70rpx;
  border-radius: 35rpx;
  background: rgba(255, 87, 34, 0.8);
  color: white;
  font-size: 28rpx;
  border: none;
}

/* 测试说明 */
.test-instructions {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  backdrop-filter: blur(10rpx);
}

.instructions-title {
  display: block;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.instructions-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.instruction-item {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
  line-height: 1.4;
}
