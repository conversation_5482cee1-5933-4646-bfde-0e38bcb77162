// 角色分配功能测试
const aiService = require('../utils/ai-service-simple');
const roomManager = require('../utils/room-manager');

/**
 * 测试角色分配功能
 */
async function testRoleAssignment() {
  console.log('🧪 开始测试角色分配功能...');
  
  try {
    // 1. 创建测试房间
    console.log('📝 步骤1: 创建测试房间');
    const roomResult = roomManager.createRoom({
      gameMode: '经典推理模式',
      maxPlayers: 6,
      timeLimit: 600,
      rounds: 3
    });
    console.log('✅ 房间创建成功:', roomResult.roomId);

    // 2. 添加测试玩家
    console.log('📝 步骤2: 添加测试玩家');
    const testPlayers = [
      { id: 'test_player_2', nickname: '测试玩家2' },
      { id: 'test_player_3', nickname: '测试玩家3' },
      { id: 'test_player_4', nickname: '测试玩家4' },
      { id: 'test_player_5', nickname: '测试玩家5' },
      { id: 'test_player_6', nickname: '测试玩家6' }
    ];

    for (const player of testPlayers) {
      roomManager.joinRoom(roomResult.roomId, player);
    }
    console.log('✅ 测试玩家添加完成');

    // 3. 生成测试剧本
    console.log('📝 步骤3: 生成测试剧本');
    const scriptData = await aiService.generateScript({
      storyType: 'mystery',
      playerCount: 6,
      difficulty: 'medium',
      theme: '神秘庄园'
    });
    console.log('✅ 剧本生成完成:', scriptData.storyInfo?.title);

    // 4. 测试角色分配
    console.log('📝 步骤4: 测试角色分配');
    const room = roomManager.getRoomInfo(roomResult.roomId);
    const playerIds = Array.from(room.players.keys());
    
    const assignmentResult = await aiService.generateRoleAssignment(scriptData, playerIds);
    console.log('✅ 角色分配完成');

    // 5. 验证分配结果
    console.log('📝 步骤5: 验证分配结果');
    
    // 验证角色数量
    if (assignmentResult.assignments.length !== scriptData.characters.length) {
      throw new Error(`角色分配数量不匹配: 期望${scriptData.characters.length}, 实际${assignmentResult.assignments.length}`);
    }

    // 验证每个角色都被分配
    const assignedCharacterIds = assignmentResult.assignments.map(a => a.characterId);
    const allCharacterIds = scriptData.characters.map(c => c.id);
    
    for (const charId of allCharacterIds) {
      if (!assignedCharacterIds.includes(charId)) {
        throw new Error(`角色 ${charId} 未被分配`);
      }
    }

    // 验证每个玩家都有角色
    const assignedPlayerIds = assignmentResult.assignments.map(a => a.playerId);
    for (const playerId of playerIds) {
      if (!assignedPlayerIds.includes(playerId)) {
        throw new Error(`玩家 ${playerId} 未获得角色`);
      }
    }

    // 6. 输出测试结果
    console.log('📊 测试结果详情:');
    console.log('房间ID:', roomResult.roomId);
    console.log('剧本标题:', scriptData.storyInfo?.title);
    console.log('角色数量:', scriptData.characters?.length);
    console.log('玩家数量:', playerIds.length);
    console.log('分配数量:', assignmentResult.assignments.length);
    
    console.log('\n🎭 角色分配详情:');
    assignmentResult.assignments.forEach(assignment => {
      const player = room.players.get(assignment.playerId);
      const character = scriptData.characters.find(c => c.id === assignment.characterId);
      console.log(`${player?.nickname || assignment.playerId} → ${character?.name || assignment.characterId} (${character?.title || '未知身份'})`);
    });

    console.log('\n✅ 角色分配功能测试通过！');
    return {
      success: true,
      roomId: roomResult.roomId,
      scriptData,
      assignmentResult
    };

  } catch (error) {
    console.error('❌ 角色分配功能测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 测试角色增强功能
 */
async function testRoleEnhancement() {
  console.log('🧪 开始测试角色增强功能...');
  
  try {
    // 创建测试角色
    const testCharacter = {
      id: 'test_char_001',
      name: '艾米丽·哈特',
      title: '庄园女主人',
      background: '你是这座神秘庄园的女主人，表面上优雅端庄，但内心隐藏着不为人知的秘密。',
      secrets: ['你知道庄园地下室隐藏着重要证据', '你与其中一位客人有着不为人知的关系']
    };

    // 模拟角色增强过程（这里需要实际的增强逻辑）
    const enhancedRole = {
      ...testCharacter,
      faction: '中立阵营',
      difficulty: '中等难度',
      objectives: ['保护庄园的秘密不被发现', '找出真正的幕后黑手'],
      relationships: [
        { targetId: 'unknown', relation: '待发现', description: '在游戏过程中逐渐揭示与其他角色的关系' }
      ]
    };

    console.log('✅ 角色增强测试通过');
    console.log('增强后角色:', enhancedRole);
    
    return {
      success: true,
      enhancedRole
    };

  } catch (error) {
    console.error('❌ 角色增强测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始运行角色分配相关测试...\n');
  
  const results = [];
  
  // 测试1: 角色分配功能
  const assignmentTest = await testRoleAssignment();
  results.push({ name: '角色分配功能', ...assignmentTest });
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // 测试2: 角色增强功能
  const enhancementTest = await testRoleEnhancement();
  results.push({ name: '角色增强功能', ...enhancementTest });
  
  // 输出总结
  console.log('\n📊 测试总结:');
  results.forEach(result => {
    const status = result.success ? '✅ 通过' : '❌ 失败';
    console.log(`${result.name}: ${status}`);
    if (!result.success) {
      console.log(`  错误: ${result.error}`);
    }
  });
  
  const passedCount = results.filter(r => r.success).length;
  console.log(`\n总计: ${passedCount}/${results.length} 个测试通过`);
  
  return results;
}

// 如果直接运行此文件，执行测试
if (typeof module !== 'undefined' && require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testRoleAssignment,
  testRoleEnhancement,
  runAllTests
};
