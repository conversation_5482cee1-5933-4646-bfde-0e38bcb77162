// test/api-config-test.js
// API配置验证测试

const aiService = require('../utils/ai-service');

/**
 * API配置测试类
 */
class ApiConfigTest {
  constructor() {
    this.testResults = [];
  }

  /**
   * 运行所有API配置测试
   */
  async runAllTests() {
    console.log('🔧 开始API配置验证测试...\n');

    try {
      this.testApiConfiguration();
      await this.testBasicConnection();
      await this.testSimpleRequest();
      
      this.printTestSummary();
    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error);
    }
  }

  /**
   * 测试API配置
   */
  testApiConfiguration() {
    console.log('⚙️ 验证API配置...');
    
    try {
      const expectedConfig = {
        apiKey: 'sk-Ts8ILZBbn0A9WKmOH8BysqFLNZ0uuQ5MPhYKlBeozDjKFhSl',
        baseUrl: 'https://api.moonshot.cn/v1',
        model: 'kimi-k2-0711-preview'
      };

      const actualConfig = {
        apiKey: aiService.apiKey,
        baseUrl: aiService.baseUrl,
        model: aiService.model
      };

      let configCorrect = true;
      const configResults = [];

      for (const [key, expectedValue] of Object.entries(expectedConfig)) {
        const actualValue = actualConfig[key];
        const isCorrect = actualValue === expectedValue;
        
        configResults.push({
          key: key,
          expected: expectedValue,
          actual: actualValue,
          correct: isCorrect
        });

        if (!isCorrect) {
          configCorrect = false;
        }
      }

      if (configCorrect) {
        console.log('✅ API配置验证通过');
        console.log('   配置详情:');
        configResults.forEach(r => {
          console.log(`   ${r.key}: ${r.actual}`);
        });
        this.addTestResult('API配置验证', true, '所有配置项正确');
      } else {
        const incorrectConfigs = configResults.filter(r => !r.correct);
        console.log('❌ API配置验证失败');
        incorrectConfigs.forEach(r => {
          console.log(`   ${r.key}: 期望 "${r.expected}", 实际 "${r.actual}"`);
        });
        this.addTestResult('API配置验证', false, `${incorrectConfigs.length} 个配置项错误`);
      }
    } catch (error) {
      console.log('❌ API配置验证出错:', error.message);
      this.addTestResult('API配置验证', false, error.message);
    }
  }

  /**
   * 测试基础连接
   */
  async testBasicConnection() {
    console.log('🔗 测试基础连接...');
    
    try {
      const success = await aiService.testConnection();
      
      if (success) {
        console.log('✅ 基础连接测试通过');
        this.addTestResult('基础连接', true, 'API连接正常');
      } else {
        console.log('❌ 基础连接测试失败');
        this.addTestResult('基础连接', false, 'API连接失败');
      }
    } catch (error) {
      console.log('❌ 基础连接测试出错:', error.message);
      this.addTestResult('基础连接', false, error.message);
    }
  }

  /**
   * 测试简单请求
   */
  async testSimpleRequest() {
    console.log('📝 测试简单AI请求...');
    
    try {
      const testParams = {
        theme: '测试主题',
        playerCount: 4,
        difficulty: 'easy',
        gameType: 'mystery'
      };

      const result = await aiService.generateScript(testParams);
      
      if (result && (typeof result === 'object' || typeof result === 'string')) {
        console.log('✅ 简单请求测试通过');
        console.log('   响应类型:', typeof result);
        console.log('   响应长度:', typeof result === 'string' ? result.length : JSON.stringify(result).length);
        this.addTestResult('简单请求', true, 'AI请求响应正常');
      } else {
        console.log('❌ 简单请求测试失败: 响应格式异常');
        this.addTestResult('简单请求', false, '响应格式异常');
      }
    } catch (error) {
      console.log('❌ 简单请求测试失败:', error.message);
      this.addTestResult('简单请求', false, error.message);
    }
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, success, message) {
    this.testResults.push({
      name: testName,
      success: success,
      message: message,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 打印测试总结
   */
  printTestSummary() {
    console.log('\n📋 API配置测试总结:');
    console.log('=' * 50);
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(result => result.success).length;
    const failedTests = totalTests - passedTests;

    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests} ✅`);
    console.log(`失败: ${failedTests} ❌`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    console.log('\n详细结果:');
    this.testResults.forEach((result, index) => {
      const status = result.success ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.name}: ${result.message}`);
    });

    if (passedTests === totalTests) {
      console.log('\n🎉 所有API配置测试通过！API配置正确。');
    } else {
      console.log('\n⚠️  部分测试失败，请检查API配置。');
    }

    // 提供配置建议
    console.log('\n💡 配置检查清单:');
    console.log('1. ✅ API密钥: sk-rFun7AywY7jUUdJAtUBbFDxhaVTI5okdFpL3mSSeLfOmKCmP');
    console.log('2. ✅ API地址: https://api.moonshot.cn/v1');
    console.log('3. ✅ 模型名称: kimi-k2-0711-preview');
    console.log('4. 📋 微信小程序域名白名单: https://api.moonshot.cn');
    console.log('5. 🔧 请求超时: 60秒');
    console.log('6. 🔄 重试机制: 最多3次');
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const test = new ApiConfigTest();
  test.runAllTests().catch(console.error);
}

module.exports = ApiConfigTest;
