<wxs src="../common/utils.wxs" module="_"/><view id="{{tId}}" style="{{_._style([style, customStyle])}}" class="{{_.cls(classPrefix, [_placement, ['block', block]])}} class {{prefix}}-class" disabled="{{_disabled}}" aria-role="radio" aria-checked="{{checked}}" aria-label="{{label + content}}" aria-disabled="{{_disabled}}" tabindex="{{tabindex}}" mut-bind:tap="handleTap"><view class="{{_.cls(classPrefix + '__icon', [_placement, ['checked', checked], ['disabled', _disabled]])}} {{prefix}}-class-icon"><slot name="icon" wx:if="{{slotIcon}}"/><view wx:elif="{{customIcon}}" class="{{classPrefix}}__image"><image src="{{checked ? iconVal[0] : iconVal[1]}}" class="{{classPrefix}}-icon__image" webp/></view><block wx:else><t-icon wx:if="{{checked && (icon == 'circle' || icon == 'line')}}" name="{{icon == 'circle' ? 'check-circle-filled' : 'check'}}" class="{{classPrefix}}__icon-wrap"/><view wx:if="{{checked && icon == 'dot'}}" class="{{_.cls(classPrefix + '__icon-' + icon, [['disabled', _disabled]])}}"/><view wx:if="{{!checked && (icon == 'circle' || icon == 'dot')}}" class="{{_.cls(classPrefix + '__icon-circle', [['disabled', _disabled]])}}"/><view wx:if="{{!checked && icon == 'line'}}" class="placeholder"></view></block></view><view class="{{classPrefix}}__content" data-target="text" mut-bind:tap="handleTap"><view class="{{_.cls(classPrefix + '__title', [['disabled', _disabled], ['checked', checked]])}} {{prefix}}-class-label" style="-webkit-line-clamp:{{maxLabelRow}}"><block wx:if="{{label}}">{{label}}</block><slot/><slot name="label"/></view><view class="{{_.cls(classPrefix + '__description', [['disabled', _disabled], ['checked', checked]])}} {{prefix}}-class-content" style="-webkit-line-clamp:{{maxContentRow}}"><block wx:if="{{content}}">{{content}}</block><slot name="content"/></view></view><view wx:if="{{!borderless}}" class="{{_.cls(classPrefix + '__border', [_placement])}} {{prefix}}-class-border"/></view>