# 网络连接问题排查指南

## 🚨 问题现象
```
AI剧本生成失败: Error: 网络连接失败，请检查网络设置
```

## 🔍 问题分析

### 1. 可能的原因
- **API地址错误**: 使用了错误的Moonshot API地址
- **域名白名单**: 微信小程序未配置合法域名
- **网络连接**: 设备网络连接异常
- **API密钥**: 密钥无效或已过期
- **请求超时**: 网络延迟导致请求超时

### 2. 已修复的问题
- ✅ 确认API地址正确: `https://api.moonshot.cn/v1` (按照PRD文档配置)
- ✅ 确认模型版本正确: `kimi-k2-0711-preview` (按照PRD文档配置)
- ✅ 增加超时时间: 30秒 → 60秒
- ✅ 添加重试机制: 最多重试3次
- ✅ 改进错误处理: 更详细的错误信息

## 🛠️ 解决方案

### 步骤1: 检查网络连接
1. 打开网络测试页面: `/pages/network-test/network-test`
2. 点击"运行完整诊断"
3. 查看网络状态和域名可达性

### 步骤2: 配置微信小程序域名白名单
1. 登录微信小程序后台
2. 进入"开发" → "开发管理" → "开发设置"
3. 在"服务器域名"中添加:
   ```
   request合法域名: https://api.moonshot.cn
   ```

### 步骤3: 验证API密钥
1. 检查 `utils/ai-service.js` 中的API密钥
2. 确认密钥格式正确: `sk-xxxxxxxxxx`
3. 验证密钥是否有效且未过期

### 步骤4: 测试API连接
1. 在网络测试页面点击"测试AI请求"
2. 查看是否能成功连接到API
3. 检查响应时间是否正常

## 🔧 技术修复详情

### 1. API服务配置更新
```javascript
// utils/ai-service.js
constructor() {
  this.apiKey = 'sk-rFun7AywY7jUUdJAtUBbFDxhaVTI5okdFpL3mSSeLfOmKCmP';
  this.baseUrl = 'https://api.moonshot.cn/v1'; // 按照PRD文档的正确地址
  this.model = 'kimi-k2-0711-preview'; // 按照PRD文档的正确模型
  
  this.requestConfig = {
    timeout: 60000, // 增加超时时间
    maxRetries: 3,  // 重试机制
    retryDelay: 2000
  };
}
```

### 2. 请求重试机制
```javascript
async makeHttpRequest(endpoint, data, retryCount = 0) {
  // 添加重试逻辑
  if (retryCount < this.requestConfig.maxRetries) {
    setTimeout(() => {
      this.makeHttpRequest(endpoint, data, retryCount + 1)
        .then(resolve)
        .catch(reject);
    }, this.requestConfig.retryDelay);
  }
}
```

### 3. 错误处理改进
```javascript
handleApiError(error) {
  const errorMsg = error.message || '';
  
  if (errorMsg.includes('网络') || errorMsg.includes('连接')) {
    return new Error('网络连接失败，请检查网络设置');
  } else if (errorMsg.includes('401')) {
    return new Error('AI服务认证失败，请检查API密钥');
  }
  // ... 更多错误类型处理
}
```

## 🧪 测试验证

### 使用网络诊断工具
1. 访问 `/pages/network-test/network-test` 页面
2. 运行完整诊断，查看:
   - 📶 网络状态
   - 🌐 域名可达性  
   - 🔌 API端点连接
   - 💡 修复建议

### 手动测试步骤
1. **基础连接测试**:
   ```javascript
   const success = await aiService.testConnection();
   ```

2. **简单请求测试**:
   ```javascript
   const result = await aiService.generateScript({
     theme: '测试主题',
     playerCount: 4,
     difficulty: 'easy'
   });
   ```

## 📋 常见问题FAQ

### Q: 仍然显示"网络连接失败"怎么办？
A: 
1. 确认设备网络连接正常
2. 检查微信小程序域名白名单配置
3. 尝试切换网络环境（WiFi/移动数据）
4. 联系管理员检查API密钥状态

### Q: API响应很慢怎么办？
A:
1. 检查网络质量，建议使用WiFi
2. 当前超时时间已设置为60秒
3. 可以在 `requestConfig.timeout` 中调整超时时间

### Q: 如何查看详细的错误信息？
A:
1. 打开微信开发者工具控制台
2. 查看网络请求详情
3. 使用网络诊断工具获取详细报告

## 🔄 后续优化建议

1. **监控和告警**: 添加API调用成功率监控
2. **缓存机制**: 对常用请求结果进行缓存
3. **降级策略**: API不可用时提供本地默认内容
4. **用户体验**: 添加更友好的加载和错误提示

## 📞 技术支持

如果问题仍然存在，请提供以下信息：
- 网络诊断工具的完整报告
- 微信开发者工具的控制台错误日志
- 设备和网络环境信息
- 复现问题的具体步骤
