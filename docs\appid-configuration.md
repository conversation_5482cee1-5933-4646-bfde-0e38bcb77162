# 微信小程序AppID配置说明

## 📋 **配置信息**

### 小程序基本信息
- **AppID**: `wxa9e14950d650cc0c`
- **项目名称**: AI推理游戏
- **描述**: 聚会游戏小程序

## ✅ **已更新的配置文件**

### 1. 主配置文件
- **`project.config.json`** ✅
  ```json
  {
    "appid": "wxa9e14950d650cc0c",
    "projectname": "AI推理游戏",
    "description": "聚会游戏小程序"
  }
  ```

### 2. 备份配置文件
- **`project.config copy.json`** ✅
  ```json
  {
    "appid": "wxa9e14950d650cc0c"
  }
  ```

### 3. 文档配置
- **`prd.md`** ✅
  ```bash
  # 微信小程序配置
  WECHAT_APP_ID=wxa9e14950d650cc0c
  WECHAT_APP_SECRET=your_app_secret
  ```

## 🔧 **配置验证**

### 检查配置是否生效
1. **微信开发者工具**:
   - 打开项目，查看右上角是否显示正确的AppID
   - 确认项目名称为"AI推理游戏"

2. **编译测试**:
   - 点击"编译"按钮
   - 查看控制台是否有AppID相关错误

3. **真机预览**:
   - 点击"预览"生成二维码
   - 用微信扫码测试是否能正常打开

## 🌐 **域名配置要求**

由于更换了AppID，需要在新的小程序后台配置合法域名：

### 必须配置的域名
```
request合法域名: https://api.moonshot.ai
```

### 配置步骤
1. 登录 [微信小程序后台](https://mp.weixin.qq.com)
2. 使用新AppID对应的账号登录
3. 进入"开发" → "开发管理" → "开发设置"
4. 在"服务器域名"中添加上述域名
5. 保存配置

## ⚠️ **注意事项**

### 1. 开发者权限
- 确保你的微信账号已被添加为该小程序的开发者
- 如果没有权限，需要管理员在后台添加

### 2. 域名配置
- 新AppID需要重新配置所有合法域名
- 之前的域名配置不会自动迁移

### 3. 发布权限
- 确认是否有该小程序的发布权限
- 测试版本和正式版本可能需要不同的权限

## 🚀 **下一步操作**

### 1. 立即验证
- [ ] 在微信开发者工具中打开项目
- [ ] 确认AppID显示正确
- [ ] 编译项目无错误

### 2. 配置域名
- [ ] 登录小程序后台
- [ ] 添加 `https://api.moonshot.ai` 到合法域名
- [ ] 保存配置并等待生效

### 3. 功能测试
- [ ] 测试AI剧本生成功能
- [ ] 验证网络请求正常
- [ ] 确认所有页面正常显示

## 🔍 **故障排查**

### 如果遇到问题

1. **AppID不匹配**:
   ```
   错误: AppID不匹配或无权限
   解决: 确认使用正确的微信账号，并检查开发者权限
   ```

2. **域名配置问题**:
   ```
   错误: url not in domain list
   解决: 在小程序后台配置 https://api.moonshot.ai
   ```

3. **编译错误**:
   ```
   错误: 项目配置文件错误
   解决: 检查 project.config.json 格式是否正确
   ```

## 📞 **技术支持**

如果配置过程中遇到问题：
1. 检查微信开发者工具控制台的错误信息
2. 确认AppID和账号权限
3. 验证域名配置是否正确
4. 查看网络诊断工具的检测结果

## 📝 **配置完成检查清单**

- [ ] ✅ project.config.json 已更新AppID
- [ ] ✅ project.config copy.json 已更新AppID  
- [ ] ✅ prd.md 文档已更新AppID
- [ ] 🔄 微信小程序后台域名配置 (待完成)
- [ ] 🔄 开发者工具验证 (待完成)
- [ ] 🔄 AI功能测试 (待完成)

配置完成后，你的AI推理游戏小程序就可以使用新的AppID正常运行了！
