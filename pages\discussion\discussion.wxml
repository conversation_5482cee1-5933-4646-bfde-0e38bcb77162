<!-- 讨论阶段页面 -->
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="nav-left" bindtap="goBack">
        <text class="nav-icon">←</text>
      </view>
      <view class="nav-title">💬 讨论阶段 - 第{{currentRound}}轮</view>
      <view class="nav-right">
        <text class="timer {{timeLeft <= 60 ? 'urgent' : ''}}">{{formatTime(timeLeft)}}</text>
      </view>
    </view>
  </view>

  <!-- 页面内容 -->
  <view class="page-content">
    <!-- 讨论说明卡片 -->
    <view class="instruction-card artistic-card">
      <view class="section-header">
        <text class="section-icon">💬</text>
        <text class="section-title">讨论环节</text>
      </view>
      <view class="instruction-content">
        <text class="instruction-text">在这个环节中，所有玩家可以自由讨论案件细节、分享线索、提出质疑。请充分利用这段时间收集信息，为接下来的投票做准备。</text>
      </view>
    </view>

    <!-- 标签页切换 -->
    <view class="tabs-card artistic-card">
      <view class="tab-header">
        <view 
          class="tab-item {{activeTab === 'all' ? 'active' : ''}}"
          bindtap="switchTab"
          data-tab="all"
        >
          <text class="tab-icon">👥</text>
          <text class="tab-text">全员讨论</text>
        </view>
        <view 
          class="tab-item {{activeTab === 'clues' ? 'active' : ''}}"
          bindtap="switchTab"
          data-tab="clues"
        >
          <text class="tab-icon">🔍</text>
          <text class="tab-text">线索汇总</text>
        </view>
        <view 
          class="tab-item {{activeTab === 'notes' ? 'active' : ''}}"
          bindtap="switchTab"
          data-tab="notes"
        >
          <text class="tab-icon">📝</text>
          <text class="tab-text">推理笔记</text>
        </view>
      </view>

      <!-- 全员讨论内容 -->
      <view class="tab-content" wx:if="{{activeTab === 'all'}}">
        <view class="chat-area">
          <scroll-view class="message-list" scroll-y="{{true}}" scroll-top="{{scrollTop}}">
            <view class="message-item" wx:for="{{allMessages}}" wx:key="id">
              <view class="message-avatar">
                <view class="avatar-circle">{{item.sender.nickname.charAt(0)}}</view>
              </view>
              <view class="message-content">
                <view class="message-header">
                  <text class="sender-name">{{item.sender.nickname}}</text>
                  <text class="message-time">{{item.timeStr}}</text>
                </view>
                <view class="message-bubble">
                  <text>{{item.content}}</text>
                </view>
              </view>
            </view>
          </scroll-view>
          
          <!-- 输入区域 -->
          <view class="input-area">
            <view class="input-wrapper">
              <input 
                class="message-input"
                placeholder="说点什么..."
                value="{{inputText}}"
                bindinput="onInputChange"
                bindconfirm="sendMessage"
                confirm-type="send"
                maxlength="200"
              />
              <view class="input-counter">{{inputText.length}}/200</view>
            </view>
            <button class="send-btn" bindtap="sendMessage" disabled="{{!inputText.trim()}}">
              <text class="send-icon">📤</text>
            </button>
          </view>
        </view>
      </view>

      <!-- 线索汇总内容 -->
      <view class="tab-content" wx:if="{{activeTab === 'clues'}}">
        <view class="clues-list">
          <view class="clue-item" wx:for="{{cluesList}}" wx:key="index">
            <view class="clue-header">
              <text class="clue-icon">🔍</text>
              <text class="clue-title">{{item.title}}</text>
            </view>
            <view class="clue-content">{{item.content}}</view>
            <view class="clue-source">来源：{{item.source}}</view>
          </view>
          <view class="no-clues" wx:if="{{cluesList.length === 0}}">
            暂无线索信息
          </view>
        </view>
      </view>

      <!-- 推理笔记内容 -->
      <view class="tab-content" wx:if="{{activeTab === 'notes'}}">
        <view class="notes-area">
          <textarea 
            class="notes-input"
            placeholder="记录你的推理过程和重要发现..."
            value="{{notesText}}"
            bindinput="onNotesChange"
            maxlength="500"
            auto-height
          ></textarea>
          <view class="notes-counter">{{notesText.length}}/500</view>
        </view>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="actions-card artistic-card">
      <view class="section-header">
        <text class="section-icon">⚡</text>
        <text class="section-title">快捷操作</text>
      </view>
      <view class="quick-actions">
        <button class="quick-btn" bindtap="shareClue">
          <text class="quick-icon">💡</text>
          <text class="quick-text">分享线索</text>
        </button>
        <button class="quick-btn" bindtap="accusePlayer">
          <text class="quick-icon">👆</text>
          <text class="quick-text">指控玩家</text>
        </button>
        <button class="quick-btn" bindtap="raiseDoubt">
          <text class="quick-icon">❓</text>
          <text class="quick-text">提出质疑</text>
        </button>
        <button class="quick-btn" bindtap="showGameRules">
          <text class="quick-icon">📋</text>
          <text class="quick-text">游戏规则</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <button class="action-btn secondary" bindtap="showGameRules">
      <text class="btn-icon">📋</text>
      <text>游戏规则</text>
    </button>
    <button class="action-btn primary" bindtap="proceedToVoting">
      <text class="btn-icon">🗳️</text>
      <text>进入投票</text>
    </button>
  </view>
</view>
