# 问题排查指南

## 微信开发者工具错误

### 常见错误信息

如果你看到以下错误信息，这些是微信开发者工具内部的问题，不是我们代码的问题：

```
Failed to load resource: the server responded with a status of 500 ()
Uncaught TypeError: document.querySelector is not a function
Uncaught TypeError: document.getElementById is not a function
crbug/1173575, non-JS module files deprecated.
```

### 解决方案

#### 1. 重启开发者工具
- 完全关闭微信开发者工具
- 重新打开项目
- 清除缓存：工具 → 清除缓存 → 清除所有缓存

#### 2. 检查开发者工具版本
- 确保使用最新版本的微信开发者工具
- 如果版本过旧，请更新到最新版本

#### 3. 项目配置检查
确保 `project.config.json` 配置正确：

```json
{
  "miniprogramRoot": "./",
  "compileType": "miniprogram",
  "setting": {
    "urlCheck": false,
    "es6": true,
    "enhance": true,
    "postcss": true,
    "minified": true
  }
}
```

#### 4. 清除项目缓存
- 删除 `miniprogram_npm` 文件夹
- 重新构建 npm：工具 → 构建 npm

## 角色分配功能测试

### 快速测试步骤

1. **打开简单测试页面**
   - 在主页点击"🧪 简单角色测试"
   - 或直接访问 `pages/simple-role-test/simple-role-test`

2. **运行测试**
   - 点击"🚀 开始测试"按钮
   - 观察测试结果输出

3. **预期结果**
   ```
   开始测试...
   1. 加载AI服务...
   ✅ AI服务加载成功
   2. 测试房间管理器...
   ✅ 房间创建成功: XXXXXX
   ✅ 测试玩家添加完成
   3. 生成测试剧本...
   ✅ 剧本生成成功: [剧本标题]
   4. 测试角色分配...
   ✅ 角色分配成功: 6个角色已分配
   5. 验证分配结果...
   ✅ 所有测试通过!
   
   📋 分配详情:
   1. 玩家2 → 角色名称
   2. 玩家3 → 角色名称
   ...
   ```

### 常见问题

#### 1. 模块加载失败
**错误**: `Cannot read property 'xxx' of undefined`

**解决方案**:
- 检查文件路径是否正确
- 确保所有依赖文件存在
- 重启开发者工具

#### 2. AI服务不可用
**错误**: `AI服务失败，回退到模拟服务`

**解决方案**:
- 这是正常的降级机制
- 检查 `config/api-config.js` 中的API配置
- 确保网络连接正常

#### 3. 角色分配失败
**错误**: `角色数量不匹配`

**解决方案**:
- 检查剧本生成是否成功
- 确保玩家数量与角色数量一致
- 查看控制台日志获取详细信息

## 功能验证清单

### ✅ 基础功能
- [ ] AI服务模块加载
- [ ] 房间管理器功能
- [ ] 剧本生成功能
- [ ] 角色分配算法
- [ ] 结果验证逻辑

### ✅ 用户界面
- [ ] 角色分配页面显示
- [ ] 角色信息展示
- [ ] 分配进度动画
- [ ] 错误提示显示

### ✅ 数据流
- [ ] 本地存储读写
- [ ] 房间数据获取
- [ ] 角色信息增强
- [ ] 分配结果保存

## 调试技巧

### 1. 控制台日志
在微信开发者工具中查看控制台输出：
- 点击"Console"标签
- 查看详细的日志信息
- 注意错误和警告信息

### 2. 网络请求
如果使用真实AI服务：
- 查看"Network"标签
- 检查API请求状态
- 验证请求参数和响应

### 3. 存储数据
检查本地存储数据：
- 在控制台输入：`wx.getStorageSync('script_test123')`
- 查看存储的剧本数据
- 验证数据格式正确性

### 4. 断点调试
在关键位置设置断点：
- 角色分配开始处
- 数据处理逻辑
- 错误处理代码

## 性能监控

### 内存使用
- 监控页面内存占用
- 检查是否有内存泄漏
- 及时清理不需要的数据

### 响应时间
- 测量角色分配耗时
- 优化慢速操作
- 提供用户反馈

## 联系支持

如果遇到无法解决的问题：

1. **收集信息**
   - 错误信息截图
   - 控制台日志
   - 操作步骤描述

2. **检查文档**
   - 查看相关技术文档
   - 搜索已知问题解决方案

3. **社区求助**
   - 微信开发者社区
   - 技术论坛和博客

记住：大多数错误都是环境配置问题，而不是代码逻辑问题。保持耐心，逐步排查。
