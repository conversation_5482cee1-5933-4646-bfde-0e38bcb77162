<!--角色分配页面-->
<view class="role-assignment-container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <t-icon name="chevron-left" size="48rpx" color="white" bind:tap="goBack" />
      <view class="navbar-title">角色分配</view>
      <view style="width: 48rpx;"></view>
    </view>
  </view>

  <!-- 分配状态 -->
  <view class="assignment-status">
    <view class="status-card">
      <view class="status-icon">
        <t-icon name="{{assignmentComplete ? 'check-circle' : 'loading'}}"
               size="80rpx"
               color="{{assignmentComplete ? '#52c41a' : '#667eea'}}"
               class="{{assignmentComplete ? '' : 'rotating'}}" />
      </view>
      <view class="status-text">
        <view class="status-title">
          {{assignmentComplete ? '角色分配完成' : 'AI正在分配角色'}}
        </view>
        <view class="status-desc">
          {{assignmentComplete ? '所有玩家已获得专属角色' : '基于剧情需要为每位玩家匹配最佳角色'}}
        </view>
      </view>
    </view>
  </view>

  <!-- 我的角色卡片 -->
  <view class="my-role-section" wx:if="{{myRole}}">
    <view class="section-title">
      <t-icon name="user" size="32rpx" color="#667eea" />
      <text>我的角色</text>
    </view>

    <view class="role-card my-role-card">
      <view class="role-header">
        <view class="role-avatar">
          <t-avatar size="large" image="{{myRole.avatar}}">
            {{myRole.name.charAt(0)}}
          </t-avatar>
        </view>
        <view class="role-info">
          <view class="role-name">{{myRole.name}}</view>
          <view class="role-title">{{myRole.title}}</view>
          <view class="role-tags">
            <t-tag theme="warning" size="small">{{myRole.difficulty}}</t-tag>
          </view>
        </view>
      </view>

      <view class="role-description">
        <view class="desc-title">角色背景</view>
        <view class="desc-content">{{myRole.background}}</view>
      </view>

      <view class="role-objectives">
        <view class="obj-title">胜利条件</view>
        <view class="obj-list">
          <view class="obj-item" wx:for="{{myRole.objectives}}" wx:key="index">
            <t-icon name="target" size="24rpx" color="#667eea" />
            <text>{{item}}</text>
          </view>
        </view>
      </view>

      <view class="role-secrets" wx:if="{{myRole.secrets.length > 0}}">
        <view class="secrets-title">
          <t-icon name="lock" size="24rpx" color="#ff6b6b" />
          <text>秘密信息</text>
        </view>
        <view class="secrets-list">
          <view class="secret-item" wx:for="{{myRole.secrets}}" wx:key="index">
            {{item}}
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 其他玩家角色 -->
  <view class="other-roles-section" wx:if="{{assignmentComplete}}">
    <view class="section-title">
      <t-icon name="team" size="32rpx" color="#667eea" />
      <text>其他玩家</text>
    </view>

    <view class="roles-grid">
      <view
        class="role-card other-role-card"
        wx:for="{{otherRoles}}"
        wx:key="id"
        bind:tap="viewRoleDetail"
        data-role-id="{{item.id}}"
      >
        <view class="role-avatar-small">
          <t-avatar size="medium" image="{{item.avatar}}">
            {{item.name.charAt(0)}}
          </t-avatar>
        </view>
        <view class="role-basic-info">
          <view class="role-name-small">{{item.name}}</view>
          <view class="role-player">{{item.playerName}}</view>
        </view>
        <view class="role-mystery">
          <t-icon name="help" size="32rpx" color="#999" />
        </view>
      </view>
    </view>
  </view>

  <!-- 角色关系提示 -->
  <view class="relationship-hint" wx:if="{{assignmentComplete && myRole.relationships.length > 0}}">
    <view class="hint-card">
      <view class="hint-title">
        <t-icon name="link" size="28rpx" color="#ffc107" />
        <text>角色关系提示</text>
      </view>
      <view class="hint-content">
        你与其他角色存在特殊关系，游戏中请注意观察和推理
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-actions">
    <t-button
      theme="default"
      size="large"
      bind:tap="viewCharacterRelations"
      class="secondary-btn"
      wx:if="{{assignmentComplete}}"
    >
      <t-icon name="share" slot="prefixIcon" />
      查看角色关系
    </t-button>
    <t-button
      theme="primary"
      size="large"
      bind:tap="startGame"
      loading="{{starting}}"
      disabled="{{!assignmentComplete}}"
      class="primary-btn"
    >
      {{starting ? '准备中...' : '开始游戏'}}
    </t-button>
  </view>
</view>

<!-- 角色详情弹窗 -->
<t-dialog
  id="role-detail-dialog"
  title="{{selectedRole.name}}"
  visible="{{showRoleDetail}}"
  bind:close="closeRoleDetail"
>
  <view class="role-detail-content" wx:if="{{selectedRole}}">
    <view class="detail-avatar">
      <t-avatar size="large" image="{{selectedRole.avatar}}">
        {{selectedRole.name.charAt(0)}}
      </t-avatar>
    </view>
    <view class="detail-info">
      <view class="detail-title">{{selectedRole.title}}</view>
      <view class="detail-player">扮演者: {{selectedRole.playerName}}</view>
      <view class="detail-desc">{{selectedRole.publicInfo}}</view>
    </view>
  </view>
</t-dialog>

<!-- 提示组件 -->
<t-toast id="t-toast" />