// 你画我猜小游戏页面逻辑
Page({
  data: {
    roomId: '',
    gameType: 'draw-guess',
    timeLeft: '06:00',
    isDrawer: true, // 当前用户是否是画家
    currentTopic: '神秘的钥匙',
    guessText: '',
    guessHistory: [
      { player: '玩家1', guess: '钥匙', correct: false },
      { player: '玩家2', guess: '门锁', correct: false },
      { player: '玩家3', guess: '神秘钥匙', correct: true }
    ],
    // 绘画相关
    canvasContext: null,
    isDrawing: false,
    lastX: 0,
    lastY: 0,
    brushSize: 3,
    brushColor: '#000000'
  },

  onLoad(options) {
    console.log('小游戏页面加载', options);
    if (options.roomId) {
      this.setData({ roomId: options.roomId });
    }
    if (options.type) {
      this.setData({ gameType: options.type });
    }

    // 初始化画布
    this.initCanvas();

    // 开始倒计时
    this.startTimer();
  },

  // 初始化画布
  initCanvas() {
    const canvasContext = wx.createCanvasContext('drawingCanvas');
    canvasContext.setStrokeStyle(this.data.brushColor);
    canvasContext.setLineWidth(this.data.brushSize);
    canvasContext.setLineCap('round');
    canvasContext.setLineJoin('round');
    this.setData({ canvasContext });
  },

  // 开始倒计时
  startTimer() {
    let totalSeconds = 10; // 10秒（测试用）

    this.timer = setInterval(() => {
      totalSeconds--;

      if (totalSeconds <= 0) {
        this.clearTimer();
        this.timeUp();
        return;
      }

      const minutes = Math.floor(totalSeconds / 60);
      const seconds = totalSeconds % 60;
      const timeLeft = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

      this.setData({ timeLeft });
    }, 1000);
  },

  // 清除计时器
  clearTimer() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },

  // 时间到
  timeUp() {
    wx.showModal({
      title: '游戏时间结束',
      content: '小游戏时间已结束，即将进入下一环节',
      showCancel: false,
      success: () => {
        this.proceedToNextStage();
      }
    });
  },

  // 开始绘画
  startDraw(e) {
    if (!this.data.isDrawer) return;

    const { x, y } = e.touches[0];
    this.setData({
      isDrawing: true,
      lastX: x,
      lastY: y
    });
  },

  // 绘画中
  drawing(e) {
    if (!this.data.isDrawer || !this.data.isDrawing) return;

    const { x, y } = e.touches[0];
    const { canvasContext, lastX, lastY } = this.data;

    canvasContext.beginPath();
    canvasContext.moveTo(lastX, lastY);
    canvasContext.lineTo(x, y);
    canvasContext.stroke();
    canvasContext.draw(true);

    this.setData({
      lastX: x,
      lastY: y
    });
  },

  // 结束绘画
  endDraw() {
    this.setData({ isDrawing: false });
  },

  // 清除画布
  clearCanvas() {
    if (!this.data.isDrawer) return;

    const { canvasContext } = this.data;
    canvasContext.clearRect(0, 0, 300, 200);
    canvasContext.draw();
  },

  // 改变画笔大小
  changeBrushSize() {
    const sizes = [1, 3, 5, 8];
    const currentIndex = sizes.indexOf(this.data.brushSize);
    const nextIndex = (currentIndex + 1) % sizes.length;
    const newSize = sizes[nextIndex];

    this.setData({ brushSize: newSize });
    this.data.canvasContext.setLineWidth(newSize);

    wx.showToast({
      title: `画笔大小: ${newSize}`,
      icon: 'none'
    });
  },

  // 改变颜色
  changeColor() {
    const colors = ['#000000', '#ff0000', '#00ff00', '#0000ff', '#ffff00'];
    const currentIndex = colors.indexOf(this.data.brushColor);
    const nextIndex = (currentIndex + 1) % colors.length;
    const newColor = colors[nextIndex];

    this.setData({ brushColor: newColor });
    this.data.canvasContext.setStrokeStyle(newColor);
  },

  // 猜测输入
  onGuessInput(e) {
    this.setData({ guessText: e.detail.value });
  },

  // 提交猜测
  submitGuess() {
    if (this.data.isDrawer || !this.data.guessText.trim()) return;

    const newGuess = {
      player: '我',
      guess: this.data.guessText,
      correct: this.data.guessText.includes('钥匙')
    };

    const guessHistory = [...this.data.guessHistory, newGuess];
    this.setData({
      guessHistory,
      guessText: ''
    });

    if (newGuess.correct) {
      wx.showToast({
        title: '恭喜猜对了！',
        icon: 'success'
      });

      setTimeout(() => {
        this.proceedToNextStage();
      }, 2000);
    }
  },

  // 显示游戏帮助
  showGameHelp() {
    wx.showModal({
      title: '游戏帮助',
      content: '画家需要根据题目画出相应内容，其他玩家通过观察画作进行猜测。猜对的玩家可以获得额外线索奖励。',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 跳过游戏
  skipGame() {
    wx.showModal({
      title: '跳过游戏',
      content: '确定要跳过当前小游戏吗？',
      success: (res) => {
        if (res.confirm) {
          this.proceedToNextStage();
        }
      }
    });
  },

  // 进入下一阶段
  proceedToNextStage() {
    this.clearTimer();

    // 跳转到下一轮讨论
    const nextRound = (this.data.round || 1) + 1;
    wx.redirectTo({
      url: `/pages/discussion/discussion?roomId=${this.data.roomId}&round=${nextRound}`
    });
  },

  onUnload() {
    this.clearTimer();
  }
})