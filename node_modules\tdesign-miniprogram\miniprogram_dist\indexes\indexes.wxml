<wxs src="../common/utils.wxs" module="_"/><wxs src="./indexes.wxs" module="_this"/><view style="{{_._style([style, customStyle])}}" class="{{classPrefix}} class {{prefix}}-class"><view class="{{classPrefix}}__sidebar {{prefix}}-class-sidebar" id="id-{{classPrefix}}__bar" catch:touchmove="onTouchMove" catch:touchcancel="onTouchCancel" catch:touchend="onTouchEnd"><view class="{{_.cls(classPrefix + '__sidebar-item', [['active', current === item]])}} {{prefix}}-class-sidebar-item" wx:for="{{ _indexList }}" wx:key="*this" data-current="{{item}}" bind:tap="onClick"><view aria-role="button" aria-label="{{ current === item ? '已选中' + item : ''}}">{{ _this.getFirstCharacter(item) }}</view><view class="{{classPrefix}}__sidebar-tips" wx:if="{{ showTips && current === item }}">{{ item }}</view></view></view><slot/></view>