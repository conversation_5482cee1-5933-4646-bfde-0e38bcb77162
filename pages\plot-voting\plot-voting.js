// 剧情分支投票页面逻辑
Page({
  data: {
    roomId: '',
    round: 1,
    timeLeft: '03:45',
    selectedOption: null,
    hasVoted: false,
    showStats: false,
    currentStory: '经过第一轮的讨论和投票，案情有了新的进展。在搜查过程中，你们发现了一封神秘的信件，信件中提到了一个重要的秘密。现在，你们需要决定下一步的调查方向...',
    plotOptions: [
      {
        icon: '🔍',
        title: '深入调查神秘访客',
        description: '专注调查当晚来访的神秘人物，追查他们的身份和动机',
        consequence: '可能发现重要线索，但也可能错过其他关键信息'
      },
      {
        icon: '🏠',
        title: '搜查庄园密室',
        description: '仔细搜查庄园中的隐秘房间，寻找隐藏的证据',
        consequence: '可能找到关键物证，但需要更多时间'
      },
      {
        icon: '📜',
        title: '分析遗嘱内容',
        description: '深入研究死者的遗嘱，分析受益人的动机',
        consequence: '可能揭露财产纠纷，但线索可能有限'
      }
    ],
    voteStats: [],
    totalVotes: 0,
    // 模拟其他玩家的投票数据
    otherPlayersVotes: [0, 0, 0] // 对应三个选项的票数
  },

  onLoad(options) {
    console.log('剧情分支页面加载', options);
    if (options.roomId) {
      this.setData({ roomId: options.roomId });
    }
    if (options.round) {
      this.setData({ round: parseInt(options.round) });
    }

    // 初始化投票统计
    this.initializeVoteStats();

    // 模拟其他玩家的投票（用于演示）
    this.simulateOtherPlayersVoting();

    // 开始倒计时
    this.startTimer();
  },

  // 开始倒计时
  startTimer() {
    let totalSeconds = 3 * 60 + 45; // 3分45秒

    this.timer = setInterval(() => {
      totalSeconds--;

      if (totalSeconds <= 0) {
        this.clearTimer();
        this.timeUp();
        return;
      }

      const minutes = Math.floor(totalSeconds / 60);
      const seconds = totalSeconds % 60;
      const timeLeft = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

      this.setData({ timeLeft });
    }, 1000);
  },

  // 清除计时器
  clearTimer() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },

  // 时间到
  timeUp() {
    if (!this.data.hasVoted) {
      wx.showModal({
        title: '投票时间结束',
        content: '投票时间已结束，将自动进入下一环节',
        showCancel: false,
        success: () => {
          this.proceedToNextStage();
        }
      });
    } else {
      this.proceedToNextStage();
    }
  },

  // 选择选项
  selectOption(e) {
    if (this.data.hasVoted) return;

    const index = e.currentTarget.dataset.index;
    this.setData({ selectedOption: index });
  },

  // 初始化投票统计
  initializeVoteStats() {
    const voteStats = this.data.plotOptions.map((option, index) => ({
      title: option.title,
      votes: this.data.otherPlayersVotes[index] || 0,
      percentage: 0
    }));

    this.setData({ voteStats });
    this.updateVotePercentages();

    console.log('初始化投票统计:', voteStats);
  },

  // 模拟其他玩家投票（用于演示效果）
  simulateOtherPlayersVoting() {
    // 初始时给一些基础票数，让统计更有意义
    const otherPlayersVotes = [1, 2, 1]; // 给每个选项一些初始票数
    this.setData({ otherPlayersVotes });
    this.initializeVoteStats();

    // 模拟其他玩家陆续投票
    this.simulateIncomingVotes();
  },

  // 模拟陆续到来的投票
  simulateIncomingVotes() {
    const totalSimulatedVotes = Math.floor(Math.random() * 4) + 3; // 3-6票
    let votesAdded = 0;

    const addVoteInterval = setInterval(() => {
      if (votesAdded >= totalSimulatedVotes) {
        clearInterval(addVoteInterval);
        return;
      }

      // 随机选择一个选项增加票数
      const randomOption = Math.floor(Math.random() * 3);
      const updatedStats = [...this.data.voteStats];
      updatedStats[randomOption].votes += 1;

      this.setData({ voteStats: updatedStats });
      this.updateVotePercentages();

      votesAdded++;
    }, Math.random() * 3000 + 2000); // 2-5秒间隔
  },

  // 更新投票百分比
  updateVotePercentages() {
    const totalVotes = this.data.voteStats.reduce((sum, stat) => sum + stat.votes, 0);

    console.log('更新百分比 - 总票数:', totalVotes, '统计数据:', this.data.voteStats);

    if (totalVotes === 0) {
      // 即使没有票数，也要设置totalVotes为0
      this.setData({ totalVotes: 0 });
      return;
    }

    const updatedStats = this.data.voteStats.map(stat => ({
      ...stat,
      percentage: Math.round((stat.votes / totalVotes) * 100)
    }));

    console.log('更新后的统计:', updatedStats);

    this.setData({
      voteStats: updatedStats,
      totalVotes: totalVotes
    });
  },

  // 提交投票
  submitVote() {
    if (this.data.selectedOption === null || this.data.hasVoted) return;

    wx.showModal({
      title: '确认投票',
      content: `确定选择"${this.data.plotOptions[this.data.selectedOption].title}"吗？`,
      success: (res) => {
        if (res.confirm) {
          // 更新选中选项的票数
          const updatedStats = [...this.data.voteStats];
          updatedStats[this.data.selectedOption].votes += 1;

          console.log('投票前统计:', this.data.voteStats);
          console.log('投票后统计:', updatedStats);

          this.setData({
            hasVoted: true,
            showStats: true,
            voteStats: updatedStats
          }, () => {
            console.log('设置完成后的状态:', {
              hasVoted: this.data.hasVoted,
              showStats: this.data.showStats,
              voteStats: this.data.voteStats
            });
          });

          // 重新计算百分比
          this.updateVotePercentages();

          wx.showToast({
            title: '投票成功',
            icon: 'success'
          });

          // 8秒后自动进入下一阶段
          setTimeout(() => {
            this.proceedToNextStage();
          }, 8000);
        }
      }
    });
  },

  // 显示剧情详情
  showPlotDetails() {
    const selectedOption = this.data.plotOptions[this.data.selectedOption];
    const content = selectedOption ?
      `${selectedOption.description}\n\n${selectedOption.consequence}` :
      '请先选择一个剧情分支选项';

    wx.showModal({
      title: '剧情详情',
      content: content,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 调试显示统计
  debugShowStats() {
    console.log('调试信息:', {
      hasVoted: this.data.hasVoted,
      showStats: this.data.showStats,
      voteStats: this.data.voteStats,
      totalVotes: this.data.totalVotes,
      selectedOption: this.data.selectedOption
    });

    // 强制显示统计
    this.setData({
      showStats: true,
      hasVoted: true
    });

    wx.showModal({
      title: '调试信息',
      content: `统计数据: ${JSON.stringify(this.data.voteStats)}`,
      showCancel: false
    });
  },

  // 进入下一阶段
  proceedToNextStage() {
    this.clearTimer();

    // 跳转到淘汰投票页面
    wx.redirectTo({
      url: `/pages/voting/voting?roomId=${this.data.roomId}&round=${this.data.round || 1}`
    });
  },

  onUnload() {
    this.clearTimer();
  }
})