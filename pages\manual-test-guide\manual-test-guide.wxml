<!--手动测试指南页面-->
<view class="container">
  <view class="header">
    <text class="title">📋 手动测试指南</text>
    <text class="subtitle">逐步验证准备开始游戏功能</text>
  </view>

  <!-- 进度指示器 -->
  <view class="progress-indicator">
    <view class="step-counter">
      步骤 {{currentStep + 1}} / {{steps.length}}
    </view>
    <view class="progress-dots">
      <view 
        class="dot {{index <= currentStep ? 'active' : ''}}" 
        wx:for="{{steps}}" 
        wx:key="index"
      ></view>
    </view>
  </view>

  <!-- 当前步骤 -->
  <view class="current-step">
    <view class="step-header">
      <text class="step-title">{{steps[currentStep].title}}</text>
    </view>
    <text class="step-description">{{steps[currentStep].description}}</text>
    <view class="expected-result">
      <text class="expected-label">预期结果:</text>
      <text class="expected-text">{{steps[currentStep].expected}}</text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button 
      class="action-btn primary" 
      bind:tap="executeStep"
      disabled="{{currentStep >= steps.length}}"
    >
      {{currentStep >= steps.length ? '测试完成' : '执行步骤'}}
    </button>
    
    <button 
      class="action-btn secondary" 
      bind:tap="showRoomStatus"
      disabled="{{!roomId}}"
    >
      查看房间状态
    </button>
  </view>

  <!-- 导航按钮 -->
  <view class="navigation">
    <button 
      class="nav-btn" 
      bind:tap="prevStep"
      disabled="{{currentStep === 0}}"
    >
      ← 上一步
    </button>
    
    <button 
      class="nav-btn" 
      bind:tap="nextStep"
      disabled="{{currentStep >= steps.length - 1}}"
    >
      下一步 →
    </button>
  </view>

  <!-- 测试信息 -->
  <view class="test-info">
    <view class="info-item">
      <text class="info-label">房间ID:</text>
      <text class="info-value">{{roomId || '未创建'}}</text>
    </view>
    <view class="info-item">
      <text class="info-label">测试玩家:</text>
      <text class="info-value">{{testUsers.length}}人</text>
    </view>
  </view>

  <!-- 重置按钮 -->
  <view class="reset-section">
    <button class="reset-btn" bind:tap="resetTest">
      🔄 重置测试
    </button>
  </view>

  <!-- 测试说明 -->
  <view class="test-instructions">
    <text class="instructions-title">💡 测试说明</text>
    <view class="instructions-list">
      <text class="instruction-item">• 按顺序执行每个步骤</text>
      <text class="instruction-item">• 观察每步的预期结果</text>
      <text class="instruction-item">• 可随时查看房间状态</text>
      <text class="instruction-item">• 最后一步会进入房间验证</text>
    </view>
  </view>
</view>
