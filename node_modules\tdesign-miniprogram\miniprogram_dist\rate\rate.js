import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import{unitConvert,getRect}from"../common/utils";const{prefix:prefix}=config,name=`${prefix}-rate`;let Rate=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`,`${prefix}-class-icon`,`${prefix}-class-text`],this.properties=props,this.controlledProps=[{key:"value",event:"change"}],this.data={prefix:prefix,classPrefix:name,defaultTexts:["极差","失望","一般","满意","惊喜"],tipsVisible:!1,tipsLeft:0,actionType:"",scaleIndex:-1,isVisibleToScreenReader:!1},this.methods={onTouch(e,t){const{count:i,allowHalf:s,gap:o,value:a,size:n}=this.properties,[r]=e.changedTouches,c=unitConvert(o);getRect(this,`.${name}__wrapper`).then(e=>{const{width:p,left:h}=e,l=(p-(i-1)*c)/i,d=(r.pageX-h+c)/(l+c),u=d%1,m=d-u;let T=u<=.5&&s?m+.5:m+1;T>i?T=i:T<0&&(T=0);const x=Math.ceil(T-1)*(unitConvert(o)+unitConvert(n))+.5*unitConvert(n);this.setData({tipsVisible:!0,actionType:t,scaleIndex:Math.ceil(T),tipsLeft:Math.max(x,0)}),T!==a&&this._trigger("change",{value:T}),this.touchEnd&&this.hideTips()})},onTap(e){const{disabled:t}=this.properties;t||this.onTouch(e,"tap")},onTouchStart(){this.touchEnd=!1},onTouchMove(e){this.onTouch(e,"move"),this.showAlertText()},onTouchEnd(){this.touchEnd=!0,this.hideTips()},hideTips(){"move"===this.data.actionType&&this.setData({tipsVisible:!1,scaleIndex:-1})},onSelect(e){const{value:t}=e.currentTarget.dataset,{actionType:i}=this.data;"move"!==i&&(this._trigger("change",{value:t}),setTimeout(()=>this.setData({tipsVisible:!1,scaleIndex:-1}),300))},showAlertText(){!0!==this.data.isVisibleToScreenReader&&(this.setData({isVisibleToScreenReader:!0}),setTimeout(()=>{this.setData({isVisibleToScreenReader:!1})},2e3))}}}};Rate=__decorate([wxComponent()],Rate);export default Rate;