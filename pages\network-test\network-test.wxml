<!-- pages/network-test/network-test.wxml -->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">🔍 网络诊断工具</text>
    <text class="subtitle">检测AI服务连接状态</text>
  </view>

  <!-- 连接状态 -->
  <view class="status-card">
    <view class="status-header">
      <text class="status-title">📶 当前网络状态</text>
    </view>
    <view class="status-content">
      <text class="status-value {{connectionStatus === 'none' ? 'status-error' : 'status-success'}}">
        {{connectionStatus === 'wifi' ? 'WiFi连接' : connectionStatus === 'none' ? '无网络' : connectionStatus}}
      </text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button 
      class="action-btn primary" 
      bindtap="runFullDiagnostic"
      disabled="{{isRunning}}"
    >
      {{isRunning ? '诊断中...' : '🔍 运行完整诊断'}}
    </button>
    
    <button
      class="action-btn secondary"
      bindtap="testDomainConfig"
      disabled="{{isRunning}}"
    >
      {{isRunning ? '检查中...' : '🔧 检查域名配置'}}
    </button>

    <button
      class="action-btn secondary"
      bindtap="testSimpleApi"
      disabled="{{isRunning}}"
    >
      {{isRunning ? '测试中...' : '🔬 简单API测试'}}
    </button>

    <button
      class="action-btn secondary"
      bindtap="testSimpleRequest"
      disabled="{{isRunning}}"
    >
      {{isRunning ? '测试中...' : '🧪 测试AI请求'}}
    </button>

    <button
      class="action-btn secondary"
      bindtap="testJsonParsing"
      disabled="{{isRunning}}"
    >
      {{isRunning ? '测试中...' : '🔧 JSON解析测试'}}
    </button>

    <button
      class="action-btn tertiary"
      bindtap="clearResults"
      disabled="{{isRunning}}"
    >
      🗑️ 清除结果
    </button>
  </view>

  <!-- 测试结果 -->
  <view class="results-section" wx:if="{{testResults.length > 0}}">
    <view class="section-header">
      <text class="section-title">📋 测试结果</text>
    </view>
    
    <view class="test-results">
      <view 
        class="test-item {{item.success ? 'test-success' : 'test-error'}}"
        wx:for="{{testResults}}" 
        wx:key="index"
      >
        <view class="test-header">
          <text class="test-name">{{item.success ? '✅' : '❌'}} {{item.name}}</text>
          <text class="test-time">{{item.timestamp}}</text>
        </view>
        <text class="test-message">{{item.message}}</text>
      </view>
    </view>
  </view>

  <!-- 诊断结果 -->
  <view class="diagnostic-section" wx:if="{{diagnosticResults}}">
    <view class="section-header">
      <text class="section-title">🔬 诊断结果</text>
    </view>
    
    <!-- 网络状态 -->
    <view class="diagnostic-card">
      <view class="card-header">
        <text class="card-title">📶 网络状态</text>
      </view>
      <view class="card-content">
        <text class="info-text">类型: {{diagnosticResults.networkStatus.networkType}}</text>
        <text class="info-text">状态: {{diagnosticResults.networkStatus.isConnected ? '已连接' : '未连接'}}</text>
      </view>
    </view>

    <!-- 域名可达性 -->
    <view class="diagnostic-card">
      <view class="card-header">
        <text class="card-title">🌐 域名可达性</text>
      </view>
      <view class="card-content">
        <view 
          class="domain-item"
          wx:for="{{diagnosticResults.domainReachability}}" 
          wx:for-item="result"
          wx:for-index="domain"
          wx:key="domain"
        >
          <text class="domain-status {{result.reachable ? 'status-success' : 'status-error'}}">
            {{result.reachable ? '✅' : '❌'}}
          </text>
          <text class="domain-name">{{domain}}</text>
          <text class="domain-time" wx:if="{{result.responseTime}}">{{result.responseTime}}ms</text>
        </view>
      </view>
    </view>

    <!-- API端点 -->
    <view class="diagnostic-card">
      <view class="card-header">
        <text class="card-title">🔌 API端点</text>
      </view>
      <view class="card-content">
        <text class="info-text">
          状态: {{diagnosticResults.apiEndpoint.reachable ? '✅ 可达' : '❌ 不可达'}}
        </text>
        <text class="info-text" wx:if="{{diagnosticResults.apiEndpoint.statusCode}}">
          状态码: {{diagnosticResults.apiEndpoint.statusCode}}
        </text>
        <text class="info-text" wx:if="{{diagnosticResults.apiEndpoint.responseTime}}">
          响应时间: {{diagnosticResults.apiEndpoint.responseTime}}ms
        </text>
      </view>
    </view>

    <!-- 建议 -->
    <view class="diagnostic-card" wx:if="{{diagnosticResults.recommendations.length > 0}}">
      <view class="card-header">
        <text class="card-title">💡 建议</text>
      </view>
      <view class="card-content">
        <view 
          class="recommendation-item"
          wx:for="{{diagnosticResults.recommendations}}" 
          wx:key="index"
        >
          <text class="rec-icon">
            {{item.type === 'critical' ? '🚨' : item.type === 'warning' ? '⚠️' : 'ℹ️'}}
          </text>
          <view class="rec-content">
            <text class="rec-title">{{item.title}}</text>
            <text class="rec-description">{{item.description}}</text>
            <text class="rec-action">建议: {{item.action}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="diagnostic-actions">
      <button class="action-btn secondary" bindtap="viewRecommendations">
        📋 查看详细建议
      </button>
      <button class="action-btn tertiary" bindtap="copyDiagnosticResults">
        📋 复制诊断结果
      </button>
    </view>
  </view>

  <!-- 帮助信息 -->
  <view class="help-section">
    <view class="section-header">
      <text class="section-title">❓ 常见问题</text>
    </view>
    
    <view class="help-content">
      <view class="help-item">
        <text class="help-question">Q: 网络连接失败怎么办？</text>
        <text class="help-answer">A: 检查网络连接，确保设备联网正常，尝试切换WiFi或移动数据。</text>
      </view>
      
      <view class="help-item">
        <text class="help-question">Q: API认证失败怎么办？</text>
        <text class="help-answer">A: 检查API密钥是否正确，确认密钥未过期且有足够的调用额度。</text>
      </view>
      
      <view class="help-item">
        <text class="help-question">Q: 域名不可达怎么办？</text>
        <text class="help-answer">A: 确保在微信小程序后台配置了合法域名，添加 https://api.moonshot.cn。</text>
      </view>
    </view>
  </view>
</view>
