<!--创建房间页面-->
<view class="create-room-container">
  <!-- 返回按钮 -->
  <view class="back-button" bind:tap="goBack">
    <text class="back-icon">←</text>
    <text class="back-text">返回</text>
  </view>

  <!-- 页面标题 -->
  <view class="page-header">
    <view class="page-title">创建房间</view>
    <view class="page-subtitle">设置您的专属游戏房间</view>
  </view>

  <view class="form-section">
    <!-- 房间设置卡片 -->
    <view class="setting-card">
      <view class="card-title">房间设置</view>

      <view class="form-group">
        <view class="form-label">房间名称</view>
        <input
          placeholder="请输入房间名称"
          value="{{formData.roomName}}"
          bindinput="onRoomNameChange"
          maxlength="20"
          class="form-input"
        />
      </view>

      <view class="form-group">
        <view class="form-label">房间密码（可选）</view>
        <input
          placeholder="设置房间密码"
          value="{{formData.password}}"
          bindinput="onPasswordChange"
          password="{{true}}"
          maxlength="10"
          class="form-input"
        />
      </view>

      <view class="form-group">
        <view class="form-label">最大人数: {{formData.maxPlayers}}人</view>
        <view class="slider-container">
          <text class="slider-min">2人</text>
          <slider
            class="player-slider"
            min="1"
            max="12"
            value="{{formData.maxPlayers}}"
            bind:change="onMaxPlayersChange"
            activeColor="#667eea"
            backgroundColor="#e6e6e6"
            block-color="#667eea"
            block-size="28"
            show-value="{{false}}"
          />
          <text class="slider-max">12人</text>
        </view>
      </view>

      <view class="form-group">
        <view class="form-label">游戏风格</view>
        <picker
          mode="selector"
          range="{{styleOptions}}"
          range-key="label"
          value="{{styleSelectedIndex}}"
          bind:change="onGameStyleChange"
          class="form-picker"
        >
          <view class="picker-display">
            {{styleOptions[styleSelectedIndex].label}}
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <view class="form-group">
        <view class="form-label">难度等级</view>
        <picker
          mode="selector"
          range="{{difficultyOptions}}"
          range-key="label"
          value="{{difficultySelectedIndex}}"
          bind:change="onDifficultyChange"
          class="form-picker"
        >
          <view class="picker-display">
            {{difficultyOptions[difficultySelectedIndex].label}}
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <view class="form-group">
        <view class="form-label">游戏特色</view>
        <view class="features-display">
          <view class="feature-tag" wx:for="{{gameFeatures}}" wx:key="key">
            <text class="feature-icon" style="color: {{item.color}}">●</text>
            <text>{{item.label}}</text>
          </view>
        </view>
        <view class="features-desc">所有特色功能已为您开启，带来最佳游戏体验</view>
      </view>

      <button
        type="primary"
        bindtap="createRoom"
        disabled="{{creating}}"
        class="create-btn"
      >
        {{creating ? '创建中...' : '创建房间'}}
      </button>
    </view>


  </view>
</view>

<!-- 使用原生提示，移除可能导致Promise错误的组件 -->
