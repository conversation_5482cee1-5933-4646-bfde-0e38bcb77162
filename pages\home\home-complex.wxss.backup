/* 地球星空背景首页 */
.home-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #000428 0%, #004e92 100%);
  padding: 0;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 地球元素 */
.home-container::before {
  content: '';
  position: absolute;
  bottom: -100rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 400rpx;
  height: 400rpx;
  background: radial-gradient(circle, rgba(135, 206, 235, 0.7) 0%, rgba(65, 105, 225, 0.8) 70%, rgba(25, 25, 112, 0.9) 100%);
  border-radius: 50%;
  box-shadow: 0 0 100rpx rgba(135, 206, 235, 0.3);
  z-index: 1;
}

/* 星星装饰 */
.stars {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.star {
  position: absolute;
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
  animation: twinkle 3s ease-in-out infinite;
}

.star-1 {
  top: 15%;
  left: 20%;
  animation-delay: 0s;
}

.star-2 {
  top: 25%;
  right: 25%;
  animation-delay: 1s;
}

.star-3 {
  top: 40%;
  left: 15%;
  animation-delay: 2s;
}

.star-4 {
  top: 60%;
  right: 20%;
  animation-delay: 0.5s;
}

.star-5 {
  top: 80%;
  left: 30%;
  animation-delay: 1.5s;
}

/* 背景遮罩层 - 确保文字可读性 */
.home-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 1;
}

/* 极简主标题区域 */
.hero-section {
  text-align: center;
  padding: 200rpx 40rpx 120rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 2;
}

/* 应用Logo */
.app-logo {
  margin-bottom: 32rpx;
}

.logo-text {
  font-size: 64rpx;
  font-weight: 300;
  color: #ffffff;
  letter-spacing: 2rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'PingFang SC', sans-serif;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
}

/* 应用副标题 */
.app-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
  letter-spacing: 1rpx;
  margin-bottom: 80rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 极简主操作按钮 */
.main-actions {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  padding: 0 40rpx;
  margin-bottom: 80rpx;
  position: relative;
  z-index: 2;
}

.minimal-btn {
  width: 100%;
  height: 96rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
}

.primary-btn {
  background: #000000;
  color: #ffffff;
}

.primary-btn:active {
  background: #333333;
  transform: scale(0.98);
}

.secondary-btn {
  background: #f5f5f5;
  color: #000000;
  border: 1rpx solid #e0e0e0;
}

.secondary-btn:active {
  background: #e8e8e8;
  transform: scale(0.98);
}

/* 快速加入区域 */
.quick-join-section {
  display: flex;
  gap: 16rpx;
  padding: 0 40rpx;
  margin-bottom: 60rpx;
  position: relative;
  z-index: 2;
}

.minimal-input {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  background: rgba(255, 255, 255, 0.9);
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.minimal-input:focus {
  border-color: #000000;
}

.join-btn {
  width: 120rpx;
  height: 80rpx;
  background: #000000;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
}

.join-btn:active {
  background: #333333;
  transform: scale(0.98);
}

.join-btn.disabled {
  background: #cccccc;
  color: #999999;
}

/* 房间列表 */
.rooms-section {
  padding: 0 40rpx;
  margin-bottom: 60rpx;
  position: relative;
  z-index: 2;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 32rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'PingFang SC', sans-serif;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
}

.rooms-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.room-card {
  background: rgba(255, 255, 255, 0.95);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s ease;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

.room-card:active {
  background: rgba(255, 255, 255, 0.9);
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.3);
}

.room-info {
  flex: 1;
}

.room-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #000000;
  margin-bottom: 8rpx;
  display: block;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
}

.room-players {
  font-size: 24rpx;
  color: #666666;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
}

.room-status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-weight: 500;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
}

.room-status.waiting {
  background: #e8f5e8;
  color: #2e7d32;
}

.room-status.playing {
  background: #fff3e0;
  color: #f57c00;
}

/* 开发测试区域 */
.dev-test-section {
  padding: 0 40rpx;
  margin-bottom: 60rpx;
  position: relative;
  z-index: 2;
}

.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.test-btn {
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.9);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
  transition: all 0.2s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.test-btn:active {
  background: rgba(255, 255, 255, 0.8);
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.test-btn.danger {
  background: #ffebee;
  color: #d32f2f;
  border-color: #ffcdd2;
}

.test-btn.danger:active {
  background: #ffcdd2;
}

/* 开发测试切换按钮 */
.dev-toggle-btn {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  width: 120rpx;
  height: 80rpx;
  background: #000000;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
  z-index: 1000;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
}

.dev-toggle-btn:active {
  background: #333333;
  transform: scale(0.95);
}

/* 加载状态 */
.loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 32rpx 48rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  z-index: 9999;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
}
  color: rgba(255,255,255,0.6);
  font-size: 24rpx;
  font-weight: 400;
  margin-bottom: 48rpx;
  letter-spacing: 1rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
}

.table-3d {
  position: relative;
  width: 320rpx;
  height: 200rpx;
  margin: 0 auto;
}

.table-surface {
  position: absolute;
  top: 80rpx;
  left: 40rpx;
  right: 40rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #2a2a2a 0%, #3a3a3a 50%, #2a2a2a 100%);
  border-radius: 50%;
  box-shadow:
    0 16rpx 48rpx rgba(0,0,0,0.4),
    inset 0 2rpx 8rpx rgba(255,255,255,0.05);
  transform: perspective(400rpx) rotateX(35deg);
}

.mystery-light {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: radial-gradient(circle,
    rgba(120,119,198,0.4) 0%,
    rgba(120,119,198,0.2) 50%,
    transparent 100%);
  animation: mysteryPulse 4s ease-in-out infinite;
}

.ambient-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(1rpx 1rpx at 80rpx 60rpx, rgba(255,255,255,0.2), transparent),
    radial-gradient(1rpx 1rpx at 240rpx 100rpx, rgba(120,119,198,0.15), transparent),
    radial-gradient(1rpx 1rpx at 160rpx 140rpx, rgba(255,255,255,0.1), transparent);
  animation: ambientFloat 8s ease-in-out infinite;
}

/* 欢迎卡片 */
.welcome-card {
  padding: 60rpx 40rpx;
  text-align: center;
  position: relative;
  z-index: 1;
  margin-bottom: 40rpx;
}

/* 游戏Logo */
.game-logo {
  margin-bottom: 32rpx;
  display: flex;
  justify-content: center;
}

.logo-icon {
  font-size: 120rpx;
  display: inline-block;
  filter: drop-shadow(0 8rpx 16rpx rgba(102, 126, 234, 0.3));
}

/* 霓虹文字效果 */
.neon-text {
  text-shadow:
    0 0 10rpx currentColor,
    0 0 20rpx currentColor,
    0 0 30rpx currentColor,
    0 0 40rpx currentColor;
}

/* 欢迎标题 */
.welcome-title {
  font-size: 56rpx;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 20rpx;
  text-align: center;
  letter-spacing: 2rpx;
}

.welcome-subtitle {
  font-size: 32rpx;
  color: #555;
  line-height: 1.6;
  margin-bottom: 12rpx;
  text-align: center;
  font-weight: 500;
}

.welcome-features {
  font-size: 26rpx;
  color: #667eea;
  font-weight: 600;
  margin-bottom: 48rpx;
  text-align: center;
  letter-spacing: 4rpx;
  opacity: 0.8;
}

/* 按钮区域 */
.action-buttons {
  display: flex;
  gap: 24rpx;
  margin-top: 40rpx;
}

/* 艺术化按钮 */
.artistic-btn {
  flex: 1;
  border: none;
  border-radius: 32rpx;
  padding: 24rpx 32rpx;
  font-size: 32rpx;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
}

.artistic-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.artistic-btn:hover::before {
  left: 100%;
}

.artistic-btn:active {
  transform: translateY(4rpx) scale(0.98);
}

/* 主要按钮 */
.primary-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 16rpx 48rpx rgba(102, 126, 234, 0.4);
}

/* 次要按钮 */
.secondary-btn {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
  color: white;
  box-shadow: 0 16rpx 48rpx rgba(78, 205, 196, 0.4);
}

.btn-content {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.btn-icon {
  font-size: 36rpx;
}

.btn-text {
  font-size: 32rpx;
  font-weight: 600;
}

/* 房间列表区域 */
.rooms-section {
  padding: 0 32rpx 40rpx;
  position: relative;
  z-index: 1;
}

.section-header {
  margin-bottom: 32rpx;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 16rpx;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.section-subtitle {
  font-size: 24rpx;
  color: #666;
  margin-left: 56rpx;
}

.rooms-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.room-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 32rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.room-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  opacity: 0.1;
  transition: opacity 0.3s ease;
}

.room-playing::before {
  background: radial-gradient(circle, #52c41a 0%, transparent 70%);
}

.room-waiting::before {
  background: radial-gradient(circle, #faad14 0%, transparent 70%);
}

.room-card:active {
  transform: scale(0.98);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}

.room-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.room-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.room-info {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.room-tags {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

/* 快速加入区域 */
.quick-join-section {
  padding: 0 32rpx 40rpx;
  position: relative;
  z-index: 1;
}

.quick-join-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.quick-join-content {
  display: flex;
  gap: 24rpx;
  align-items: center;
  padding: 32rpx;
}

.room-id-input {
  flex: 1;
}

.quick-join-btn {
  flex-shrink: 0;
  font-size: 28rpx !important;
  font-weight: 500 !important;
  min-width: 140rpx !important;
}

/* 动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0rpx);
  }
  50% {
    transform: translateY(-20rpx);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 40rpx rgba(102, 126, 234, 0.5);
  }
  50% {
    box-shadow: 0 0 60rpx rgba(102, 126, 234, 0.8);
  }
}

@keyframes shimmer {
  0% {
    background-position: -400rpx 0;
  }
  100% {
    background-position: 400rpx 0;
  }
}

/* 动画类 */
.float-effect {
  animation: float 3s ease-in-out infinite;
}

.fade-in {
  animation: fadeIn 0.8s ease-out;
}

.glow-effect {
  animation: glow 2s ease-in-out infinite;
}

.shimmer-effect {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 400rpx 100%;
  animation: shimmer 2s infinite;
}

/* 现代卡片基础样式 */
.artistic-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.08);
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 现代房间区域样式 */
.rooms-section {
  margin-bottom: 40rpx;
  padding: 40rpx;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.section-icon {
  width: 64rpx;
  height: 64rpx;
  background: linear-gradient(135deg, #7c77c6, #5a67d8);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(120,119,198,0.25);
}

.section-info {
  flex: 1;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  display: block;
  margin-bottom: 8rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
}

.section-subtitle {
  font-size: 22rpx;
  color: rgba(255,255,255,0.6);
  display: block;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
}

/* 房间列表 */
.rooms-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* 现代房间卡片 */
.artistic-room-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.08);
  border-radius: 20rpx;
  padding: 24rpx;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

.artistic-room-card:active {
  transform: translateY(2rpx) scale(0.98);
  background: rgba(255, 255, 255, 0.08);
}

.room-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 80rpx;
  height: 80rpx;
  background: radial-gradient(circle, rgba(120,119,198, 0.1) 0%, transparent 70%);
  border-radius: 50%;
}

.room-content {
  position: relative;
  z-index: 1;
}

.room-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.room-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
}

.room-status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
}

.status-playing {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1rpx solid rgba(34, 197, 94, 0.3);
}

.status-waiting {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
  border: 1rpx solid rgba(251, 191, 36, 0.3);
}

.room-info {
  font-size: 22rpx;
  color: rgba(255,255,255,0.6);
  margin-bottom: 12rpx;
  display: block;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
}

.room-tags {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

.tag {
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.difficulty-easy {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.difficulty-medium {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
}

.difficulty-hard {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
}

.voice-tag {
  background: rgba(33, 150, 243, 0.2);
  color: #2196f3;
}

.ai-tag {
  background: rgba(156, 39, 176, 0.2);
  color: #9c27b0;
}

/* 快速加入区域 */
.quick-join-section {
  margin-bottom: 40rpx;
  padding: 40rpx;
}

.quick-join-icon {
  background: linear-gradient(45deg, #ffc107, #ff8f00) !important;
}

.quick-join-content {
  display: flex;
  gap: 24rpx;
  align-items: center;
}

/* 现代输入框 */
.artistic-input {
  flex: 1;
  background: rgba(255, 255, 255, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.12);
  border-radius: 16rpx;
  padding: 20rpx 24rpx;
  font-size: 26rpx;
  color: #ffffff;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
}

.artistic-input:focus {
  border-color: rgba(120,119,198, 0.4);
  background: rgba(255, 255, 255, 0.12);
  box-shadow: 0 8rpx 24rpx rgba(120,119,198, 0.2);
}

.quick-join-btn {
  flex-shrink: 0;
  background: linear-gradient(135deg, #7c77c6 0%, #5a67d8 100%);
  color: white;
  min-width: 140rpx;
  box-shadow: 0 8rpx 24rpx rgba(120,119,198, 0.3);
}

.quick-join-btn.disabled {
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  opacity: 0.5;
}

/* 开发者测试区域 */
.dev-test-section {
  margin-bottom: 40rpx;
  padding: 40rpx;
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(255, 140, 22, 0.1) 100%) !important;
  border: 2rpx solid rgba(255, 107, 107, 0.2) !important;
}

.dev-icon {
  background: linear-gradient(45deg, #ff6b6b, #ee5a52) !important;
}

.test-buttons-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

/* 测试按钮 */
.test-btn {
  background: rgba(255, 255, 255, 0.8);
  border: 2rpx solid rgba(102, 126, 234, 0.3);
  border-radius: 20rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.05);
}

.test-btn:active {
  transform: translateY(2rpx) scale(0.98);
  background: rgba(102, 126, 234, 0.1);
}

.primary-test {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  color: white;
  box-shadow: 0 12rpx 32rpx rgba(102, 126, 234, 0.3) !important;
}

.primary-test .test-btn-text {
  color: white;
}

.danger-test {
  background: rgba(255, 107, 107, 0.1) !important;
  border-color: rgba(255, 107, 107, 0.3) !important;
}

.danger-test .test-btn-text {
  color: #ff6b6b;
}

.test-btn-icon {
  font-size: 32rpx;
}

.test-btn-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #667eea;
}

/* 浮动测试按钮 */
.floating-test-btn {
  position: fixed;
  bottom: 60rpx;
  right: 60rpx;
  z-index: 100;
}

.show-test-btn {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 16rpx 48rpx rgba(102, 126, 234, 0.4);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.show-test-btn:active {
  transform: scale(0.9);
}

.floating-icon {
  font-size: 48rpx;
  color: white;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .home-container {
    padding: 32rpx 24rpx;
  }

  .welcome-card {
    padding: 48rpx 32rpx;
  }

  .action-buttons {
    flex-direction: column;
  }

  .artistic-btn {
    width: 100%;
  }

  .quick-join-content {
    flex-direction: column;
  }

  .artistic-input {
    margin-bottom: 16rpx;
  }

  .test-buttons-grid {
    grid-template-columns: 1fr;
  }
}

/* 加载状态 */
.loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 32rpx 48rpx;
  border-radius: 24rpx;
  z-index: 9999;
  backdrop-filter: blur(10rpx);
  font-size: 28rpx;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.3);
}

/* 滚动优化 */
.home-container {
  scroll-behavior: smooth;
}

/* 触摸反馈优化 */
.artistic-card:active,
.artistic-btn:active,
.artistic-room-card:active {
  transition: all 0.1s ease;
}

/* 无障碍优化 */
.artistic-btn:focus,
.test-btn:focus,
.show-test-btn:focus {
  outline: 4rpx solid rgba(102, 126, 234, 0.5);
  outline-offset: 4rpx;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .home-container {
    background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
  }

  .artistic-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-color: rgba(255, 255, 255, 0.2);
  }

  .section-title {
    color: #fff;
  }

  .section-subtitle {
    color: #ccc;
  }

  .room-name {
    color: #fff;
  }

  .room-info {
    color: #ccc;
  }
}

/* 加载状态 */
.t-loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
}

/* 动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

/* 现代微妙动画效果 */
@keyframes twinkle {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.7; }
}

@keyframes warmGlow {
  0% { opacity: 0.6; transform: scale(1); }
  100% { opacity: 0.8; transform: scale(1.05); }
}

@keyframes floatParticles {
  0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.2; }
  25% { transform: translateY(-10rpx) rotate(90deg); opacity: 0.3; }
  50% { transform: translateY(-5rpx) rotate(180deg); opacity: 0.25; }
  75% { transform: translateY(-15rpx) rotate(270deg); opacity: 0.35; }
}

@keyframes logoFloat {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-4rpx); }
}

@keyframes mysteryPulse {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.5;
    transform: translate(-50%, -50%) scale(1.05);
  }
}

@keyframes ambientFloat {
  0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.2; }
  33% { transform: translateY(-8rpx) rotate(120deg); opacity: 0.3; }
  66% { transform: translateY(-4rpx) rotate(240deg); opacity: 0.25; }
}

@keyframes btnBgShift {
  0%, 100% { transform: translateX(-100%) skewX(-15deg); }
  50% { transform: translateX(100%) skewX(-15deg); }
}

@keyframes btnShine {
  0% { left: -100%; }
  50% { left: -50%; }
  100% { left: 100%; }
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 4rpx 12rpx rgba(255,215,0,0.5));
  }
  50% {
    transform: scale(1.1);
    filter: drop-shadow(0 6rpx 16rpx rgba(255,215,0,0.8));
  }
}

@keyframes iconGlow {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

@keyframes logoRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes raysRotate {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(-360deg); }
}

@keyframes decorationPulse {
  0%, 100% {
    transform: scale(1);
    background: #00d4aa;
  }
  50% {
    transform: scale(1.1);
    background: #00c4a0;
  }
}

/* 椭圆形扁平按钮 */
.main-actions {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  padding: 0 40rpx 80rpx;
  position: relative;
  z-index: 10;
}

/* 内凹按钮效果 - 模拟图片中的凹陷效果 */
.artistic-btn {
  background: none;
  border: none;
  padding: 0;
  border-radius: 80rpx;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: translateY(0);
  width: 320rpx;
  height: 96rpx;
  margin: 0 auto;
  /* 强烈的内凹效果 - 模拟按钮被按下的状态 */
  background: linear-gradient(145deg, #d0d0d0, #f0f0f0);
  box-shadow:
    /* 强烈的内部阴影 - 创造深度凹陷感 */
    inset 12rpx 12rpx 24rpx #b8b8b8,
    inset -12rpx -12rpx 24rpx #ffffff,
    /* 轻微的外部阴影 - 保持边界清晰 */
    2rpx 2rpx 8rpx rgba(0,0,0,0.1);
  border: 1rpx solid rgba(0,0,0,0.1);
}

.create-btn {
  /* 保持iOS风格但添加轻微的色彩 */
  background: linear-gradient(145deg, #f0f0f0, #ffffff);
  color: #333333;
}

.join-btn {
  /* 保持iOS风格但添加轻微的色彩 */
  background: linear-gradient(145deg, #f0f0f0, #ffffff);
  color: #333333;
}

.artistic-btn:active {
  transform: translateY(1rpx) scale(0.99);
  /* 按下时进一步加深内凹效果 */
  box-shadow:
    /* 更深的内部阴影 - 模拟按钮被进一步按下 */
    inset 16rpx 16rpx 32rpx #a8a8a8,
    inset -16rpx -16rpx 32rpx #ffffff,
    /* 几乎消除外部阴影 */
    1rpx 1rpx 4rpx rgba(0,0,0,0.05);
}

.btn-bg-effect {
  display: none; /* 移除背景效果 */
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
  flex-direction: row;
  gap: 16rpx;
}

.btn-icon {
  font-size: 32rpx;
  opacity: 0.8;
  filter: drop-shadow(0 1rpx 1rpx rgba(255,255,255,0.8));
}

.btn-text-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.btn-main-text {
  font-size: 28rpx;
  font-weight: 600;
  line-height: 1.3;
  color: #333333;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'PingFang SC', sans-serif;
  text-shadow: 0 1rpx 1rpx rgba(255,255,255,0.8);
}

.btn-sub-text {
  font-size: 20rpx;
  font-weight: 400;
  opacity: 0.7;
  margin-top: 2rpx;
  color: #666666;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
  text-shadow: 0 1rpx 1rpx rgba(255,255,255,0.8);
}

.btn-shine {
  display: none; /* 移除闪光效果 */
}

/* 开发者测试区域 */
.dev-test-section {
  margin: 32rpx;
}

.dev-test-card {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(255, 140, 22, 0.1) 100%) !important;
  border: 2rpx solid rgba(255, 107, 107, 0.2) !important;
}

.dev-test-content {
  padding: 16rpx 0;
}

.test-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.test-btn {
  background: rgba(255, 255, 255, 0.8) !important;
  border: 2rpx solid rgba(102, 126, 234, 0.3) !important;
  color: #667eea !important;
  font-size: 26rpx !important;
  padding: 16rpx !important;
  border-radius: 16rpx !important;
}

.test-btn:active {
  transform: scale(0.95);
  background: rgba(102, 126, 234, 0.1) !important;
}

.quick-room-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  border: none !important;
  font-weight: 600 !important;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3) !important;
}

.quick-room-btn:active {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%) !important;
}

.test-toggle {
  text-align: center;
}

.toggle-btn {
  background: rgba(255, 107, 107, 0.1) !important;
  border: 2rpx solid rgba(255, 107, 107, 0.3) !important;
  color: #ff6b6b !important;
  border-radius: 16rpx !important;
}

.show-dev-test {
  position: fixed;
  bottom: 32rpx;
  right: 32rpx;
  z-index: 100;
}

.show-test-btn {
  background: rgba(102, 126, 234, 0.9) !important;
  color: white !important;
  border: none !important;
  border-radius: 50% !important;
  width: 120rpx !important;
  height: 120rpx !important;
  font-size: 24rpx !important;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3) !important;
}

/* 原生组件样式增强 */
button {
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  outline: none;
}

button[type="primary"] {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

button[type="default"] {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  border: 2rpx solid rgba(102, 126, 234, 0.2);
}

button:active {
  transform: scale(0.98);
  opacity: 0.8;
}

input {
  background: rgba(255, 255, 255, 0.9);
  border: 2rpx solid rgba(102, 126, 234, 0.2);
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  text-align: center;
}

.room-status {
  background: #52c41a;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
}

.loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 32rpx;
  border-radius: 16rpx;
  z-index: 9999;
}
