/* AI剧本生成Loading页面样式 */
.loading-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f0f8ff 0%, #e6f3ff 100%);
  position: relative;
  overflow: hidden;
}

.loading-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 30% 20%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, rgba(138, 43, 226, 0.08) 0%, transparent 50%);
  pointer-events: none;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 80rpx 60rpx;
  position: relative;
  z-index: 1;
}

/* AI大脑动画 */
.ai-brain {
  position: relative;
  margin-bottom: 60rpx;
}

.brain-icon {
  position: relative;
  z-index: 2;
  animation: float 3s ease-in-out infinite;
}

.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120rpx;
  height: 120rpx;
  border: 4rpx solid rgba(102, 126, 234, 0.3);
  border-radius: 50%;
  animation: pulse 2s ease-out infinite;
}

.pulse-ring.delay-1 {
  animation-delay: 0.5s;
}

.pulse-ring.delay-2 {
  animation-delay: 1s;
}

/* 标题区域 */
.loading-title {
  font-size: 44rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
  text-align: center;
}

.loading-subtitle {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  text-align: center;
  margin-bottom: 60rpx;
  max-width: 500rpx;
}

/* 进度条区域 */
.progress-section {
  width: 100%;
  max-width: 400rpx;
  margin-bottom: 60rpx;
}

.progress-bar {
  margin-bottom: 16rpx;
}

.progress-text {
  text-align: center;
  font-size: 28rpx;
  color: #667eea;
  font-weight: 500;
}

/* 生成步骤 */
.steps-section {
  width: 100%;
  max-width: 400rpx;
  margin-bottom: 40rpx;
}

.step-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  transition: all 0.3s ease;
}

.step-item.completed {
  opacity: 1;
}

.step-item.processing {
  opacity: 1;
  animation: highlight 1.5s ease-in-out infinite;
}

.step-item.pending {
  opacity: 0.5;
}

.step-icon {
  margin-right: 24rpx;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-text {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

/* 提示信息 */
.tips-section {
  width: 100%;
  max-width: 400rpx;
}

.tips-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.tips-content {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
}

.tips-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  flex: 1;
}

/* 底部装饰点 */
.decoration-dots {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  pointer-events: none;
}

.dot {
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  background: rgba(102, 126, 234, 0.3);
  border-radius: 50%;
  animation: float-dot 4s ease-in-out infinite;
}

.dot-1 {
  left: 10%;
  bottom: 20%;
  animation-delay: 0s;
}

.dot-2 {
  left: 25%;
  bottom: 40%;
  animation-delay: 0.8s;
}

.dot-3 {
  left: 50%;
  bottom: 15%;
  animation-delay: 1.6s;
}

.dot-4 {
  left: 75%;
  bottom: 35%;
  animation-delay: 2.4s;
}

.dot-5 {
  left: 90%;
  bottom: 25%;
  animation-delay: 3.2s;
}

/* 动画定义 */
@keyframes float {
  0%, 100% { transform: translateY(0rpx); }
  50% { transform: translateY(-20rpx); }
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.7;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

@keyframes highlight {
  0%, 100% { background-color: transparent; }
  50% { background-color: rgba(102, 126, 234, 0.1); }
}

@keyframes float-dot {
  0%, 100% { transform: translateY(0rpx); opacity: 0.3; }
  50% { transform: translateY(-40rpx); opacity: 0.8; }
}

/* TDesign组件样式覆盖 */
.t-progress {
  --td-progress-bar-color: linear-gradient(90deg, #667eea, #764ba2);
}

.t-icon.loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
