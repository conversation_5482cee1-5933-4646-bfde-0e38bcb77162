/* 私人线索页面样式 */
.private-clues-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #0f0f23 0%, #1a1a2e 100%);
  color: white;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: rgba(15, 15, 35, 0.95);
  backdrop-filter: blur(20rpx);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  padding-top: calc(20rpx + env(safe-area-inset-top));
}

.navbar-title {
  color: white;
  font-size: 36rpx;
  font-weight: 600;
}

.navbar-extra {
  width: 48rpx;
  display: flex;
  justify-content: center;
}

/* 角色信息卡片 */
.character-info-section {
  padding: 32rpx;
  margin-top: calc(100rpx + env(safe-area-inset-top));
}

.character-card {
  display: flex;
  align-items: center;
  gap: 24rpx;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 28rpx;
  padding: 32rpx;
  border: 2rpx solid rgba(102, 126, 234, 0.3);
  position: relative;
}

.character-avatar {
  position: relative;
  flex-shrink: 0;
}

.character-status {
  position: absolute;
  bottom: -4rpx;
  right: -4rpx;
  background: rgba(15, 15, 35, 0.8);
  border-radius: 50%;
  padding: 4rpx;
}

.character-info {
  flex: 1;
}

.character-name {
  font-size: 32rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.character-title {
  font-size: 26rpx;
  color: #ccc;
  margin-bottom: 16rpx;
}

.character-stats {
  display: flex;
  gap: 24rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #e8e8e8;
}

/* 线索分类标签 */
.clue-tabs-section {
  padding: 0 32rpx 24rpx;
}

.clue-tabs {
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 24rpx;
  padding: 8rpx;
  backdrop-filter: blur(10rpx);
}

.clue-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx 12rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #ccc;
  transition: all 0.3s ease;
  position: relative;
}

.clue-tab.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: scale(1.05);
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.tab-badge {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background: #ff4d4f;
  color: white;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 12rpx;
  min-width: 24rpx;
  text-align: center;
}

/* 线索列表 */
.clues-section {
  flex: 1;
  padding: 0 32rpx;
  padding-bottom: 120rpx;
}

.clues-scroll {
  height: calc(100vh - 400rpx);
}

.clues-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 线索卡片 - 参考第三图设计 */
.clue-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(30rpx);
  border-radius: 32rpx;
  padding: 32rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  margin-bottom: 24rpx;
  box-shadow:
    0 16rpx 48rpx rgba(0, 0, 0, 0.1),
    0 4rpx 16rpx rgba(0, 0, 0, 0.05),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.2);
}

.clue-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
  border-radius: 32rpx 32rpx 0 0;
}

.clue-card:active {
  transform: translateY(-4rpx) scale(1.02);
  box-shadow:
    0 24rpx 64rpx rgba(0, 0, 0, 0.15),
    0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

/* 不同重要性的线索卡片样式 */
.clue-card.critical {
  border-color: rgba(255, 77, 79, 0.4);
  background: linear-gradient(135deg, rgba(255, 77, 79, 0.2) 0%, rgba(255, 77, 79, 0.05) 100%);
  box-shadow:
    0 16rpx 48rpx rgba(255, 77, 79, 0.2),
    0 4rpx 16rpx rgba(255, 77, 79, 0.1),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.3);
}

.clue-card.critical::before {
  background: linear-gradient(90deg, #ff4d4f, #ff7875, #ffa39e);
}

.clue-card.high {
  border-color: rgba(255, 193, 7, 0.4);
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.2) 0%, rgba(255, 193, 7, 0.05) 100%);
  box-shadow:
    0 16rpx 48rpx rgba(255, 193, 7, 0.2),
    0 4rpx 16rpx rgba(255, 193, 7, 0.1),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.3);
}

.clue-card.high::before {
  background: linear-gradient(90deg, #ffc107, #ffd54f, #ffeb3b);
}

.clue-card.normal {
  border-color: rgba(102, 126, 234, 0.3);
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(102, 126, 234, 0.05) 100%);
}

.clue-card.normal::before {
  background: linear-gradient(90deg, #667eea, #764ba2, #9c88ff);
}

.clue-card.low {
  border-color: rgba(255, 255, 255, 0.15);
  opacity: 0.85;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.02) 100%);
}

.clue-card.low::before {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
}

.clue-header {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.clue-icon {
  flex-shrink: 0;
  padding: 8rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
}

.clue-title-area {
  flex: 1;
}

/* 线索标题 - 增强可读性 */
.clue-title {
  font-size: 32rpx;
  font-weight: 700;
  margin-bottom: 12rpx;
  color: #ffffff;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  line-height: 1.4;
  letter-spacing: 0.5rpx;
}

.clue-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.clue-time {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(255, 255, 255, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  backdrop-filter: blur(10rpx);
}

.clue-status {
  flex-shrink: 0;
}

/* 线索预览 - 优化排版 */
.clue-preview {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-align: justify;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
  letter-spacing: 0.3rpx;
}

/* 线索标签 - 现代化设计 */
.clue-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 20rpx;
  margin-top: 16rpx;
}

.clue-tag {
  font-size: 22rpx;
  font-weight: 500;
  color: #ffffff;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8));
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.clue-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.clue-tag:hover::before {
  left: 100%;
}

.clue-relations {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.relations-label {
  font-size: 22rpx;
  color: #999;
}

.relations-avatars {
  display: flex;
  gap: 8rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 32rpx;
  text-align: center;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #666;
  margin: 24rpx 0 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(15, 15, 35, 0.95);
  backdrop-filter: blur(20rpx);
  padding: 20rpx 32rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  border-top: 2rpx solid rgba(255, 255, 255, 0.1);
  display: flex;
  gap: 12rpx;
}

.action-btn {
  flex: 1;
  background: rgba(255, 255, 255, 0.1) !important;
  border: 2rpx solid rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  border-radius: 20rpx !important;
  font-size: 26rpx !important;
}

.primary-action-btn {
  flex: 1.5;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  border-radius: 20rpx !important;
  font-size: 26rpx !important;
  font-weight: 600 !important;
}

/* 线索详情弹窗 */
.clue-detail-content {
  padding: 16rpx 0;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.detail-time {
  font-size: 24rpx;
  color: #666;
}

.detail-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 24rpx;
}

.detail-analysis {
  background: rgba(255, 193, 7, 0.1);
  border: 1rpx solid rgba(255, 193, 7, 0.3);
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 24rpx;
}

.analysis-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  font-weight: 600;
  color: #ffc107;
  margin-bottom: 12rpx;
}

.analysis-content {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

.detail-actions {
  display: flex;
  gap: 12rpx;
  justify-content: flex-end;
}

/* TDesign组件样式覆盖 */
.t-tag {
  border-radius: 12rpx !important;
}

.t-button {
  border-radius: 20rpx !important;
}
