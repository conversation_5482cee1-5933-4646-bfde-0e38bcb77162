/* 加入房间页面样式 */
.join-room-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f9f0ff 0%, #f0e6ff 100%);
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  padding-top: calc(20rpx + env(safe-area-inset-top));
}

.navbar-title {
  color: white;
  font-size: 36rpx;
  font-weight: 600;
}

/* 内容区域 */
.join-content {
  padding: 32rpx;
  margin-top: calc(100rpx + env(safe-area-inset-top));
  padding-bottom: 120rpx;
}

/* 通用区块样式 */
.input-section, .password-section, .quick-join-section, .recent-rooms-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

/* 房间号输入 */
.room-input-container {
  margin-bottom: 16rpx;
}

.room-input {
  font-size: 48rpx !important;
  font-weight: 600 !important;
  letter-spacing: 8rpx !important;
  text-align: center !important;
}

.input-tips {
  display: flex;
  align-items: center;
  gap: 8rpx;
  justify-content: center;
  font-size: 24rpx;
  color: #999;
}

/* 密码输入 */
.password-input-container {
  margin-bottom: 16rpx;
}

.password-input {
  font-size: 32rpx !important;
}

/* 快速加入 */
.quick-join-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 24rpx;
  text-align: center;
}

.quick-join-btn {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%) !important;
  border: none !important;
  border-radius: 24rpx !important;
  font-size: 30rpx !important;
  font-weight: 600 !important;
}

/* 最近房间 */
.recent-rooms-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.recent-room-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 16rpx;
  border: 2rpx solid rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;
}

.recent-room-item:active {
  transform: scale(0.98);
  background: rgba(102, 126, 234, 0.1);
}

.room-info {
  flex: 1;
}

.room-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.room-details {
  display: flex;
  gap: 16rpx;
  font-size: 24rpx;
  color: #666;
}

.room-status {
  flex-shrink: 0;
}

/* 底部按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  padding: 32rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  border-top: 2rpx solid rgba(0, 0, 0, 0.05);
}

/* 房间预览弹窗 */
.room-preview {
  padding: 16rpx 0;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.preview-item:last-child {
  border-bottom: none;
}

.preview-label {
  font-size: 28rpx;
  color: #666;
}

.preview-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* TDesign组件样式覆盖 */
.t-button--theme-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  border-radius: 24rpx !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3) !important;
}

.t-button--disabled {
  background: #d9d9d9 !important;
  color: #999 !important;
  box-shadow: none !important;
}

.t-input__control {
  border-radius: 16rpx !important;
  border: 2rpx solid #e6e6e6 !important;
  background: rgba(255, 255, 255, 0.8) !important;
  padding: 24rpx !important;
}

.t-input__control:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1) !important;
}
