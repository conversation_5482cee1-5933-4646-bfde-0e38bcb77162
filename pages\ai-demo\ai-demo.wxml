<!--AI功能演示页面-->
<view class="ai-demo-container">
  <!-- 页面标题 -->
  <view class="demo-header">
    <text class="demo-title">🤖 AI功能演示</text>
    <text class="demo-subtitle">体验智能剧本生成、角色分配和动态内容生成</text>
  </view>

  <!-- 参数配置区域 -->
  <view class="config-section">
    <view class="section-title">📝 剧本参数配置</view>
    
    <view class="config-item">
      <text class="config-label">剧本类型：</text>
      <picker bindchange="onStoryTypeChange" value="{{scriptParams.storyType}}" range-key="label" range="{{storyTypes}}">
        <view class="picker-value">{{storyTypes[0].label}}</view>
      </picker>
    </view>

    <view class="config-item">
      <text class="config-label">难度等级：</text>
      <picker bindchange="onDifficultyChange" value="{{scriptParams.difficulty}}" range-key="label" range="{{difficulties}}">
        <view class="picker-value">{{difficulties[1].label}}</view>
      </picker>
    </view>

    <view class="config-item">
      <text class="config-label">玩家数量：</text>
      <slider bindchange="onPlayerCountChange" value="{{scriptParams.playerCount}}" min="3" max="8" show-value />
    </view>

    <view class="config-item">
      <text class="config-label">故事主题：</text>
      <input class="config-input" bindinput="onThemeInput" value="{{scriptParams.theme}}" placeholder="输入故事主题" />
    </view>

    <view class="config-item">
      <text class="config-label">特殊要求：</text>
      <textarea class="config-textarea" bindinput="onRequirementsInput" value="{{scriptParams.specialRequirements}}" placeholder="输入特殊要求（可选）" />
    </view>
  </view>

  <!-- 演示按钮区域 -->
  <view class="demo-buttons">
    <button class="demo-btn primary" bindtap="demoScriptGeneration" disabled="{{loading}}">
      <text wx:if="{{loading && currentDemo === 'script'}}">🔄 生成中...</text>
      <text wx:else>🎭 演示剧本生成</text>
    </button>

    <button class="demo-btn secondary" bindtap="demoRoleAssignment" disabled="{{loading || !generatedScript}}">
      <text wx:if="{{loading && currentDemo === 'assignment'}}">🔄 分配中...</text>
      <text wx:else>👥 演示角色分配</text>
    </button>

    <button class="demo-btn tertiary" bindtap="demoDynamicContent" disabled="{{loading}}">
      <text wx:if="{{loading && currentDemo === 'dynamic'}}">🔄 生成中...</text>
      <text wx:else>💬 演示动态内容</text>
    </button>

    <button class="demo-btn clear" bindtap="clearDemo">🗑️ 清除演示</button>
  </view>

  <!-- 结果展示区域 -->
  <view class="results-section" wx:if="{{generatedScript || assignmentResult || dynamicContent}}">
    <view class="section-title">📊 演示结果</view>

    <!-- 剧本生成结果 -->
    <view class="result-card" wx:if="{{generatedScript}}">
      <view class="result-header">
        <text class="result-title">🎭 生成的剧本</text>
        <button class="detail-btn" bindtap="viewScriptDetail">查看详情</button>
      </view>
      <view class="result-content">
        <view class="result-item">
          <text class="item-label">标题：</text>
          <text class="item-value">{{generatedScript.storyInfo.title}}</text>
        </view>
        <view class="result-item">
          <text class="item-label">背景：</text>
          <text class="item-value">{{generatedScript.storyInfo.background}}</text>
        </view>
        <view class="result-item">
          <text class="item-label">角色数量：</text>
          <text class="item-value">{{generatedScript.characters.length}}个</text>
        </view>
        <view class="result-item">
          <text class="item-label">真心话问题：</text>
          <text class="item-value">{{generatedScript.truthQuestions.length}}个</text>
        </view>
      </view>
    </view>

    <!-- 角色分配结果 -->
    <view class="result-card" wx:if="{{assignmentResult}}">
      <view class="result-header">
        <text class="result-title">👥 角色分配</text>
        <button class="detail-btn" bindtap="viewAssignmentDetail">查看详情</button>
      </view>
      <view class="result-content">
        <view class="assignment-list">
          <view class="assignment-item" wx:for="{{assignmentResult.assignments}}" wx:key="playerId">
            <text class="player-id">{{item.playerId}}</text>
            <text class="arrow">→</text>
            <text class="character-name">{{item.characterName}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 动态内容结果 -->
    <view class="result-card" wx:if="{{dynamicContent}}">
      <view class="result-header">
        <text class="result-title">💬 动态内容</text>
        <button class="detail-btn" bindtap="viewDynamicDetail">查看详情</button>
      </view>
      <view class="result-content">
        <view class="question-list" wx:if="{{dynamicContent.questions}}">
          <view class="question-item" wx:for="{{dynamicContent.questions}}" wx:key="index">
            <text class="question-text">{{index + 1}}. {{item.question}}</text>
            <text class="question-purpose">目的：{{item.purpose}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">AI正在思考中...</text>
      <text class="loading-tip">请耐心等待，通常需要10-30秒</text>
    </view>
  </view>

  <!-- 底部操作 -->
  <view class="bottom-actions">
    <button class="action-btn" bindtap="goHome">🏠 返回首页</button>
  </view>
</view>
