import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";const{prefix:prefix}=config,name=`${prefix}-side-bar-item`;let SideBarItem=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`],this.properties=Object.assign(Object.assign({},props),{tId:{type:String}}),this.relations={"../side-bar/side-bar":{type:"parent",linked(e){this.parent=e,this.updateActive(e.data.value)}}},this.observers={icon(e){this.setData({_icon:"string"==typeof e?{name:e}:e})}},this.data={classPrefix:name,prefix:prefix,active:!1,isPre:!1,isNext:!1},this.methods={updateActive(e){const t=e===this.data.value;this.setData({active:t})},handleClick(){var e;if(this.data.disabled)return;const{value:t,label:i}=this.data;null===(e=this.parent)||void 0===e||e.doChange({value:t,label:i})}}}};SideBarItem=__decorate([wxComponent()],SideBarItem);export default SideBarItem;