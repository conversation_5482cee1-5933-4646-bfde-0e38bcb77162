// utils/account-checker.js
// Moonshot AI 账户状态检查工具

const apiConfig = require('../config/api-config');

class AccountChecker {
  constructor() {
    this.baseUrl = 'https://api.moonshot.cn/v1';
  }

  /**
   * 检查账户状态
   * @returns {Promise<Object>} 账户状态信息
   */
  async checkAccountStatus() {
    const config = apiConfig.getCurrentConfig();
    
    if (!config.apiKey || config.apiKey === 'YOUR_NEW_API_KEY_HERE') {
      return {
        status: 'no_key',
        message: '未配置API密钥',
        suggestions: [
          '请前往Moonshot AI控制台生成API密钥',
          '在应用的API设置页面配置密钥'
        ]
      };
    }

    try {
      // 尝试一个简单的API调用来检查状态
      const result = await this.testApiCall(config);
      return result;
    } catch (error) {
      return this.analyzeError(error);
    }
  }

  /**
   * 测试API调用
   * @param {Object} config - API配置
   * @returns {Promise<Object>} 测试结果
   */
  async testApiCall(config) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.baseUrl}/chat/completions`,
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.apiKey}`
        },
        data: {
          model: config.model,
          messages: [
            { role: 'user', content: 'test' }
          ],
          max_tokens: 1
        },
        timeout: 10000,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve({
              status: 'active',
              message: 'API密钥有效，账户状态正常',
              suggestions: []
            });
          } else {
            reject({
              statusCode: res.statusCode,
              data: res.data,
              message: res.data?.error?.message || '未知错误'
            });
          }
        },
        fail: (error) => {
          reject(error);
        }
      });
    });
  }

  /**
   * 分析错误并提供建议
   * @param {Object} error - 错误对象
   * @returns {Object} 分析结果
   */
  analyzeError(error) {
    const statusCode = error.statusCode;
    const errorMessage = error.message || error.data?.error?.message || '';

    switch (statusCode) {
      case 401:
        if (errorMessage.includes('Invalid Authentication')) {
          return {
            status: 'invalid_key',
            message: 'API密钥无效或已被禁用',
            suggestions: [
              '🚨 密钥可能已被Moonshot AI自动禁用（因泄露检测）',
              '🔑 请前往控制台删除当前密钥',
              '✨ 生成全新的API密钥',
              '💰 确认账户有足够余额',
              '⚠️ 避免在公开场所展示密钥'
            ],
            actions: [
              {
                text: '前往控制台',
                url: 'https://platform.moonshot.cn/console/api-keys'
              }
            ]
          };
        } else if (errorMessage.includes('账户额度不足')) {
          return {
            status: 'insufficient_balance',
            message: '账户余额不足',
            suggestions: [
              '💰 请前往控制台充值账户余额',
              '📊 查看账户使用情况',
              '💳 选择合适的充值套餐'
            ],
            actions: [
              {
                text: '前往充值',
                url: 'https://platform.moonshot.cn/console/billing'
              }
            ]
          };
        } else {
          return {
            status: 'auth_error',
            message: 'API认证失败',
            suggestions: [
              '🔍 检查API密钥是否正确',
              '💰 确认账户余额充足',
              '🔄 尝试重新生成密钥'
            ]
          };
        }

      case 402:
        return {
          status: 'insufficient_balance',
          message: '账户余额不足',
          suggestions: [
            '💰 请前往控制台充值账户余额',
            '📊 查看详细账单信息',
            '💡 考虑升级账户套餐'
          ],
          actions: [
            {
              text: '前往充值',
              url: 'https://platform.moonshot.cn/console/billing'
            }
          ]
        };

      case 403:
        return {
          status: 'access_denied',
          message: 'API访问被拒绝',
          suggestions: [
            '🔐 检查API密钥权限',
            '👤 确认账户状态正常',
            '📞 联系客服获取帮助'
          ]
        };

      case 429:
        return {
          status: 'rate_limited',
          message: '请求频率过高',
          suggestions: [
            '⏰ 请稍后重试',
            '📈 考虑升级账户套餐',
            '🔧 优化请求频率'
          ]
        };

      default:
        if (error.errMsg && error.errMsg.includes('timeout')) {
          return {
            status: 'network_error',
            message: '网络连接超时',
            suggestions: [
              '🌐 检查网络连接',
              '🔄 稍后重试',
              '📶 确认网络稳定'
            ]
          };
        } else {
          return {
            status: 'unknown_error',
            message: `未知错误: ${errorMessage}`,
            suggestions: [
              '🔄 稍后重试',
              '📞 如持续出现请联系客服',
              '🔧 检查网络和配置'
            ]
          };
        }
    }
  }

  /**
   * 获取账户状态的用户友好描述
   * @param {string} status - 状态代码
   * @returns {Object} 状态描述
   */
  getStatusDescription(status) {
    const descriptions = {
      'no_key': {
        icon: '🔑',
        title: '未配置API密钥',
        color: '#f39c12'
      },
      'active': {
        icon: '✅',
        title: '账户状态正常',
        color: '#27ae60'
      },
      'invalid_key': {
        icon: '🚨',
        title: '密钥无效或已禁用',
        color: '#e74c3c'
      },
      'insufficient_balance': {
        icon: '💰',
        title: '账户余额不足',
        color: '#f39c12'
      },
      'auth_error': {
        icon: '🔐',
        title: 'API认证失败',
        color: '#e74c3c'
      },
      'access_denied': {
        icon: '🚫',
        title: 'API访问被拒绝',
        color: '#e74c3c'
      },
      'rate_limited': {
        icon: '⏰',
        title: '请求频率过高',
        color: '#f39c12'
      },
      'network_error': {
        icon: '🌐',
        title: '网络连接错误',
        color: '#95a5a6'
      },
      'unknown_error': {
        icon: '❓',
        title: '未知错误',
        color: '#95a5a6'
      }
    };

    return descriptions[status] || descriptions['unknown_error'];
  }
}

module.exports = AccountChecker;
