// test-model-config.js
// 测试新的模型配置

const apiConfig = require('./config/api-config');

console.log('🧪 测试模型配置');
console.log('================');

// 获取当前配置
const config = apiConfig.getCurrentConfig();

console.log('当前API配置:');
console.log('- Base URL:', config.baseUrl);
console.log('- Model:', config.model);
console.log('- API Key:', config.apiKey ? `${config.apiKey.substring(0, 10)}...` : '未设置');

// 测试简单请求
async function testSimpleRequest() {
  console.log('\n🔍 测试简单API请求...');
  
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${config.baseUrl}/chat/completions`,
      method: 'POST',
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`
      },
      data: {
        model: config.model,
        messages: [
          {
            role: 'user',
            content: '请简单回复"测试成功"'
          }
        ],
        temperature: 0.3,
        max_tokens: 50,
        stream: false
      },
      timeout: 30000,
      success: (res) => {
        console.log('✅ API请求成功');
        console.log('状态码:', res.statusCode);
        console.log('响应数据:', res.data);
        resolve(res.data);
      },
      fail: (error) => {
        console.log('❌ API请求失败');
        console.log('错误信息:', error);
        reject(error);
      }
    });
  });
}

// 如果在小程序环境中运行
if (typeof wx !== 'undefined') {
  testSimpleRequest().then(result => {
    console.log('\n✅ 测试完成，配置正常');
  }).catch(error => {
    console.log('\n❌ 测试失败，需要检查配置');
    console.log('建议检查:');
    console.log('1. API密钥是否正确');
    console.log('2. 网络连接是否正常');
    console.log('3. 微信小程序域名白名单是否配置');
  });
} else {
  console.log('⚠️ 非小程序环境，无法测试wx.request');
}

module.exports = {
  testSimpleRequest
};
