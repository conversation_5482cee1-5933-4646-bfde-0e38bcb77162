<!--pages/persistence-demo/persistence-demo.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">个人坚持机制演示</text>
    <text class="page-subtitle">Personal Persistence Mechanism Demo</text>
  </view>

  <!-- 当前状态卡片 -->
  <view class="status-card">
    <view class="status-header">
      <text class="status-title">当前游戏状态</text>
      <view class="influence-badge" bindtap="toggleInfluenceDetail">
        <text class="influence-text">{{currentInfluence}}点影响力</text>
        <text class="influence-level">{{roleStatus}}地位</text>
      </view>
    </view>
    
    <view class="status-content">
      <view class="status-item">
        <text class="status-label">角色身份：</text>
        <text class="status-value">{{playerRole}}</text>
      </view>
      <view class="status-item">
        <text class="status-label">集体投票结果：</text>
        <text class="status-value vote-result">{{voteResult}}</text>
      </view>
      <view class="status-item">
        <text class="status-label">当前轮次：</text>
        <text class="status-value">第{{currentRound}}轮</text>
      </view>
      <view class="status-item">
        <text class="status-label">剧情重要程度：</text>
        <text class="status-value importance-{{plotImportanceClass}}">{{plotImportance}}</text>
      </view>
    </view>
  </view>

  <!-- 影响力详情（可展开） -->
  <view class="influence-detail {{showInfluenceDetail ? 'show' : ''}}" wx:if="{{influenceAnalysis}}">
    <view class="detail-header">
      <text class="detail-title">影响力分析</text>
    </view>
    <view class="detail-content">
      <text class="detail-text">{{influenceAnalysis.currentLevel}}</text>
      <text class="detail-desc">{{influenceAnalysis.afterChoice}}</text>
    </view>
  </view>

  <!-- 坚持机制选择面板 -->
  <view class="persistence-panel">
    <view class="panel-header">
      <text class="panel-title">个人坚持机制选择</text>
      <view class="countdown-timer" wx:if="{{countdownTimer}}">
        <text class="countdown-text">{{countdown}}s</text>
      </view>
    </view>

    <!-- 选择选项列表 -->
    <view class="options-list">
      <view 
        class="persistence-choice {{selectedOption && selectedOption.type === item.type ? 'selected' : ''}} {{item.influenceCost > currentInfluence ? 'disabled' : ''}}"
        wx:for="{{persistenceOptions}}" 
        wx:key="type"
        data-index="{{index}}"
        bindtap="selectPersistenceOption"
        animation="{{optionAnimation[index]}}"
      >
        <view class="choice-header">
          <view class="choice-icon">{{item.icon}}</view>
          <view class="choice-title-area">
            <text class="choice-title">{{item.name}}</text>
            <view class="choice-cost">
              <text class="cost-text" wx:if="{{item.influenceCost > 0}}">消耗 {{item.influenceCost}} 影响力</text>
              <text class="cost-text free" wx:else>免费选择</text>
            </view>
          </view>
          <view class="choice-risk risk-{{item.riskLevelClass}}">{{item.riskLevel}}</view>
        </view>
        
        <view class="choice-description">
          <text class="description-text">{{item.description}}</text>
        </view>
        
        <view class="choice-effects">
          <text class="effects-title">预期效果：</text>
          <view class="effects-list">
            <text class="effect-item" wx:for="{{item.effects}}" wx:for-item="effect" wx:key="*this">• {{effect}}</text>
          </view>
        </view>
        
        <view class="choice-recommendation">
          <text class="recommendation-text">{{item.recommendation}}</text>
        </view>

        <!-- 特殊要求提示 -->
        <view class="choice-requirements" wx:if="{{item.requirements}}">
          <text class="requirements-title">特殊要求：</text>
          <text class="requirements-text" wx:for="{{item.requirements}}" wx:for-item="req" wx:key="*this">{{req}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮区域 -->
  <view class="action-buttons">
    <view class="button-row">
      <button 
        class="demo-button secondary" 
        bindtap="generateAIPersistenceOptions"
        loading="{{loading}}"
      >
        {{loading ? '生成中...' : 'AI智能生成选择'}}
      </button>
      <button 
        class="demo-button secondary" 
        bindtap="calculateInfluence"
        loading="{{loading}}"
      >
        计算影响力
      </button>
    </view>
    
    <view class="button-row">
      <button 
        class="demo-button secondary" 
        bindtap="startCountdown"
        disabled="{{countdownTimer}}"
      >
        开始倒计时
      </button>
      <button 
        class="demo-button secondary" 
        bindtap="stopCountdown"
        disabled="{{!countdownTimer}}"
      >
        停止倒计时
      </button>
    </view>

    <button 
      class="demo-button primary confirm-button" 
      bindtap="confirmPersistenceChoice"
      disabled="{{!selectedOption || loading}}"
    >
      确认选择：{{selectedOption ? selectedOption.name : '请选择'}}
    </button>
  </view>

  <!-- 演示配置切换 -->
  <view class="demo-configs">
    <text class="config-title">演示配置：</text>
    <view class="config-buttons">
      <button 
        class="config-button {{item.config.currentInfluence === currentInfluence ? 'active' : ''}}"
        wx:for="{{demoConfigs}}" 
        wx:key="name"
        data-index="{{index}}"
        bindtap="switchDemoConfig"
      >
        {{item.name}}
      </button>
    </view>
  </view>

  <!-- 说明文档 -->
  <view class="help-section">
    <view class="help-header">
      <text class="help-title">机制说明</text>
    </view>
    <view class="help-content">
      <text class="help-text">个人坚持机制允许玩家在集体投票后，使用个人影响力改变剧情走向：</text>
      <text class="help-item">• 跟随集体：安全选择，获得影响力奖励</text>
      <text class="help-item">• 轻度坚持：获得额外收益，风险较低</text>
      <text class="help-item">• 强力坚持：改变剧情方向，风险中等</text>
      <text class="help-item">• 极限坚持：颠覆性选择，高风险高回报</text>
    </view>
  </view>
</view>
