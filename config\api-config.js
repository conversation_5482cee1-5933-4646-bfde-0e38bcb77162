// config/api-config.js
// API配置管理 - 安全版本

/**
 * API配置类
 * 注意：实际的API密钥不应该硬编码在代码中
 */
class ApiConfig {
  constructor() {
    // 开发环境配置
    this.development = {
      baseUrl: 'https://api.moonshot.cn/v1',
      model: 'moonshot-v1-8k', // 使用更稳定的模型
      // 注意：这里应该从环境变量或安全存储中获取
      apiKey: this.getApiKey()
    };

    // 生产环境配置
    this.production = {
      baseUrl: 'https://api.moonshot.cn/v1',
      model: 'moonshot-v1-8k', // 使用更稳定的模型
      apiKey: this.getApiKey()
    };
  }

  /**
   * 获取当前环境配置
   */
  getCurrentConfig() {
    // 微信小程序中判断环境的方法
    const accountInfo = wx.getAccountInfoSync();
    const envVersion = accountInfo.miniProgram.envVersion;
    
    if (envVersion === 'develop' || envVersion === 'trial') {
      return this.development;
    } else {
      return this.production;
    }
  }

  /**
   * 安全获取API密钥
   * 注意：这里需要你手动替换为新的API密钥
   */
  getApiKey() {
    // 方案1: 从本地存储获取（推荐用于开发测试）
    try {
      const storedKey = wx.getStorageSync('moonshot_api_key');
      if (storedKey) {
        return storedKey;
      }
    } catch (error) {
      console.warn('无法从本地存储获取API密钥');
    }

    // 方案2: 临时硬编码（仅用于测试，生产环境需要更安全的方式）
    // 使用你的新API密钥
    return 'sk-rFun7AywY7jUUdJAtUBbFDxhaVTI5okdFpL3mSSeLfOmKCmP';
  }

  /**
   * 设置API密钥到本地存储
   * @param {string} apiKey - 新的API密钥
   */
  setApiKey(apiKey) {
    try {
      wx.setStorageSync('moonshot_api_key', apiKey);
      console.log('API密钥已保存到本地存储');
      return true;
    } catch (error) {
      console.error('保存API密钥失败:', error);
      return false;
    }
  }

  /**
   * 清除API密钥
   */
  clearApiKey() {
    try {
      wx.removeStorageSync('moonshot_api_key');
      console.log('API密钥已清除');
      return true;
    } catch (error) {
      console.error('清除API密钥失败:', error);
      return false;
    }
  }

  /**
   * 验证API密钥格式
   * @param {string} apiKey - 要验证的API密钥
   */
  validateApiKey(apiKey) {
    if (!apiKey) {
      return { valid: false, message: 'API密钥不能为空' };
    }

    if (!apiKey.startsWith('sk-')) {
      return { valid: false, message: 'API密钥格式错误，应以sk-开头' };
    }

    if (apiKey.length < 30) {
      return { valid: false, message: 'API密钥长度不足，请检查是否完整复制' };
    }

    if (apiKey === 'YOUR_NEW_API_KEY_HERE') {
      return { valid: false, message: '请替换为真实的API密钥' };
    }

    return { valid: true, message: 'API密钥格式正确' };
  }

  /**
   * 获取安全的密钥显示（用于日志和调试）
   * @param {string} apiKey - 完整的API密钥
   */
  getSafeKeyDisplay(apiKey) {
    if (!apiKey || apiKey.length < 10) {
      return '***';
    }
    return apiKey.substring(0, 6) + '...' + apiKey.substring(apiKey.length - 4);
  }
}

// 创建全局配置实例
const apiConfig = new ApiConfig();

module.exports = apiConfig;
