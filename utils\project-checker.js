// utils/project-checker.js
// Moonshot AI 项目状态检查工具

class ProjectChecker {
  constructor() {
    this.projectInfo = {
      organizationId: 'org-02000ec2fff2415fbbf190d126adf8ec',
      projectId: 'proj-19a7aa5e5d4c4102bb1c4af7e24740b7',
      projectName: 'default'
    };
  }

  /**
   * 获取项目信息
   * @returns {Object} 项目信息
   */
  getProjectInfo() {
    return {
      ...this.projectInfo,
      status: 'unknown',
      balance: 'unknown',
      lastChecked: new Date().toISOString()
    };
  }

  /**
   * 检查项目状态的用户指南
   * @returns {Object} 检查指南
   */
  getCheckGuide() {
    return {
      title: '项目状态检查指南',
      steps: [
        {
          step: 1,
          title: '访问控制台',
          description: '前往 https://platform.moonshot.cn/console',
          action: 'navigate'
        },
        {
          step: 2,
          title: '查看项目总览',
          description: '点击左侧菜单的"项目总览"',
          details: [
            '确认当前在"default"项目中',
            '查看项目状态是否为"正常"',
            '检查项目是否已激活'
          ]
        },
        {
          step: 3,
          title: '检查余额',
          description: '查看项目余额信息',
          details: [
            '确认余额大于0',
            '检查是否有足够资金进行API调用',
            '如果余额不足，需要充值'
          ],
          warning: '⚠️ 余额不足是401错误的最常见原因！'
        },
        {
          step: 4,
          title: '验证API密钥',
          description: '在"API Key 管理"页面检查',
          details: [
            '确认密钥状态为"有效"',
            '检查密钥权限设置',
            '确认密钥属于正确的项目'
          ]
        }
      ],
      troubleshooting: {
        '余额不足': {
          solution: '前往计费页面充值',
          urgency: 'high',
          estimatedTime: '5-10分钟'
        },
        '项目未激活': {
          solution: '联系客服激活项目',
          urgency: 'medium',
          estimatedTime: '1-24小时'
        },
        'API密钥无效': {
          solution: '重新生成API密钥',
          urgency: 'medium',
          estimatedTime: '5分钟'
        }
      }
    };
  }

  /**
   * 生成项目诊断报告
   * @param {Object} apiTestResult - API测试结果
   * @returns {Object} 诊断报告
   */
  generateDiagnosticReport(apiTestResult) {
    const report = {
      projectInfo: this.getProjectInfo(),
      timestamp: new Date().toISOString(),
      apiTestResult: apiTestResult,
      diagnosis: [],
      recommendations: []
    };

    // 基于API测试结果进行诊断
    if (apiTestResult.statusCode === 401) {
      if (apiTestResult.error?.message === 'Invalid Authentication') {
        report.diagnosis.push({
          issue: 'API认证失败',
          severity: 'high',
          possibleCauses: [
            '项目余额不足（最可能）',
            'API密钥已被禁用',
            '项目状态异常',
            'API密钥权限不足'
          ]
        });

        report.recommendations.push({
          priority: 1,
          action: '立即检查项目余额',
          description: '在控制台查看"default"项目的余额状态',
          url: 'https://platform.moonshot.cn/console'
        });

        report.recommendations.push({
          priority: 2,
          action: '如余额不足，立即充值',
          description: '选择合适的充值金额（建议10-20元用于测试）'
        });

        report.recommendations.push({
          priority: 3,
          action: '验证API密钥状态',
          description: '确认密钥在正确项目中且状态有效'
        });
      }
    } else if (apiTestResult.statusCode === 402) {
      report.diagnosis.push({
        issue: '余额不足',
        severity: 'high',
        possibleCauses: ['项目余额已用完']
      });

      report.recommendations.push({
        priority: 1,
        action: '立即充值',
        description: '为"default"项目充值以继续使用API服务'
      });
    }

    return report;
  }

  /**
   * 获取项目管理最佳实践
   * @returns {Object} 最佳实践指南
   */
  getBestPractices() {
    return {
      title: 'Moonshot AI 项目管理最佳实践',
      practices: [
        {
          category: '余额管理',
          tips: [
            '定期检查项目余额',
            '设置余额预警',
            '根据使用量合理充值',
            '监控API调用成本'
          ]
        },
        {
          category: 'API密钥管理',
          tips: [
            '为不同环境使用不同密钥',
            '定期轮换API密钥',
            '设置合适的使用限制',
            '监控密钥使用情况'
          ]
        },
        {
          category: '项目组织',
          tips: [
            '为不同应用创建独立项目',
            '使用有意义的项目名称',
            '定期清理不用的项目',
            '合理分配项目权限'
          ]
        },
        {
          category: '安全实践',
          tips: [
            '不要在代码中硬编码密钥',
            '使用环境变量或安全存储',
            '定期审查项目访问权限',
            '监控异常API调用'
          ]
        }
      ]
    };
  }

  /**
   * 格式化项目信息用于显示
   * @returns {string} 格式化的项目信息
   */
  formatProjectInfo() {
    const info = this.getProjectInfo();
    return `
项目信息:
- 组织ID: ${info.organizationId}
- 项目ID: ${info.projectId}  
- 项目名称: ${info.projectName}
- 状态: ${info.status}
- 余额: ${info.balance}
- 检查时间: ${new Date(info.lastChecked).toLocaleString()}

⚠️ 重要提醒:
API密钥只能在所属项目中使用。
如果出现401错误，首先检查项目余额！
    `.trim();
  }
}

module.exports = ProjectChecker;
