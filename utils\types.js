// 数据类型定义
// 用于统一前后端数据结构

/**
 * 用户数据结构
 */
const UserType = {
  id: '',
  nickname: '',
  avatar: '',
  level: 1,
  experience: 0,
  gamesPlayed: 0,
  winRate: 0,
  createdAt: '',
  updatedAt: ''
};

/**
 * 房间数据结构
 */
const RoomType = {
  id: '',
  roomId: '', // 6位房间号
  name: '',
  password: '', // 可选
  maxPlayers: 6,
  currentPlayers: 0,
  gameStyle: 'classic', // classic, social, puzzle, roleplay
  difficulty: 'medium', // easy, medium, hard, expert
  mechanisms: [], // 特殊机制数组
  status: 'waiting', // waiting, playing, finished
  hostId: '',
  players: [], // 玩家列表
  createdAt: '',
  updatedAt: ''
};

/**
 * 玩家数据结构
 */
const PlayerType = {
  id: '',
  userId: '',
  nickname: '',
  avatar: '',
  isHost: false,
  isReady: false,
  status: 'online', // online, offline, disconnected
  joinedAt: ''
};

/**
 * 游戏数据结构
 */
const GameType = {
  id: '',
  roomId: '',
  status: 'preparing', // preparing, story, roles, discussion, voting, result
  currentPhase: '',
  timeLeft: 0,
  story: null, // StoryType
  characters: [], // CharacterType[]
  players: [], // GamePlayerType[]
  votes: [], // VoteType[]
  result: null, // GameResultType
  createdAt: '',
  updatedAt: ''
};

/**
 * 故事背景数据结构
 */
const StoryType = {
  id: '',
  title: '',
  subtitle: '',
  genre: '',
  difficulty: '',
  playerCount: '',
  paragraphs: [], // 故事段落数组
  setting: '', // 故事背景设定
  theme: '' // 故事主题
};

/**
 * 角色数据结构
 */
const CharacterType = {
  id: '',
  name: '',
  role: '',
  description: '',
  avatar: '',
  background: '',
  personality: '',
  motivation: '',
  secrets: [], // 角色秘密
  relationships: [], // 与其他角色的关系
  clues: [], // 私人线索
  position: { x: 0, y: 0 } // 关系图中的位置
};

/**
 * 游戏中的玩家数据结构
 */
const GamePlayerType = {
  id: '',
  userId: '',
  nickname: '',
  avatar: '',
  character: null, // CharacterType
  clues: [], // ClueType[]
  votes: [], // 投票记录
  status: 'alive', // alive, eliminated
  score: 0
};

/**
 * 线索数据结构
 */
const ClueType = {
  id: '',
  title: '',
  content: '',
  type: 'public', // public, private, shared
  importance: 'normal', // low, normal, high, critical
  relatedCharacters: [], // 相关角色ID
  discoveredBy: '', // 发现者ID
  discoveredAt: ''
};

/**
 * 投票数据结构
 */
const VoteType = {
  id: '',
  gameId: '',
  phase: '', // discussion, plot, final
  voterId: '',
  targetId: '',
  reason: '',
  confidence: 0, // 0-100
  createdAt: ''
};

/**
 * 游戏结果数据结构
 */
const GameResultType = {
  id: '',
  gameId: '',
  winner: '', // 获胜方
  truth: '', // 真相揭露
  playerResults: [], // PlayerResultType[]
  mvp: '', // 最佳玩家
  duration: 0, // 游戏时长（分钟）
  createdAt: ''
};

/**
 * 玩家结果数据结构
 */
const PlayerResultType = {
  playerId: '',
  character: '',
  performance: '', // excellent, good, average, poor
  score: 0,
  achievements: [], // 成就列表
  feedback: '' // 游戏反馈
};

/**
 * 聊天消息数据结构
 */
const MessageType = {
  id: '',
  roomId: '',
  senderId: '',
  senderName: '',
  senderAvatar: '',
  content: '',
  type: 'text', // text, image, clue, system
  timestamp: '',
  isPrivate: false,
  targetId: '' // 私聊目标ID
};

/**
 * 真心话大冒险数据结构
 */
const TruthDareType = {
  id: '',
  gameId: '',
  playerId: '',
  type: 'truth', // truth, dare
  question: '',
  answer: '',
  relatedClues: [], // 相关线索
  createdAt: ''
};

/**
 * 小游戏数据结构
 */
const MiniGameType = {
  id: '',
  gameId: '',
  type: 'draw_guess', // draw_guess, word_chain, riddle
  title: '',
  description: '',
  participants: [], // 参与者ID
  result: null, // 游戏结果
  rewards: [], // 奖励线索
  createdAt: ''
};

// 导出所有类型定义
module.exports = {
  UserType,
  RoomType,
  PlayerType,
  GameType,
  StoryType,
  CharacterType,
  GamePlayerType,
  ClueType,
  VoteType,
  GameResultType,
  PlayerResultType,
  MessageType,
  TruthDareType,
  MiniGameType
};
