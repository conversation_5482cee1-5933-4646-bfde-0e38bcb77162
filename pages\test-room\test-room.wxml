<!--测试房间功能页面-->
<view class="test-container">
  <view class="header">
    <text class="title">房间功能测试</text>
  </view>

  <view class="user-info">
    <text class="label">当前用户:</text>
    <text class="value">{{currentUser.nickname}} ({{currentUser.id}})</text>
  </view>

  <view class="room-info" wx:if="{{roomInfo}}">
    <text class="label">房间ID:</text>
    <text class="value">{{roomInfo.id}}</text>
    <text class="label">玩家数量:</text>
    <text class="value">{{roomInfo.players.size}}/{{roomInfo.maxPlayers}}</text>
  </view>

  <view class="button-group">
    <button class="test-btn" bind:tap="createTestRoom">创建测试房间</button>
    <button class="test-btn" bind:tap="addMockPlayer" disabled="{{!roomId}}">添加模拟玩家</button>
    <button class="test-btn" bind:tap="showRoomInfo" disabled="{{!roomInfo}}">显示房间信息</button>
    <button class="test-btn primary" bind:tap="goToRoomLobby" disabled="{{!roomId}}">进入房间大厅</button>
  </view>
</view>
