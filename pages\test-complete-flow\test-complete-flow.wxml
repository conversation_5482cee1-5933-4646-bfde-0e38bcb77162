<!--完整流程测试页面-->
<view class="container">
  <view class="header">
    <text class="title">🔬 完整流程测试</text>
    <text class="subtitle">验证从创建房间到角色分配的完整流程</text>
  </view>

  <!-- 测试步骤 -->
  <view class="test-steps">
    <view class="steps-title">测试步骤</view>
    <view class="steps-list">
      <view 
        class="step-item {{item.status}}" 
        wx:for="{{testSteps}}" 
        wx:key="id"
      >
        <view class="step-indicator">
          <view class="step-number {{currentStep === item.id ? 'active' : ''}}">
            {{item.id}}
          </view>
          <view class="step-status">
            <text wx:if="{{item.status === 'pending'}}">⏳</text>
            <text wx:if="{{item.status === 'running'}}">🔄</text>
            <text wx:if="{{item.status === 'success'}}">✅</text>
            <text wx:if="{{item.status === 'failed'}}">❌</text>
          </view>
        </view>
        
        <view class="step-content">
          <text class="step-name">{{item.name}}</text>
          <text class="step-desc">{{item.desc}}</text>
          <text class="step-result" wx:if="{{item.result}}">{{item.result}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 控制按钮 -->
  <view class="controls">
    <button 
      class="control-btn primary" 
      bind:tap="startCompleteTest"
      loading="{{isRunning}}"
      disabled="{{isRunning}}"
    >
      {{isRunning ? '测试中...' : '🚀 开始完整测试'}}
    </button>
    
    <button 
      class="control-btn secondary" 
      bind:tap="resetTest"
      disabled="{{isRunning}}"
    >
      🔄 重置测试
    </button>
  </view>

  <!-- 测试结果 -->
  <view class="test-result" wx:if="{{testResult}}">
    <view class="result-header">
      <text class="result-title">📊 测试结果</text>
      <view class="result-status {{testResult.success ? 'success' : 'failed'}}">
        {{testResult.success ? '✅ 成功' : '❌ 失败'}}
      </view>
    </view>
    
    <view class="result-content">
      <text class="result-message">{{testResult.message}}</text>
      
      <view class="result-details" wx:if="{{testResult.success && testResult.details}}">
        <view class="detail-item">
          <text class="detail-label">房间ID:</text>
          <text class="detail-value">{{testResult.roomId}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">玩家数量:</text>
          <text class="detail-value">{{testResult.details.roomPlayers}}人</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">剧本标题:</text>
          <text class="detail-value">{{testResult.details.scriptTitle}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">角色数量:</text>
          <text class="detail-value">{{testResult.details.roleCount}}个</text>
        </view>
      </view>
    </view>

    <view class="result-actions">
      <button class="action-btn" bind:tap="viewTestDetails">
        📋 查看详情
      </button>
      <button class="action-btn" bind:tap="goToRoomLobby" wx:if="{{testResult.success}}">
        🏠 进入房间
      </button>
      <button class="action-btn" bind:tap="goToRoleAssignment" wx:if="{{testResult.success}}">
        🎭 角色分配
      </button>
      <button class="action-btn" bind:tap="goToCluesCollection" wx:if="{{testResult.success}}">
        🔍 线索收集
      </button>
    </view>
  </view>

  <!-- 说明信息 -->
  <view class="info-section">
    <text class="info-title">ℹ️ 测试说明</text>
    <view class="info-list">
      <text class="info-item">• 自动创建房间并添加AI测试玩家</text>
      <text class="info-item">• 生成AI剧本并进行角色分配</text>
      <text class="info-item">• 验证整个流程的数据完整性</text>
      <text class="info-item">• 测试成功后可直接进入游戏</text>
      <text class="info-item">• 支持重复测试和结果查看</text>
    </view>
  </view>
</view>
