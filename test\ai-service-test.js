// AI服务测试文件
const aiService = require('../utils/ai-service');

/**
 * 测试AI剧本生成功能
 */
async function testScriptGeneration() {
  console.log('=== 测试AI剧本生成 ===');
  
  const testParams = {
    storyType: 'mystery',
    playerCount: 6,
    difficulty: 'medium',
    theme: '现代都市悬疑',
    specialRequirements: '包含密室元素',
    gameSettings: {
      duration: '60-90分钟',
      rounds: '3轮',
      truthDare: true,
      miniGame: true
    }
  };

  try {
    console.log('开始生成剧本，参数:', testParams);
    const startTime = Date.now();
    
    const scriptData = await aiService.generateScript(testParams);
    
    const endTime = Date.now();
    console.log(`剧本生成完成，耗时: ${endTime - startTime}ms`);
    
    // 验证剧本结构
    console.log('验证剧本结构...');
    validateScriptStructure(scriptData);
    
    console.log('剧本标题:', scriptData.storyInfo.title);
    console.log('角色数量:', scriptData.characters.length);
    console.log('真心话问题数量:', scriptData.truthQuestions?.length || 0);
    
    return scriptData;
    
  } catch (error) {
    console.error('剧本生成测试失败:', error);
    throw error;
  }
}

/**
 * 测试角色分配功能
 */
async function testRoleAssignment(scriptData) {
  console.log('\n=== 测试AI角色分配 ===');
  
  const mockPlayerIds = ['player_1', 'player_2', 'player_3', 'player_4', 'player_5', 'player_6'];
  
  try {
    console.log('开始角色分配...');
    const startTime = Date.now();
    
    const assignmentResult = await aiService.generateRoleAssignment(scriptData, mockPlayerIds);
    
    const endTime = Date.now();
    console.log(`角色分配完成，耗时: ${endTime - startTime}ms`);
    
    // 验证分配结果
    console.log('验证分配结果...');
    validateAssignmentResult(assignmentResult, mockPlayerIds, scriptData.characters);
    
    console.log('分配结果:', assignmentResult.assignments);
    
    return assignmentResult;
    
  } catch (error) {
    console.error('角色分配测试失败:', error);
    throw error;
  }
}

/**
 * 测试动态内容生成
 */
async function testDynamicContent() {
  console.log('\n=== 测试动态内容生成 ===');
  
  const gameContext = {
    storyBackground: '神秘庄园谋杀案',
    currentPhase: '第二轮讨论',
    aliveCharacters: ['艾米丽', '约翰', '玛丽', '汤姆'],
    eliminatedCharacters: ['杰克', '莉莉']
  };
  
  try {
    console.log('生成真心话问题...');
    const truthQuestions = await aiService.generateDynamicContent('truthQuestions', gameContext);
    
    console.log('真心话问题:', truthQuestions);
    
    return truthQuestions;
    
  } catch (error) {
    console.error('动态内容生成测试失败:', error);
    throw error;
  }
}

/**
 * 验证剧本结构完整性
 */
function validateScriptStructure(scriptData) {
  const requiredFields = ['storyInfo', 'characters'];
  const requiredStoryFields = ['title', 'background', 'coreEvent', 'winConditions'];
  const requiredCharacterFields = ['id', 'name', 'title', 'faction', 'background', 'objectives'];
  
  // 检查顶级字段
  for (const field of requiredFields) {
    if (!scriptData[field]) {
      throw new Error(`缺少必需字段: ${field}`);
    }
  }
  
  // 检查故事信息字段
  for (const field of requiredStoryFields) {
    if (!scriptData.storyInfo[field]) {
      throw new Error(`故事信息缺少必需字段: ${field}`);
    }
  }
  
  // 检查角色字段
  if (!Array.isArray(scriptData.characters) || scriptData.characters.length === 0) {
    throw new Error('角色列表为空或格式错误');
  }
  
  scriptData.characters.forEach((char, index) => {
    for (const field of requiredCharacterFields) {
      if (!char[field]) {
        throw new Error(`角色${index + 1}缺少必需字段: ${field}`);
      }
    }
  });
  
  console.log('✓ 剧本结构验证通过');
}

/**
 * 验证角色分配结果
 */
function validateAssignmentResult(assignmentResult, playerIds, characters) {
  if (!assignmentResult.assignments || !Array.isArray(assignmentResult.assignments)) {
    throw new Error('分配结果格式错误');
  }
  
  if (assignmentResult.assignments.length !== playerIds.length) {
    throw new Error('分配结果数量与玩家数量不匹配');
  }
  
  const assignedCharacterIds = new Set();
  const assignedPlayerIds = new Set();
  
  assignmentResult.assignments.forEach((assignment, index) => {
    if (!assignment.playerId || !assignment.characterId) {
      throw new Error(`分配结果${index + 1}缺少必需字段`);
    }
    
    if (assignedPlayerIds.has(assignment.playerId)) {
      throw new Error(`玩家${assignment.playerId}被重复分配`);
    }
    
    if (assignedCharacterIds.has(assignment.characterId)) {
      throw new Error(`角色${assignment.characterId}被重复分配`);
    }
    
    assignedPlayerIds.add(assignment.playerId);
    assignedCharacterIds.add(assignment.characterId);
  });
  
  console.log('✓ 角色分配结果验证通过');
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('开始AI服务功能测试...\n');
  
  try {
    // 测试剧本生成
    const scriptData = await testScriptGeneration();
    
    // 测试角色分配
    const assignmentResult = await testRoleAssignment(scriptData);
    
    // 测试动态内容生成
    const dynamicContent = await testDynamicContent();
    
    console.log('\n=== 所有测试完成 ===');
    console.log('✓ 剧本生成测试通过');
    console.log('✓ 角色分配测试通过');
    console.log('✓ 动态内容生成测试通过');
    
    return {
      scriptData,
      assignmentResult,
      dynamicContent
    };
    
  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    throw error;
  }
}

/**
 * 性能测试
 */
async function performanceTest() {
  console.log('\n=== 性能测试 ===');
  
  const testCount = 3;
  const times = [];
  
  for (let i = 0; i < testCount; i++) {
    console.log(`第${i + 1}次性能测试...`);
    
    const startTime = Date.now();
    
    try {
      await testScriptGeneration();
      const endTime = Date.now();
      const duration = endTime - startTime;
      times.push(duration);
      
      console.log(`第${i + 1}次测试完成，耗时: ${duration}ms`);
      
    } catch (error) {
      console.error(`第${i + 1}次测试失败:`, error.message);
    }
  }
  
  if (times.length > 0) {
    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    console.log(`\n性能测试结果:`);
    console.log(`平均耗时: ${avgTime.toFixed(2)}ms`);
    console.log(`最短耗时: ${minTime}ms`);
    console.log(`最长耗时: ${maxTime}ms`);
  }
}

// 导出测试函数
module.exports = {
  testScriptGeneration,
  testRoleAssignment,
  testDynamicContent,
  runAllTests,
  performanceTest
};

// 如果直接运行此文件，执行所有测试
if (require.main === module) {
  runAllTests()
    .then(() => {
      console.log('\n所有测试成功完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n测试失败:', error);
      process.exit(1);
    });
}
