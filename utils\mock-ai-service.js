// utils/mock-ai-service.js
// 模拟AI服务 - 用于开发和测试

class MockAIService {
  constructor() {
    this.isEnabled = true;
    this.delay = 2000; // 模拟网络延迟
  }

  /**
   * 模拟延迟
   */
  async simulateDelay() {
    return new Promise(resolve => setTimeout(resolve, this.delay));
  }

  /**
   * 生成故事背景
   */
  async generateStoryPrompt(params) {
    await this.simulateDelay();

    const { storyType = 'mystery', playerCount, difficulty = 'medium', theme } = params;

    console.log('🎭 模拟服务生成剧本，参数:', { storyType, playerCount, difficulty, theme });

    const mockStories = {
      'mystery': {
        easy: {
          title: '咖啡厅失窃案',
          background: '市中心一家知名咖啡厅的收银机被盗，监控录像显示有内鬼作案。店长怀疑是员工所为。',
          setting: '现代都市，繁华商业街，周末下午',
          objective: '找出内鬼，追回被盗资金'
        },
        medium: {
          title: '公司机密泄露',
          background: '科技公司的核心技术被竞争对手提前发布，内部必有间谍。CEO要求秘密调查。',
          setting: '现代写字楼，高科技公司，工作日夜晚',
          objective: '揪出内部间谍，保护公司利益'
        },
        hard: {
          title: '名画失踪悬案',
          background: '博物馆价值连城的名画在展览期间神秘消失，现场没有破坏痕迹，疑似内部人员作案。',
          setting: '现代艺术博物馆，周末展览日，人流密集',
          objective: '找回失踪名画，揭露盗窃真相'
        }
      },
      'romance': {
        easy: {
          title: '校园恋爱推理',
          background: '在樱花飞舞的大学校园里，学生会的情书箱里出现了一封神秘的匿名情书。收信人是校花，但情书的内容暗示着一个关于过去的秘密。',
          setting: '春日大学校园，樱花盛开的季节',
          objective: '找出神秘情书的作者，揭开青春往事的真相'
        },
        medium: {
          title: '婚礼前的秘密',
          background: '在一场盛大的婚礼前夜，新娘收到了一张神秘的照片，照片中显示新郎与另一个女人的亲密合影。婚礼是否还能如期举行？',
          setting: '豪华酒店，婚礼前夜，浪漫装饰',
          objective: '查明照片的真相，决定婚礼的命运'
        },
        hard: {
          title: '十年之约的谜团',
          background: '十年前，一群大学同学约定十年后在老地方相聚。如今聚会如期而至，但其中一人带来了一个关于当年暗恋的惊人秘密。',
          setting: '大学时代的咖啡厅，十年后的重聚',
          objective: '解开十年前的情感谜团，重新审视青春记忆'
        }
      },
      'horror': {
        easy: {
          title: '深夜图书馆',
          background: '大学图书馆在深夜传出奇怪的声音，管理员发现有人在闭馆后仍在里面。是小偷，还是有其他不可告人的秘密？',
          setting: '深夜的大学图书馆，昏暗的灯光',
          objective: '找出深夜图书馆的真相，解开神秘事件'
        },
        medium: {
          title: '古宅夜话',
          background: '一群朋友在古老的庄园里过夜，传说这里曾经发生过神秘的失踪案。夜深时分，奇怪的事情开始发生...',
          setting: '偏远的古老庄园，风雨交加的夜晚',
          objective: '揭开古宅的秘密，找出失踪案的真相'
        },
        hard: {
          title: '午夜电梯',
          background: '高档公寓的电梯在午夜时分总是自动停在13楼，但13楼根本不存在。住户们开始感到恐惧，决定一探究竟。',
          setting: '现代高档公寓，神秘的午夜时分',
          objective: '解开电梯的诅咒，找出13楼的秘密'
        }
      },
      'adventure': {
        easy: {
          title: '寻宝游戏',
          background: '在一次团建活动中，公司组织了寻宝游戏。但游戏中的线索似乎指向了一个真实的宝藏，而不仅仅是游戏道具。',
          setting: '郊外度假村，阳光明媚的下午',
          objective: '跟随线索找到真正的宝藏，完成冒险'
        },
        medium: {
          title: '密室逃脱',
          background: '朋友们参加了一个高科技密室逃脱游戏，但游戏开始后发现，这不是普通的娱乐项目，而是一个真实的挑战。',
          setting: '高科技密室，充满机关和谜题',
          objective: '解开所有谜题，成功逃出密室'
        },
        hard: {
          title: '荒岛求生',
          background: '一群朋友的海岛旅行遭遇意外，被困在无人荒岛上。他们必须团结合作，利用有限的资源生存下去。',
          setting: '热带荒岛，与世隔绝的环境',
          objective: '在荒岛上生存，找到回家的方法'
        }
      },
      'comedy': {
        easy: {
          title: '搞笑聚会',
          background: '朋友聚会时，大家决定玩一个角色扮演游戏。每个人都要扮演一个搞笑的角色，但游戏过程中发生了意想不到的趣事。',
          setting: '温馨的家庭聚会，轻松愉快的氛围',
          objective: '在欢声笑语中完成角色扮演，享受快乐时光'
        },
        medium: {
          title: '办公室趣事',
          background: '公司年会上，员工们要表演节目。但排练过程中发生了一系列搞笑的意外，让整个节目变得更加有趣。',
          setting: '公司年会现场，热闹的庆祝氛围',
          objective: '克服各种搞笑困难，成功完成年会表演'
        },
        hard: {
          title: '婚礼乌龙',
          background: '一场婚礼因为各种意外变成了搞笑大会：司仪念错名字、戒指找不到、蛋糕倒了...但这些意外让婚礼变得更加难忘。',
          setting: '婚礼现场，充满意外和欢声笑语',
          objective: '在各种乌龙中完成婚礼，创造美好回忆'
        }
      },
      'fantasy': {
        easy: {
          title: '魔法学院',
          background: '在一所神秘的魔法学院里，学生们正在学习各种魔法技能。但有人发现学院里隐藏着一个古老的秘密。',
          setting: '充满魔法的学院，神秘而美丽',
          objective: '学习魔法，揭开学院的古老秘密'
        },
        medium: {
          title: '精灵森林',
          background: '一群冒险者进入了传说中的精灵森林，寻找失落的魔法水晶。但森林里的精灵们似乎在隐瞒什么重要的事情。',
          setting: '神秘的精灵森林，充满魔法生物',
          objective: '找到魔法水晶，与精灵们建立友谊'
        },
        hard: {
          title: '龙族传说',
          background: '古老的龙族即将苏醒，世界面临巨大的危机。勇士们必须找到传说中的神器，阻止灾难的发生。',
          setting: '奇幻世界，龙族的古老领域',
          objective: '找到神器，拯救世界免于龙族的威胁'
        }
      }
    };

    // 根据剧情类型选择故事
    const typeStories = mockStories[storyType] || mockStories['mystery'];
    const story = typeStories[difficulty] || typeStories['medium'];

    // 生成角色信息
    const characters = await this.generateCharacters(storyType, playerCount);

    return {
      storyInfo: {
        title: story.title,
        background: story.background,
        setting: story.setting,
        coreEvent: story.objective,
        winConditions: '找出真相，完成各自的目标'
      },
      characters: characters,
      timeline: [
        { phase: '开场介绍', duration: '10分钟', description: '角色介绍和背景说明' },
        { phase: '信息收集', duration: '20分钟', description: '玩家互相询问，收集线索' },
        { phase: '推理讨论', duration: '15分钟', description: '分析线索，推理真相' },
        { phase: '最终投票', duration: '10分钟', description: '投票决定结果' }
      ],
      truthQuestions: [
        '你认为谁最可疑？为什么？',
        '你有什么重要信息没有透露？',
        '你的真实动机是什么？',
        '你觉得案件的关键线索是什么？'
      ],
      miniGameTopics: ['神秘线索', '隐藏证据', '关键证人'],
      solution: {
        truth: '真相将在游戏过程中揭晓',
        culprit: '',
        motive: '',
        method: '',
        evidence: ''
      },
      playerCount: playerCount,
      estimatedDuration: difficulty === 'easy' ? '30-45分钟' : difficulty === 'medium' ? '45-60分钟' : '60-90分钟'
    };
  }

  /**
   * 生成角色信息
   */
  async generateCharacters(storyType, playerCount) {
    await this.simulateDelay();

    const characterTemplates = {
      'mystery': [
        { name: '张经理', role: '部门经理', description: '工作能力强，但野心勃勃', motive: '升职加薪' },
        { name: '李秘书', role: '总裁秘书', description: '细心谨慎，掌握内部信息', motive: '保护公司机密' },
        { name: '王程序员', role: '技术专家', description: '技术精湛，性格内向', motive: '专注技术发展' },
        { name: '陈保安', role: '安保主管', description: '责任心强，观察敏锐', motive: '维护公司安全' },
        { name: '刘会计', role: '财务主管', description: '精明能干，掌握财务信息', motive: '确保财务安全' },
        { name: '赵助理', role: '行政助理', description: '勤奋踏实，了解内部动态', motive: '维护工作稳定' }
      ],
      'romance': [
        { name: '林小雨', role: '文艺青年', description: '温柔善良，喜欢写诗', motive: '寻找真爱' },
        { name: '陈阳光', role: '摄影师', description: '阳光开朗，热爱生活', motive: '记录美好瞬间' },
        { name: '王梦琪', role: '咖啡师', description: '细腻温柔，善于倾听', motive: '经营温馨咖啡厅' },
        { name: '李浩然', role: '音乐老师', description: '才华横溢，浪漫多情', motive: '用音乐表达情感' },
        { name: '张诗涵', role: '书店老板', description: '知性优雅，热爱阅读', motive: '传播知识与美好' },
        { name: '刘星辰', role: '设计师', description: '创意无限，追求完美', motive: '创造美丽的作品' }
      ],
      'horror': [
        { name: '王探长', role: '警察', description: '经验丰富，冷静理性', motive: '维护正义' },
        { name: '李医生', role: '心理医生', description: '善于分析，洞察人心', motive: '帮助他人' },
        { name: '张管家', role: '古宅管家', description: '神秘莫测，知晓秘密', motive: '守护古宅' },
        { name: '陈记者', role: '调查记者', description: '好奇心强，追求真相', motive: '揭露真相' },
        { name: '刘学者', role: '历史学者', description: '博学多才，研究古迹', motive: '探索历史' },
        { name: '赵守夜', role: '夜班保安', description: '警觉敏锐，夜间巡逻', motive: '保护安全' }
      ],
      'adventure': [
        { name: '林探险', role: '探险家', description: '勇敢无畏，经验丰富', motive: '探索未知' },
        { name: '陈向导', role: '当地向导', description: '熟悉地形，机智灵活', motive: '帮助他人' },
        { name: '王医护', role: '随队医生', description: '医术精湛，关爱他人', motive: '救死扶伤' },
        { name: '李工程', role: '工程师', description: '技术专家，善于解决问题', motive: '克服技术难题' },
        { name: '张摄影', role: '摄影师', description: '记录冒险，热爱自然', motive: '拍摄精彩瞬间' },
        { name: '刘学生', role: '大学生', description: '年轻活力，学习能力强', motive: '增长见识' }
      ],
      'comedy': [
        { name: '王搞笑', role: '喜剧演员', description: '幽默风趣，善于搞笑', motive: '带给大家欢乐' },
        { name: '李开心', role: '活动策划', description: '创意无限，组织能力强', motive: '策划完美活动' },
        { name: '张乐天', role: '音响师', description: '技术过硬，性格开朗', motive: '营造欢乐氛围' },
        { name: '陈糊涂', role: '新手助理', description: '善良可爱，经常出错', motive: '努力学习成长' },
        { name: '刘机灵', role: '道具师', description: '心灵手巧，反应敏捷', motive: '制作精美道具' },
        { name: '赵热情', role: '主持人', description: '热情洋溢，善于调动气氛', motive: '让大家开心' }
      ],
      'fantasy': [
        { name: '艾莉娅', role: '精灵法师', description: '优雅神秘，精通自然魔法', motive: '保护森林' },
        { name: '雷恩', role: '人类骑士', description: '勇敢正义，守护弱者', motive: '维护正义' },
        { name: '索菲亚', role: '魔法学者', description: '博学睿智，研究古老魔法', motive: '探索魔法奥秘' },
        { name: '加雷斯', role: '矮人工匠', description: '技艺精湛，制作神器', motive: '打造完美武器' },
        { name: '露娜', role: '月之祭司', description: '神圣纯洁，治愈他人', motive: '传播光明' },
        { name: '达克', role: '暗影刺客', description: '神秘莫测，身手敏捷', motive: '完成神秘任务' }
      ]
    };

    const templates = characterTemplates[storyType] || characterTemplates['mystery'];
    
    // 随机选择角色
    const selectedCharacters = [];
    const shuffled = [...templates].sort(() => 0.5 - Math.random());
    
    for (let i = 0; i < Math.min(playerCount, shuffled.length); i++) {
      const char = shuffled[i];
      selectedCharacters.push({
        id: i + 1,
        name: char.name,
        role: char.role,
        title: char.role,
        description: char.description,
        motive: char.motive,
        preview: {
          tagline: `${char.description}，${char.motive}`
        },
        secrets: this.generateSecrets(char, storyType),
        relationships: this.generateRelationships(i, playerCount),
        publicInfo: {
          background: char.description,
          personality: this.generatePersonality(char),
          appearance: this.generateAppearance(char)
        },
        privateInfo: {
          realMotive: char.motive,
          hiddenSecrets: this.generateSecrets(char, storyType),
          specialAbilities: this.generateAbilities(char)
        }
      });
    }

    return selectedCharacters;
  }

  /**
   * 检测故事主题
   */
  detectTheme(storyContext) {
    const text = JSON.stringify(storyContext).toLowerCase();
    if (text.includes('宫廷') || text.includes('皇帝') || text.includes('古代')) {
      return '古代宫廷';
    } else if (text.includes('科幻') || text.includes('未来') || text.includes('ai') || text.includes('基地')) {
      return '科幻未来';
    } else {
      return '现代都市';
    }
  }

  /**
   * 生成角色秘密
   */
  generateSecrets(character, storyType) {
    const secretTemplates = {
      'mystery': [
        '发现了公司内部的违法行为',
        '知道某位同事的不当关系',
        '掌握着重要的财务信息',
        '目击了关键的犯罪证据'
      ],
      'romance': [
        '暗恋着某个人很久了',
        '收到过神秘的情书',
        '知道某对情侣的秘密',
        '曾经有过一段刻骨铭心的恋情'
      ],
      'horror': [
        '在深夜听到过奇怪的声音',
        '发现了古老的诅咒传说',
        '目击过超自然现象',
        '知道某个地方的恐怖历史'
      ],
      'adventure': [
        '拥有一张神秘的藏宝图',
        '知道某个隐藏的秘密通道',
        '掌握着古老的生存技能',
        '曾经历过危险的冒险'
      ],
      'comedy': [
        '有一个搞笑的童年经历',
        '经常在关键时刻出错',
        '拥有意想不到的才艺',
        '总是能在困境中找到乐趣'
      ],
      'fantasy': [
        '拥有隐藏的魔法能力',
        '知道古老的魔法咒语',
        '与神秘生物有过接触',
        '掌握着失传的魔法知识'
      ]
    };

    const secrets = secretTemplates[storyType] || secretTemplates['mystery'];
    return secrets[Math.floor(Math.random() * secrets.length)];
  }

  /**
   * 生成角色关系
   */
  generateRelationships(index, totalPlayers) {
    const relationships = [];
    const relationTypes = ['盟友', '竞争对手', '暗恋对象', '仇人', '合作伙伴'];
    
    for (let i = 0; i < totalPlayers; i++) {
      if (i !== index) {
        relationships.push({
          targetId: i + 1,
          relationship: relationTypes[Math.floor(Math.random() * relationTypes.length)]
        });
      }
    }
    
    return relationships;
  }

  /**
   * 生成线索
   */
  async generateClues(storyContext, characters) {
    await this.simulateDelay();

    const clueTemplates = [
      '在现场发现了一张神秘的纸条',
      '监控录像显示了可疑的身影',
      '证人提供了重要的证词',
      '在嫌疑人的物品中发现了关键证据',
      '时间线分析揭示了重要信息'
    ];

    const clues = [];
    for (let i = 0; i < 3; i++) {
      clues.push({
        id: i + 1,
        description: clueTemplates[i],
        importance: ['关键', '重要', '一般'][i],
        relatedCharacters: [characters[i % characters.length].id]
      });
    }

    return clues;
  }

  /**
   * 生成角色性格
   */
  generatePersonality(character) {
    const personalities = {
      '内务总管': '谨慎细致，善于观察',
      '皇帝宠妃': '聪明机智，善于交际',
      '禁军统领': '忠诚正直，行事果断',
      '御医': '沉稳理性，医者仁心',
      '朝廷重臣': '老谋深算，城府很深',
      '部门经理': '雄心勃勃，善于管理',
      '总裁秘书': '细心谨慎，忠于职守',
      '技术专家': '专注认真，逻辑性强',
      '安保主管': '警觉敏锐，责任心强',
      '财务主管': '精明能干，注重细节'
    };
    return personalities[character.role] || '性格复杂，难以捉摸';
  }

  /**
   * 生成角色外貌
   */
  generateAppearance(character) {
    const appearances = {
      '内务总管': '中年男性，身材瘦削，眼神精明',
      '皇帝宠妃': '年轻女性，容貌秀美，气质优雅',
      '禁军统领': '壮年男性，身材魁梧，气势威严',
      '御医': '中年男性，须发花白，神态慈祥',
      '朝廷重臣': '老年男性，仪表堂堂，威严庄重',
      '部门经理': '中年男性，西装革履，精神干练',
      '总裁秘书': '年轻女性，职业装扮，举止优雅',
      '技术专家': '青年男性，休闲装扮，戴着眼镜',
      '安保主管': '中年男性，身材健壮，目光锐利',
      '财务主管': '中年女性，穿着得体，表情严肃'
    };
    return appearances[character.role] || '外貌普通，没有特别之处';
  }

  /**
   * 生成角色特殊能力
   */
  generateAbilities(character) {
    const abilities = {
      '内务总管': ['信息收集', '人际关系网络'],
      '皇帝宠妃': ['魅力影响', '宫廷政治'],
      '禁军统领': ['武力威慑', '安全防护'],
      '御医': ['医学知识', '毒物识别'],
      '朝廷重臣': ['政治手腕', '权力运作'],
      '部门经理': ['团队管理', '商业谈判'],
      '总裁秘书': ['信息获取', '文档管理'],
      '技术专家': ['技术分析', '系统操作'],
      '安保主管': ['安全监控', '风险评估'],
      '财务主管': ['财务分析', '资金追踪']
    };
    return abilities[character.role] || ['观察力', '推理能力'];
  }

  /**
   * 检查服务状态
   */
  async checkServiceStatus() {
    await this.simulateDelay();
    
    return {
      status: 'mock',
      message: '当前使用模拟AI服务',
      features: {
        storyGeneration: true,
        characterGeneration: true,
        clueGeneration: true
      }
    };
  }
}

module.exports = MockAIService;
