# Moonshot AI 设置指南

## 🎯 **问题诊断**

根据你的截图，API密钥存在于"default"项目中，但仍然返回401错误。这通常是以下原因：

### 常见原因分析

1. **项目余额不足** ⭐ 最可能的原因
   - 每个项目都有独立的余额
   - 需要为"default"项目充值

2. **项目状态异常**
   - 项目可能未激活
   - 项目可能被暂停

3. **API密钥权限问题**
   - 密钥可能没有调用权限
   - 密钥可能有使用限制

## 🔧 **解决步骤**

### 步骤1: 检查项目余额

1. 在Moonshot AI控制台中
2. 点击左侧"项目总览"
3. 查看"default"项目的余额
4. 如果余额为0或不足，需要充值

### 步骤2: 检查项目状态

1. 确认"default"项目状态为"正常"
2. 检查项目是否有任何限制
3. 确认项目已完成必要的认证

### 步骤3: 检查API密钥

1. 在"API Key 管理"页面
2. 确认密钥状态为"有效"
3. 检查密钥的权限设置
4. 确认没有使用限制

### 步骤4: 测试API连接

使用以下方法测试：

#### A. 在控制台测试
- 使用控制台的API测试工具
- 发送一个简单的测试请求

#### B. 在应用中测试
1. 打开微信小程序
2. 进入"个人中心" → "API设置"
3. 输入新的API密钥：`sk-rFun7AywY7jUUdJAtUBbFD`
4. 点击"测试连接"

## 💰 **充值指南**

如果是余额不足问题：

### 1. 进入充值页面
- 在控制台点击"计费管理"或"充值"
- 选择合适的充值金额

### 2. 选择充值方式
- 支付宝
- 微信支付
- 银行卡

### 3. 完成充值
- 按照提示完成支付
- 等待余额更新（通常几分钟内到账）

## 🔑 **API密钥最佳实践**

### 安全使用
- ✅ 只在安全的环境中使用
- ✅ 定期轮换密钥
- ✅ 监控使用情况
- ❌ 不要在公开场所展示
- ❌ 不要提交到代码仓库

### 权限管理
- 为不同用途创建不同的密钥
- 设置合适的使用限制
- 定期审查密钥权限

## 🚨 **故障排除**

### 如果仍然出现401错误：

1. **等待几分钟** - 充值后可能需要时间生效
2. **重新生成密钥** - 删除旧密钥，生成新密钥
3. **联系客服** - 如果问题持续存在
4. **检查网络** - 确认网络连接正常

### 错误代码对照表

| 错误代码 | 含义 | 解决方法 |
|---------|------|----------|
| 401 | API密钥无效或余额不足 | 检查密钥和余额 |
| 402 | 余额不足 | 充值账户 |
| 403 | 访问被拒绝 | 检查权限 |
| 429 | 请求过频 | 降低请求频率 |

## 📞 **获取帮助**

### 官方渠道
- **控制台**: https://platform.moonshot.cn/console
- **文档**: https://platform.moonshot.cn/docs
- **客服**: 通过控制台联系在线客服

### 社区支持
- 查看官方文档和FAQ
- 参考社区讨论和解决方案

## 🎉 **成功标志**

当API配置成功后，你应该看到：
- ✅ API测试返回200状态码
- ✅ 收到AI的正常响应
- ✅ 应用功能正常运行

记住：**大多数401错误都是余额不足导致的！** 💰
