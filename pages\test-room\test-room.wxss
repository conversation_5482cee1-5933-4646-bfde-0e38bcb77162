/* 测试房间功能页面样式 */
.test-container {
  padding: 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
}

.user-info, .room-info {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
}

.label {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.value {
  display: block;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.button-group {
  margin-top: 60rpx;
}

.test-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
}

.test-btn:active {
  background: rgba(255, 255, 255, 0.3);
}

.test-btn.primary {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
}

.test-btn.primary:active {
  background: rgba(255, 255, 255, 1);
}

.test-btn[disabled] {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
  border-color: rgba(255, 255, 255, 0.1);
}
