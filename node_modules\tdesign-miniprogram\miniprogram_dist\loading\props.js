const props={delay:{type:Number,value:0},duration:{type:Number,value:800},fullscreen:{type:Boolean,value:!1},indicator:{type:Boolean,value:!0},inheritColor:{type:Boolean,value:!1},layout:{type:String,value:"horizontal"},loading:{type:Boolean,value:!0},pause:{type:Boolean,value:!1},progress:{type:Number},reverse:{type:Boolean},size:{type:String,value:"20px"},text:{type:String},theme:{type:String,value:"circular"}};export default props;