/* AI剧情生成页面样式 */
.story-generator-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 32rpx;
  position: relative;
}

/* 返回按钮 */
.back-button {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 40rpx;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 32rpx;
  width: fit-content;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.back-button:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

.back-icon {
  font-size: 32rpx;
  color: white;
  font-weight: bold;
}

.back-text {
  font-size: 28rpx;
  color: white;
  font-weight: 500;
}

/* 标题区域 */
.header-section {
  text-align: center;
  margin-bottom: 48rpx;
}

.title-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  display: block;
  filter: drop-shadow(0 8rpx 16rpx rgba(0, 0, 0, 0.2));
}

.main-title {
  font-size: 56rpx;
  font-weight: 800;
  color: white;
  margin-bottom: 16rpx;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

/* 艺术化卡片 */
.artistic-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(25rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.1);
}

/* 区域标题 */
.section-title {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.title-icon {
  font-size: 36rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
}

/* 剧情类型网格 */
.story-types-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.story-type-card {
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.story-type-card:active {
  transform: scale(0.95);
}

.story-type-card.selected {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 8rpx 24rpx rgba(255, 255, 255, 0.2);
}

.type-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
  display: block;
}

.type-name {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 8rpx;
}

.type-desc {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
}

/* 设置项 */
.setting-item {
  margin-bottom: 32rpx;
}

.setting-label {
  font-size: 28rpx;
  font-weight: 500;
  color: white;
  margin-bottom: 16rpx;
}

/* 选择器 */
.picker-display {
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  font-size: 28rpx;
}

.picker-arrow {
  color: rgba(255, 255, 255, 0.6);
  font-size: 24rpx;
}

/* 复杂程度选择器 */
.complexity-selector {
  display: flex;
  gap: 16rpx;
}

.complexity-item {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  padding: 24rpx 16rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.complexity-item.selected {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.complexity-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.complexity-name {
  font-size: 24rpx;
  color: white;
  font-weight: 500;
}

/* 特殊元素 */
.special-elements {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.element-tag {
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 32rpx;
  padding: 16rpx 24rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  transition: all 0.3s ease;
}

.element-tag.selected {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.element-icon {
  font-size: 24rpx;
}

.element-name {
  font-size: 24rpx;
  color: white;
  font-weight: 500;
}

/* AI优化按钮 */
.ai-optimize-section {
  margin: 48rpx 0;
}

.ai-optimize-btn {
  width: 100%;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  border: none;
  border-radius: 32rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  box-shadow: 0 16rpx 48rpx rgba(255, 107, 107, 0.4);
  transition: all 0.3s ease;
}

.ai-optimize-btn:active {
  transform: translateY(4rpx) scale(0.98);
}

.ai-optimize-btn.optimizing {
  background: linear-gradient(135deg, #ccc 0%, #999 100%);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
}

.btn-icon {
  font-size: 36rpx;
  color: white;
}

.btn-icon.loading {
  animation: rotate 1s linear infinite;
}

.btn-text {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
}

/* 优化后的剧情显示区域 */
.optimized-story-section {
  margin-top: 32rpx;
}

.story-content {
  margin: 24rpx 0;
}

.story-textarea {
  width: 100%;
  min-height: 400rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 进度显示 */
.progress-title {
  font-size: 28rpx;
  color: white;
  text-align: center;
  margin-bottom: 32rpx;
  font-weight: 500;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  opacity: 0.5;
  transition: all 0.3s ease;
}

.step-item.completed {
  opacity: 1;
}

.step-icon {
  font-size: 32rpx;
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-item.completed .step-icon {
  background: rgba(255, 255, 255, 0.2);
}

.step-text {
  font-size: 22rpx;
  color: white;
  font-weight: 500;
}

.progress-bar {
  height: 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4ecdc4 0%, #44a08d 100%);
  border-radius: 4rpx;
  transition: width 0.5s ease;
}

/* 剧情预览 */
.story-preview {
  margin-bottom: 32rpx;
}

.story-title {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 16rpx;
  text-align: center;
}

.story-summary {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 24rpx;
}

.story-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

.detail-value {
  font-size: 24rpx;
  color: white;
  font-weight: 500;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  border: none;
  border-radius: 24rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.action-btn.primary {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(78, 205, 196, 0.3);
}

.action-btn:active {
  transform: scale(0.95);
}

/* 加载遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: rgba(255, 255, 255, 0.9);
  padding: 48rpx;
  border-radius: 24rpx;
  backdrop-filter: blur(10rpx);
}

.loading-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 动画 */
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
