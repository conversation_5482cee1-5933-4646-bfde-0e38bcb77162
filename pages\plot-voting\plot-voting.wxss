/* 剧情分支投票页面样式 - 艺术化设计 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #2c1810 100%);
  position: relative;
  overflow: hidden;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

.container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 30%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(0, 191, 255, 0.08) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(40rpx);
  border-bottom: 2rpx solid rgba(255, 255, 255, 0.2);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 40rpx;
  padding-top: calc(20rpx + env(safe-area-inset-top));
  height: 88rpx;
}

.nav-left, .nav-right {
  width: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-title {
  color: white;
  font-size: 36rpx;
  font-weight: 600;
  text-shadow: 0 0 20rpx rgba(255, 255, 255, 0.5);
}

.timer {
  color: #ffd700;
  font-size: 28rpx;
  font-weight: 600;
  background: rgba(255, 215, 0, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 2rpx solid rgba(255, 215, 0, 0.3);
}

/* 页面内容 */
.page-content {
  padding: 200rpx 32rpx 200rpx;
  position: relative;
  z-index: 1;
}

/* 艺术化卡片 */
.artistic-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(30rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 48rpx;
  padding: 48rpx;
  margin-bottom: 40rpx;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 24rpx 80rpx rgba(0, 0, 0, 0.15),
    0 8rpx 32rpx rgba(0, 0, 0, 0.1),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  animation: fadeInScale 0.6s ease-out;
}

.artistic-card::before {
  content: '';
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff6b6b);
  border-radius: 50rpx;
  z-index: -1;
  opacity: 0.6;
  filter: blur(8rpx);
  animation: holographic 6s ease infinite;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes holographic {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* 章节标题 */
.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.section-icon {
  font-size: 40rpx;
  margin-right: 16rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  text-shadow: 0 0 20rpx rgba(255, 255, 255, 0.6);
}

/* 剧情内容 */
.story-content {
  padding: 16rpx 0;
}

.story-text {
  font-size: 32rpx;
  line-height: 1.8;
  color: rgba(255, 255, 255, 0.9);
  text-align: justify;
}

/* 投票选项 */
.vote-options {
  padding: 16rpx 0;
}

.vote-option {
  background: rgba(255, 255, 255, 0.08);
  border: 2rpx solid rgba(255, 255, 255, 0.15);
  border-radius: 32rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.vote-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, transparent 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.vote-option:hover::before,
.vote-option.selected::before {
  opacity: 1;
}

.vote-option.selected {
  border-color: #ffd700;
  background: rgba(255, 215, 0, 0.1);
  transform: scale(1.02);
  box-shadow: 0 16rpx 40rpx rgba(255, 215, 0, 0.2);
}

.option-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.option-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.option-title {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  flex: 1;
}

.option-description {
  font-size: 28rpx;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 16rpx;
}

.option-consequence {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  padding: 16rpx;
  border-left: 6rpx solid #ffd700;
}

.consequence-label {
  font-size: 24rpx;
  color: #ffd700;
  font-weight: 600;
  margin-right: 8rpx;
}

.consequence-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* 投票统计 */
.stats-card {
  animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(30rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.vote-stats {
  padding: 16rpx 0;
}

.stat-item {
  margin-bottom: 32rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2rpx);
}

.stat-option {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-bar {
  height: 20rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 12rpx;
  position: relative;
}

.stat-fill {
  height: 100%;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 10rpx;
  transition: width 1.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.stat-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.stat-count {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* 投票提示 */
.vote-tip {
  margin-top: 24rpx;
  padding: 16rpx 20rpx;
  background: rgba(76, 175, 80, 0.2);
  border: 1rpx solid rgba(76, 175, 80, 0.3);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.tip-icon {
  font-size: 28rpx;
  color: #4caf50;
}

.tip-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 40rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(40rpx);
  border-top: 2rpx solid rgba(255, 255, 255, 0.2);
  display: flex;
  gap: 24rpx;
  z-index: 1000;
}

.action-btn {
  flex: 1;
  padding: 32rpx;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(20rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.action-btn.primary {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #333;
  box-shadow: 0 12rpx 40rpx rgba(255, 215, 0, 0.4);
}

.action-btn.primary.voted {
  background: linear-gradient(45deg, #4caf50, #45a049);
  color: white;
  box-shadow: 0 12rpx 40rpx rgba(76, 175, 80, 0.4);
}

.action-btn.primary.voted:disabled {
  background: linear-gradient(45deg, #4caf50, #45a049) !important;
  color: white !important;
  opacity: 0.9;
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.action-btn:disabled {
  background: rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.5) !important;
  cursor: not-allowed;
}

.action-btn:hover:not(:disabled) {
  transform: translateY(-4rpx);
  box-shadow: 0 16rpx 50rpx rgba(0, 0, 0, 0.2);
}

.btn-icon {
  font-size: 28rpx;
}
