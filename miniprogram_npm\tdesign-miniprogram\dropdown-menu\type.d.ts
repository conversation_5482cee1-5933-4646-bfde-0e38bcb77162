export interface TdDropdownMenuProps {
    arrowIcon?: {
        type: null;
        value?: string | object;
    };
    closeOnClickOverlay?: {
        type: BooleanConstructor;
        value?: boolean;
    };
    duration?: {
        type: null;
        value?: string | number;
    };
    showOverlay?: {
        type: BooleanConstructor;
        value?: boolean;
    };
    zIndex?: {
        type: NumberConstructor;
        value?: number;
    };
}
