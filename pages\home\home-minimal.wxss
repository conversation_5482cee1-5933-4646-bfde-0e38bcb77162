/* 极简风格首页 */
.home-container {
  min-height: 100vh;
  background: #ffffff;
  padding: 0;
  display: flex;
  flex-direction: column;
}

/* 极简主标题区域 */
.hero-section {
  text-align: center;
  padding: 200rpx 40rpx 120rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

/* 应用Logo */
.app-logo {
  margin-bottom: 32rpx;
}

.logo-text {
  font-size: 64rpx;
  font-weight: 300;
  color: #000000;
  letter-spacing: 2rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'PingFang SC', sans-serif;
}

/* 应用副标题 */
.app-subtitle {
  font-size: 28rpx;
  color: #666666;
  font-weight: 400;
  letter-spacing: 1rpx;
  margin-bottom: 80rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
}

/* 极简主操作按钮 */
.main-actions {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  padding: 0 40rpx;
  margin-bottom: 80rpx;
}

.minimal-btn {
  width: 100%;
  height: 96rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
}

.primary-btn {
  background: #000000;
  color: #ffffff;
}

.primary-btn:active {
  background: #333333;
  transform: scale(0.98);
}

.secondary-btn {
  background: #f5f5f5;
  color: #000000;
  border: 1rpx solid #e0e0e0;
}

.secondary-btn:active {
  background: #e8e8e8;
  transform: scale(0.98);
}

/* 快速加入区域 */
.quick-join-section {
  display: flex;
  gap: 16rpx;
  padding: 0 40rpx;
  margin-bottom: 60rpx;
}

.minimal-input {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
}

.minimal-input:focus {
  border-color: #000000;
}

.join-btn {
  width: 120rpx;
  height: 80rpx;
  background: #000000;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
}

.join-btn:active {
  background: #333333;
  transform: scale(0.98);
}

.join-btn.disabled {
  background: #cccccc;
  color: #999999;
}

/* 房间列表 */
.rooms-section {
  padding: 0 40rpx;
  margin-bottom: 60rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
  margin-bottom: 32rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'PingFang SC', sans-serif;
}

.rooms-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.room-card {
  background: #ffffff;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s ease;
}

.room-card:active {
  background: #f8f8f8;
  transform: scale(0.98);
}

.room-info {
  flex: 1;
}

.room-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #000000;
  margin-bottom: 8rpx;
  display: block;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
}

.room-players {
  font-size: 24rpx;
  color: #666666;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
}

.room-status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-weight: 500;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
}

.room-status.waiting {
  background: #e8f5e8;
  color: #2e7d32;
}

.room-status.playing {
  background: #fff3e0;
  color: #f57c00;
}

/* 开发测试区域 */
.dev-test-section {
  padding: 0 40rpx;
  margin-bottom: 60rpx;
}

.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.test-btn {
  padding: 16rpx 24rpx;
  background: #f5f5f5;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #000000;
  font-weight: 500;
  transition: all 0.2s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
}

.test-btn:active {
  background: #e8e8e8;
  transform: scale(0.95);
}

.test-btn.danger {
  background: #ffebee;
  color: #d32f2f;
  border-color: #ffcdd2;
}

.test-btn.danger:active {
  background: #ffcdd2;
}

/* 开发测试切换按钮 */
.dev-toggle-btn {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  width: 120rpx;
  height: 80rpx;
  background: #000000;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
  z-index: 1000;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
}

.dev-toggle-btn:active {
  background: #333333;
  transform: scale(0.95);
}

/* 加载状态 */
.loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 32rpx 48rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  z-index: 9999;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'PingFang SC', sans-serif;
}
