<!-- 剧情分支投票页面 -->
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="nav-left"></view>
      <view class="nav-title">🎭 剧情分支</view>
      <view class="nav-right">
        <text class="timer">{{timeLeft}}</text>
      </view>
    </view>
  </view>

  <!-- 页面内容 -->
  <view class="page-content">
    <!-- 剧情背景卡片 -->
    <view class="story-card artistic-card">
      <view class="section-header">
        <text class="section-icon">📖</text>
        <text class="section-title">剧情发展</text>
      </view>
      <view class="story-content">
        <text class="story-text">{{currentStory}}</text>
      </view>
    </view>

    <!-- 分支选项卡片 -->
    <view class="voting-card artistic-card">
      <view class="section-header">
        <text class="section-icon">🎯</text>
        <text class="section-title">剧情分支投票</text>
      </view>

      <view class="vote-options">
        <view
          class="vote-option {{selectedOption === index ? 'selected' : ''}}"
          wx:for="{{plotOptions}}"
          wx:key="index"
          bindtap="selectOption"
          data-index="{{index}}"
        >
          <view class="option-header">
            <view class="option-icon">{{item.icon}}</view>
            <view class="option-title">{{item.title}}</view>
          </view>
          <view class="option-description">{{item.description}}</view>
          <view class="option-consequence">
            <text class="consequence-label">可能结果：</text>
            <text class="consequence-text">{{item.consequence}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 投票统计 -->
    <view class="stats-card artistic-card" wx:if="{{hasVoted || showStats}}">
      <view class="section-header">
        <text class="section-icon">📊</text>
        <text class="section-title">投票统计 ({{totalVotes}}票)</text>
      </view>
      <view class="vote-stats" wx:if="{{voteStats && voteStats.length > 0}}">
        <view class="stat-item" wx:for="{{voteStats}}" wx:key="index">
          <view class="stat-option">
            <text>{{item.title}}</text>
            <text class="stat-count">{{item.votes}}票 ({{item.percentage}}%)</text>
          </view>
          <view class="stat-bar">
            <view class="stat-fill" style="width: {{item.percentage}}%"></view>
          </view>
        </view>
      </view>

      <!-- 投票提示 -->
      <view class="vote-tip" wx:if="{{hasVoted}}">
        <text class="tip-icon">✅</text>
        <text class="tip-text">您已投票，等待其他玩家...</text>
      </view>

      <!-- 调试信息 -->
      <view class="debug-info" wx:if="{{!voteStats || voteStats.length === 0}}" style="padding: 20rpx; background: rgba(255,0,0,0.2); margin-top: 20rpx; border-radius: 10rpx;">
        <text style="color: white; font-size: 24rpx;">调试: voteStats为空或未定义</text>
      </view>
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="bottom-actions">
    <button class="action-btn secondary" bindtap="showPlotDetails">
      <text class="btn-icon">🔍</text>
      <text>查看详情</text>
    </button>
    <button
      class="action-btn primary {{hasVoted ? 'voted' : ''}}"
      bindtap="submitVote"
      disabled="{{selectedOption === null || hasVoted}}"
    >
      <text class="btn-icon">{{hasVoted ? '✅' : '🗳️'}}</text>
      <text>{{hasVoted ? '投票完成' : (selectedOption !== null ? '确认投票' : '请选择选项')}}</text>
    </button>
  </view>

  <!-- 调试按钮 -->
  <view style="position: fixed; bottom: 200rpx; right: 20rpx; z-index: 1000;">
    <button
      style="background: rgba(255,0,0,0.7); color: white; font-size: 24rpx; padding: 10rpx;"
      bindtap="debugShowStats"
    >
      调试统计
    </button>
  </view>
</view>