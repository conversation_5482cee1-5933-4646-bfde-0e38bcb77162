// AI剧本生成页面
const aiService = require('../../utils/ai-service-simple');

Page({
  data: {
    roomId: '',
    generating: false,
    progress: 0,
    currentStep: '',
    scriptData: null,
    generationSteps: [
      { id: 1, name: '分析需求', desc: '分析游戏参数和玩家偏好' },
      { id: 2, name: '构建世界观', desc: '创建故事背景和设定' },
      { id: 3, name: '设计角色', desc: '生成个性化角色和关系' },
      { id: 4, name: '编织剧情', desc: '构建悬疑情节和线索' },
      { id: 5, name: '完善细节', desc: '优化对话和互动元素' }
    ],
    currentStepIndex: 0
  },

  onLoad(options) {
    const { roomId } = options;
    if (roomId) {
      this.setData({ roomId });
      // 显示服务选择对话框
      this.showServiceSelection();
    }
  },

  // 显示服务选择
  showServiceSelection() {
    wx.showModal({
      title: '🤖 选择生成方式',
      content: '请选择剧本生成方式：\n\n🤖 AI服务：真正的AI生成，个性化程度高\n🎭 模拟服务：快速稳定，质量优秀\n\n注意：AI服务可能有请求限制',
      showCancel: true,
      cancelText: '🎭 模拟服务',
      confirmText: '🤖 AI服务',
      success: (res) => {
        if (res.confirm) {
          // 用户选择AI服务
          this.startGeneration('ai');
        } else {
          // 用户选择模拟服务
          this.startGeneration('mock');
        }
      }
    });
  },

  // 开始AI剧本生成
  async startGeneration(serviceType = 'ai') {
    this.setData({ 
      generating: true,
      progress: 0,
      currentStepIndex: 0
    });

    try {
      // 模拟生成步骤
      for (let i = 0; i < this.data.generationSteps.length; i++) {
        const step = this.data.generationSteps[i];
        this.setData({
          currentStep: step.name,
          currentStepIndex: i,
          progress: Math.round((i / this.data.generationSteps.length) * 80) // 80%用于步骤进度
        });

        // 模拟每个步骤的处理时间
        await this.delay(1000 + Math.random() * 1000);
      }

      // 实际调用AI生成
      this.setData({
        currentStep: serviceType === 'mock' ? '🎭 模拟服务创作剧本...' : '🤖 AI正在创作剧本...',
        progress: 85
      });

      // 设置服务模式
      if (serviceType === 'ai') {
        aiService.forceUseAI();
      } else {
        aiService.forceUseMock();
      }

      const scriptResult = await aiService.generateScript({
        storyType: 'mystery',
        playerCount: 6,
        difficulty: 'medium',
        theme: '现代都市'
      });

      this.handleScriptResult(scriptResult);
    } catch (error) {
      this.handleGenerationError(error);
    }
  },

  // 处理剧本生成结果
  handleScriptResult(scriptResult) {
    try {
      console.log('📖 剧本生成结果:', scriptResult);

      // 检查结果格式
      let scriptData;
      if (scriptResult.success) {
        // 新格式：包含success字段
        scriptData = scriptResult.scriptData;
      } else if (scriptResult.storyInfo) {
        // 旧格式：直接返回剧本数据
        scriptData = scriptResult;
      } else {
        throw new Error(scriptResult.error || '剧本生成失败');
      }

      if (scriptData && scriptData.storyInfo) {
        // 保存剧本数据
        wx.setStorageSync(`script_${this.data.roomId}`, scriptData);

        this.setData({
          scriptData: scriptData,
          progress: 100,
          currentStep: '✅ 剧本生成完成'
        });

        // 显示成功信息
        setTimeout(() => {
          this.showGenerationResult(scriptData);
        }, 1000);

      } else {
        throw new Error('剧本数据格式错误');
      }

    } catch (error) {
      this.handleGenerationError(error);
    }
  },

  // 处理生成错误
  handleGenerationError(error) {
    console.error('❌ 剧本生成失败:', error);
    this.setData({
      generating: false,
      currentStep: '❌ 生成失败'
    });

    // 尝试使用默认剧本数据作为备选
    const defaultScript = this.getDefaultScript();

    wx.showModal({
      title: '生成失败',
      content: `剧本生成失败: ${error.message}\n\n是否使用默认剧本继续？`,
      showCancel: true,
      cancelText: '重试',
      confirmText: '使用默认',
      success: (res) => {
        if (res.confirm) {
          // 使用默认剧本
          wx.setStorageSync(`script_${this.data.roomId}`, defaultScript);
          this.setData({
            scriptData: defaultScript,
            progress: 100,
            currentStep: '✅ 使用默认剧本'
          });
          setTimeout(() => {
            this.showGenerationResult(defaultScript);
          }, 1000);
        } else {
          this.showServiceSelection();
        }
      }
    });
  },

  // 显示生成结果
  showGenerationResult(scriptData) {
    const title = scriptData.storyInfo?.title || '神秘剧本';
    const characterCount = scriptData.characters?.length || 0;
    
    wx.showModal({
      title: '🎉 剧本生成成功',
      content: `剧本标题: ${title}\n角色数量: ${characterCount}个\n\n即将进入角色分配阶段`,
      showCancel: false,
      confirmText: '开始分配角色',
      success: () => {
        this.goToRoleAssignment();
      }
    });
  },

  // 跳转到角色分配
  goToRoleAssignment() {
    wx.redirectTo({
      url: `/pages/role-assignment/role-assignment?roomId=${this.data.roomId}`
    });
  },

  // 重新生成
  regenerateScript() {
    wx.showModal({
      title: '重新生成',
      content: '确定要重新生成剧本吗？当前进度将丢失。',
      showCancel: true,
      cancelText: '取消',
      confirmText: '确定',
      success: (res) => {
        if (res.confirm) {
          this.startGeneration();
        }
      }
    });
  },

  // 查看剧本详情
  viewScriptDetails() {
    if (!this.data.scriptData) return;

    const details = `标题: ${this.data.scriptData.storyInfo?.title || '未知'}
背景: ${this.data.scriptData.storyInfo?.background || '暂无'}
设定: ${this.data.scriptData.storyInfo?.setting || '暂无'}
角色数量: ${this.data.scriptData.characters?.length || 0}个`;

    wx.showModal({
      title: '剧本详情',
      content: details,
      showCancel: false
    });
  },

  // 获取默认剧本数据
  getDefaultScript() {
    return {
      storyInfo: {
        title: '神秘庄园谋杀案',
        background: '在一个风雨交加的夜晚，庄园主人神秘死亡。所有的客人都有嫌疑，真相隐藏在重重迷雾之中...',
        setting: '19世纪末的英国乡村庄园',
        coreEvent: '庄园主人的神秘死亡',
        theme: '神秘庄园'
      },
      characters: [
        {
          id: 'char_001',
          name: '艾米丽·哈特',
          title: '庄园女主人',
          background: '优雅的庄园女主人，与死者有着复杂的关系',
          secrets: ['隐藏着一个重要的秘密'],
          preview: {
            tagline: '优雅的女主人',
            intrigue: '她的微笑背后隐藏着什么？'
          }
        },
        {
          id: 'char_002',
          name: '詹姆斯·威尔逊',
          title: '忠诚的管家',
          background: '在庄园工作多年的老管家，了解所有人的秘密',
          secrets: ['知道庄园的所有秘密'],
          preview: {
            tagline: '忠诚的管家',
            intrigue: '他究竟知道多少真相？'
          }
        },
        {
          id: 'char_003',
          name: '维克多·布莱克',
          title: '神秘访客',
          background: '突然到访的神秘客人，似乎与死者有过节',
          secrets: ['与死者的恩怨'],
          preview: {
            tagline: '神秘访客',
            intrigue: '他为什么在这个时候出现？'
          }
        }
      ],
      truthQuestions: [
        '你认为谁最可疑？',
        '你隐藏了什么秘密？',
        '你与死者的真实关系是什么？'
      ],
      miniGameTopics: ['神秘线索', '隐藏证据', '关键证人']
    };
  },

  // 延迟函数
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  // 返回上一页
  goBack() {
    if (this.data.generating) {
      wx.showModal({
        title: '确认退出',
        content: '剧本正在生成中，确定要退出吗？',
        showCancel: true,
        cancelText: '继续生成',
        confirmText: '确定退出',
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack();
          }
        }
      });
    } else {
      wx.navigateBack();
    }
  }
});
