<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI社交推理游戏 - 原型展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* iPhone 15 Pro 系统UI适配 */
        :root {
            /* iPhone 15 Pro 实际可用区域 */
            --screen-width: 393px;
            --screen-height: 852px;
            --status-bar-height: 44px;  /* 状态栏+Dynamic Island */
            --safe-area-top: 44px;
            --safe-area-bottom: 34px;   /* 底部指示器 */

            /* 实际内容区域 */
            --content-height: calc(852px - 44px - 34px); /* 774px */

            /* 新增设计变量 */
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --mystery-gradient: linear-gradient(135deg, #0a0a2e 0%, #16213e 50%, #0f3460 100%);
            --gold-accent: #ffd700;
            --shadow-mystery: 0 16px 48px rgba(0, 0, 0, 0.3);
            --shadow-glow: 0 0 30px rgba(255, 215, 0, 0.3);
        }

        body {
            font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #2c1810 100%);
            color: #ffffff;
            overflow-x: auto;
            padding: 20px;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 30%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(0, 191, 255, 0.08) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .container {
            display: flex;
            gap: 20px;
            min-width: fit-content;
            padding-bottom: 20px;
        }

        .phone-frame {
            width: var(--screen-width);
            height: var(--screen-height);
            background: linear-gradient(145deg, #1a1a1a, #000000);
            border-radius: 40px;
            padding: 8px;
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.5),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
            flex-shrink: 0;
            backdrop-filter: blur(10px);
        }

        .phone-frame::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
            border-radius: 42px;
            z-index: -1;
            opacity: 0.3;
            filter: blur(6px);
        }

        .phone-screen {
            width: 100%;
            height: 100%;
            background: #ffffff;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }

        .status-bar {
            height: var(--status-bar-height);
            background: #000000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 30px;
            color: #ffffff;
            font-size: 14px;
            font-weight: 600;
            position: relative;
            z-index: 1000;
            /* 避开Dynamic Island区域 */
        }

        .status-bar::before {
            content: '';
            position: absolute;
            top: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 126px;
            height: 30px;
            background: #000000;
            border-radius: 20px;
            /* Dynamic Island区域遮罩 */
        }

        /* 页面容器适配 */
        .page-container {
            min-height: calc(100vh - var(--safe-area-top) - var(--safe-area-bottom));
            padding-top: 0;
            padding-bottom: var(--safe-area-bottom);
        }

        .page-content {
            height: calc(100% - var(--status-bar-height));
            background: linear-gradient(180deg, #000428 0%, #004e92 100%);
            overflow-y: auto;
            position: relative;
            /* 确保内容不被系统UI遮挡 */
            padding-bottom: var(--safe-area-bottom);
        }

        /* 地球元素 */
        .page-content::before {
            content: '';
            position: absolute;
            bottom: -400px;
            left: 50%;
            transform: translateX(-50%);
            width: 1200px;
            height: 1200px;
            background: radial-gradient(circle, rgba(135, 206, 235, 0.7) 0%, rgba(65, 105, 225, 0.8) 70%, rgba(25, 25, 112, 0.9) 100%);
            border-radius: 50%;
            box-shadow: 0 0 200px rgba(135, 206, 235, 0.3);
            z-index: 1;
            pointer-events: none;
        }

        /* 星星效果 */
        .page-content::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.2);
            z-index: 1;
            pointer-events: none;
        }

        /* 固定头部适配 */
        .page-header {
            background: #0052d9;
            color: white;
            padding: 16px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 8px rgba(0, 82, 217, 0.2);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
        }

        .back-btn {
            width: 24px;
            height: 24px;
            cursor: pointer;
        }

        .page-body {
            padding: 20px;
            min-height: calc(100% - 72px);
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        }

        .btn-primary {
            background: #0052d9;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: #0041a8;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: transparent;
            color: #0052d9;
            border: 1px solid #0052d9;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s ease;
            margin-top: 12px;
        }

        .input-field {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            margin-bottom: 16px;
            transition: border-color 0.3s ease;
        }

        .input-field:focus {
            outline: none;
            border-color: #0052d9;
        }

        .room-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .room-info h3 {
            color: #333;
            font-size: 16px;
            margin-bottom: 4px;
        }

        .room-info p {
            color: #666;
            font-size: 14px;
        }

        .room-status {
            background: #52c41a;
            color: white;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
        }

        .player-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-top: 20px;
        }

        .player-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .player-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #0052d9;
            margin: 0 auto 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .player-name {
            color: #333;
            font-size: 14px;
            font-weight: 500;
        }

        .role-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 16px;
            padding: 24px;
            text-align: center;
            margin-bottom: 20px;
        }

        .role-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .role-desc {
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.5;
        }

        .clue-item {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
        }

        .clue-item h4 {
            color: #856404;
            font-size: 14px;
            margin-bottom: 4px;
        }

        .clue-item p {
            color: #856404;
            font-size: 13px;
            line-height: 1.4;
        }

        .chat-container {
            height: 400px;
            background: white;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-messages {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
        }

        .message {
            margin-bottom: 12px;
        }

        .message-sender {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }

        .message-content {
            background: #f0f0f0;
            padding: 8px 12px;
            border-radius: 12px;
            font-size: 14px;
            color: #333;
        }

        .message.own .message-content {
            background: #0052d9;
            color: white;
            margin-left: auto;
            max-width: 80%;
        }

        .chat-input {
            display: flex;
            padding: 12px;
            border-top: 1px solid #e0e0e0;
            gap: 8px;
        }

        .chat-input input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #e0e0e0;
            border-radius: 20px;
            font-size: 14px;
        }

        .send-btn {
            background: #0052d9;
            color: white;
            border: none;
            border-radius: 20px;
            padding: 8px 16px;
            font-size: 14px;
            cursor: pointer;
        }

        .game-canvas {
            width: 100%;
            height: 200px;
            background: white;
            border: 2px dashed #e0e0e0;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
            margin-bottom: 16px;
        }

        .vote-option {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .vote-option:hover {
            border-color: #0052d9;
        }

        .vote-option.selected {
            border-color: #0052d9;
            background: #f0f7ff;
        }

        .vote-option h4 {
            color: #333;
            font-size: 16px;
            margin-bottom: 8px;
        }

        .vote-option p {
            color: #666;
            font-size: 14px;
            line-height: 1.4;
        }

        .result-summary {
            background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
            color: white;
            border-radius: 16px;
            padding: 24px;
            text-align: center;
            margin-bottom: 20px;
        }

        .result-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .result-desc {
            font-size: 14px;
            opacity: 0.9;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-top: 20px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #0052d9;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
        }

        .phone-label {
            position: absolute;
            bottom: -40px;
            left: 50%;
            transform: translateX(-50%);
            color: #ffffff;
            font-size: 14px;
            font-weight: 500;
            text-align: center;
            white-space: nowrap;
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in {
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateX(-20px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* 艺术化房间大厅样式 */
        .lobby-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: relative;
            overflow: hidden;
        }

        .lobby-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 40%),
                radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.08) 0%, transparent 40%);
            pointer-events: none;
        }

        .lobby-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 16px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .room-info-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 20px;
            margin: 20px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .room-number {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #333;
            padding: 8px 20px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 16px;
            display: inline-block;
            margin-bottom: 12px;
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
        }

        .players-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            padding: 20px;
            max-width: 350px;
            margin: 0 auto;
        }

        .player-slot {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 20px;
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
            min-height: 120px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .player-slot.occupied {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .player-slot.empty {
            border-style: dashed;
            opacity: 0.6;
        }

        .player-avatar-large {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .player-status {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #52c41a;
            border: 2px solid white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .lobby-actions {
            position: absolute;
            bottom: calc(var(--safe-area-bottom) + 10px);
            left: 20px;
            right: 20px;
            display: flex;
            gap: 12px;
        }

        .action-btn {
            flex: 1;
            padding: 16px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .action-btn.primary {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #333;
            box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
        }

        .action-btn.secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        /* 私聊和语音功能样式 */
        .chat-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px 12px 0 0;
            overflow: hidden;
        }

        .chat-tab {
            flex: 1;
            padding: 12px;
            background: transparent;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .chat-tab.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .voice-controls {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: rgba(0, 0, 0, 0.1);
            border-radius: 0 0 12px 12px;
        }

        .voice-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: none;
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .voice-btn:hover {
            transform: scale(1.1);
        }

        .voice-btn.recording {
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 107, 107, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 107, 107, 0); }
        }

        @keyframes countdown {
            from { width: 100%; }
            to { width: 0%; }
        }

        @keyframes glow {
            0%, 100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.5); }
            50% { box-shadow: 0 0 30px rgba(255, 215, 0, 0.8); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* 新增神秘主题动画 */
        @keyframes twinkle {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 1; }
        }

        @keyframes fog {
            0% { transform: translateX(-10px); opacity: 0.1; }
            100% { transform: translateX(10px); opacity: 0.3; }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .vote-result-card {
            animation: slideInUp 0.6s ease-out;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
            border-radius: 20px;
            padding: 24px;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .artistic-card {
            animation: fadeInScale 0.5s ease-out;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
            border-radius: 20px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .artistic-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
        }

        .artistic-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 16px 24px;
            border-radius: 16px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .artistic-btn:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .artistic-btn:hover:before {
            left: 100%;
        }

        .artistic-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 32px rgba(102, 126, 234, 0.4);
        }

        .neon-text {
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.8), 0 0 20px rgba(255, 255, 255, 0.6), 0 0 30px rgba(255, 255, 255, 0.4);
        }

        .glow-effect {
            animation: glow 2s ease-in-out infinite;
        }

        .float-effect {
            animation: float 3s ease-in-out infinite;
        }

        .shimmer-effect {
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            background-size: 200px 100%;
            animation: shimmer 2s infinite;
        }

        /* 顶级艺术化效果 */
        .artistic-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 24px;
            box-shadow:
                0 12px 40px rgba(0, 0, 0, 0.15),
                0 4px 16px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.3),
                inset 0 -1px 0 rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .artistic-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.2),
                0 8px 24px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
        }

        .artistic-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .artistic-card:hover::before {
            left: 100%;
        }

        .neon-text {
            text-shadow:
                0 0 5px currentColor,
                0 0 10px currentColor,
                0 0 15px currentColor,
                0 0 20px currentColor;
        }

        .gradient-border {
            position: relative;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
            padding: 2px;
            border-radius: 20px;
        }

        .gradient-border::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
            border-radius: 20px;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .gradient-border:hover::before {
            opacity: 0.8;
        }

        .holographic {
            background: linear-gradient(45deg,
                #ff0080, #ff8c00, #40e0d0, #ff0080,
                #ff8c00, #40e0d0, #ff0080);
            background-size: 400% 400%;
            animation: holographic 4s ease infinite;
        }

        @keyframes holographic {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        /* 影响力系统样式 */
        .influence-meter {
            background: linear-gradient(90deg, #ff6b6b 0%, #feca57 50%, #48dbfb 100%);
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            position: relative;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .influence-fill {
            background: linear-gradient(90deg, #fff, rgba(255, 255, 255, 0.8));
            height: 100%;
            border-radius: 4px;
            transition: width 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
        }

        .influence-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
            animation: shimmer 2s infinite;
        }

        /* 投票结果样式 */
        .vote-result-card {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.25) 0%,
                rgba(255, 255, 255, 0.1) 50%,
                rgba(255, 255, 255, 0.05) 100%);
            backdrop-filter: blur(30px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 28px;
            padding: 24px;
            margin: 16px 0;
            position: relative;
            overflow: hidden;
            box-shadow:
                0 16px 48px rgba(0, 0, 0, 0.1),
                0 8px 24px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
        }

        .vote-result-card::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff6b6b);
            border-radius: 30px;
            z-index: -1;
            opacity: 0.6;
            filter: blur(8px);
            animation: holographic 6s ease infinite;
        }

        /* 坚持机制样式 */
        .persistence-panel {
            background: linear-gradient(135deg,
                rgba(255, 193, 7, 0.2) 0%,
                rgba(255, 152, 0, 0.15) 100%);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 193, 7, 0.4);
            border-radius: 24px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
            overflow: hidden;
        }

        .persistence-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: shimmer 3s infinite;
        }

        .persistence-choice {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(255, 193, 7, 0.6);
            border-radius: 16px;
            padding: 16px;
            margin: 12px 0;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
        }

        .persistence-choice:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 12px 32px rgba(255, 193, 7, 0.3);
            border-color: rgba(255, 193, 7, 0.8);
        }

        .persistence-choice.selected {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 152, 0, 0.1));
            border-color: #ffc107;
            transform: scale(1.05);
            box-shadow: 0 8px 24px rgba(255, 193, 7, 0.4);
        }

        /* 艺术化按钮 */
        .artistic-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 20px;
            padding: 16px 32px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-shadow:
                0 8px 24px rgba(102, 126, 234, 0.3),
                0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .artistic-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .artistic-btn:hover {
            transform: translateY(-4px) scale(1.05);
            box-shadow:
                0 16px 40px rgba(102, 126, 234, 0.4),
                0 8px 20px rgba(0, 0, 0, 0.15);
        }

        .artistic-btn:hover::before {
            left: 100%;
        }

        .artistic-btn:active {
            transform: translateY(-2px) scale(1.02);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 首页 - 神秘沉浸式设计 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span style="color: #ffffff; font-weight: 700; text-shadow: 0 1px 2px rgba(0,0,0,0.8);">10:29</span>
                    <div style="display: flex; align-items: center; gap: 6px;">
                        <span style="background: #ffd700; color: #000; padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 700;">100%</span>
                        <span style="color: #ffd700; font-size: 14px;">🔋</span>
                    </div>
                </div>
                <!-- 地球星空背景层 -->
                <div class="page-content" style="background: linear-gradient(180deg, #000428 0%, #004e92 100%); position: relative; overflow: hidden;">
                    <!-- 星星装饰 -->
                    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; z-index: 1; pointer-events: none;">
                        <div style="position: absolute; top: 15%; left: 20%; color: rgba(255, 255, 255, 0.8); font-size: 12px; animation: twinkle 3s ease-in-out infinite;">✦</div>
                        <div style="position: absolute; top: 25%; right: 25%; color: rgba(255, 255, 255, 0.8); font-size: 10px; animation: twinkle 3s ease-in-out infinite 1s;">✧</div>
                        <div style="position: absolute; top: 40%; left: 15%; color: rgba(255, 255, 255, 0.8); font-size: 12px; animation: twinkle 3s ease-in-out infinite 2s;">✦</div>
                        <div style="position: absolute; top: 60%; right: 20%; color: rgba(255, 255, 255, 0.8); font-size: 10px; animation: twinkle 3s ease-in-out infinite 0.5s;">✧</div>
                        <div style="position: absolute; top: 80%; left: 30%; color: rgba(255, 255, 255, 0.8); font-size: 12px; animation: twinkle 3s ease-in-out infinite 1.5s;">✦</div>
                    </div>

                    <!-- 地球元素 -->
                    <div style="position: absolute; bottom: -400px; left: 50%; transform: translateX(-50%);
                        width: 1200px; height: 1200px;
                        background: radial-gradient(circle, rgba(135, 206, 235, 0.7) 0%, rgba(65, 105, 225, 0.8) 70%, rgba(25, 25, 112, 0.9) 100%);
                        border-radius: 50%; box-shadow: 0 0 200px rgba(135, 206, 235, 0.3);
                        z-index: 1; pointer-events: none;"></div>

                    <!-- 背景遮罩层 - 确保文字可读性 -->
                    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0;
                        background: rgba(0, 0, 0, 0.2); z-index: 1; pointer-events: none;"></div>

                    <!-- 顶部导航 - 简化设计 -->
                    <div style="padding: 20px 20px 0; position: relative; z-index: 10;">
                        <div style="text-align: center;">
                            <div style="color: var(--gold-accent); font-size: 14px; font-weight: 500; margin-bottom: 8px; opacity: 0.9;">WELCOME TO</div>
                            <h1 style="color: #ffffff; font-size: 32px; font-weight: 800; margin: 0; text-shadow: 0 2px 8px rgba(0,0,0,0.5); letter-spacing: 1px;">
                                🕵️ 密室推理社
                            </h1>
                            <p style="color: rgba(255,255,255,0.8); font-size: 16px; margin: 8px 0 0; font-weight: 300;">
                                与好友一起，在AI编织的谜团中寻找真相
                            </p>
                        </div>
                    </div>
                    <!-- 主要内容区域 -->
                    <div style="padding: 40px 20px 20px; position: relative; z-index: 10; flex: 1; display: flex; flex-direction: column; justify-content: center;">

                        <!-- 中央展示区域 -->
                        <div style="text-align: center; margin-bottom: 60px;">
                            <!-- 3D立体桌子效果 -->
                            <div style="position: relative; margin: 0 auto 40px; width: 200px; height: 120px;">
                                <!-- 桌面 -->
                                <div style="position: absolute; top: 40px; left: 20px; right: 20px; height: 80px;
                                    background: linear-gradient(135deg, #8B4513 0%, #A0522D 50%, #CD853F 100%);
                                    border-radius: 50%;
                                    box-shadow: 0 8px 32px rgba(0,0,0,0.4), inset 0 2px 8px rgba(255,255,255,0.2);
                                    transform: perspective(200px) rotateX(45deg);"></div>

                                <!-- 椅子们 -->
                                <div style="position: absolute; top: 10px; left: 80px; width: 40px; height: 30px;
                                    background: linear-gradient(135deg, #654321 0%, #8B4513 100%);
                                    border-radius: 8px 8px 4px 4px;
                                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                                    transform: perspective(100px) rotateX(30deg);"></div>
                                <div style="position: absolute; top: 30px; left: 30px; width: 30px; height: 25px;
                                    background: linear-gradient(135deg, #654321 0%, #8B4513 100%);
                                    border-radius: 6px; box-shadow: 0 3px 8px rgba(0,0,0,0.3);
                                    transform: perspective(100px) rotateX(20deg) rotateY(20deg);"></div>
                                <div style="position: absolute; top: 30px; right: 30px; width: 30px; height: 25px;
                                    background: linear-gradient(135deg, #654321 0%, #8B4513 100%);
                                    border-radius: 6px; box-shadow: 0 3px 8px rgba(0,0,0,0.3);
                                    transform: perspective(100px) rotateX(20deg) rotateY(-20deg);"></div>

                                <!-- 神秘光效 -->
                                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
                                    width: 60px; height: 60px; border-radius: 50%;
                                    background: radial-gradient(circle, var(--gold-accent) 0%, transparent 70%);
                                    opacity: 0.6; animation: pulse 2s ease-in-out infinite;"></div>
                            </div>

                            <!-- 特色标签 -->
                            <div style="display: flex; justify-content: center; gap: 12px; margin-bottom: 40px;">
                                <span style="background: rgba(255,215,0,0.2); color: var(--gold-accent);
                                    padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600;
                                    border: 1px solid rgba(255,215,0,0.3);">🤖 AI智能</span>
                                <span style="background: rgba(255,255,255,0.2); color: rgba(255,255,255,0.9);
                                    padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600;
                                    border: 1px solid rgba(255,255,255,0.3);">🎭 沉浸剧情</span>
                                <span style="background: rgba(255,215,0,0.2); color: var(--gold-accent);
                                    padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600;
                                    border: 1px solid rgba(255,215,0,0.3);">👥 社交推理</span>
                            </div>
                        </div>

                        <!-- 主要操作按钮 -->
                        <div style="display: flex; flex-direction: column; gap: 16px;">
                            <button style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
                                color: white; border: none; padding: 18px 24px; border-radius: 16px;
                                font-size: 18px; font-weight: 700; text-align: center;
                                box-shadow: var(--shadow-mystery), 0 0 30px rgba(255, 107, 107, 0.4);
                                transform: translateY(0); transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                                cursor: pointer; position: relative; overflow: hidden;">
                                <div style="position: absolute; top: 0; left: -100%; width: 100%; height: 100%;
                                    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                                    animation: shimmer 3s infinite;"></div>
                                <span style="position: relative; z-index: 1;">
                                    🏰 创建密室 - 成为主持人
                                </span>
                            </button>

                            <button style="background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
                                color: white; border: none; padding: 18px 24px; border-radius: 16px;
                                font-size: 18px; font-weight: 700; text-align: center;
                                box-shadow: var(--shadow-mystery), 0 0 30px rgba(78, 205, 196, 0.4);
                                transform: translateY(0); transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                                cursor: pointer; position: relative; overflow: hidden;">
                                <div style="position: absolute; top: 0; left: -100%; width: 100%; height: 100%;
                                    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                                    animation: shimmer 3s infinite 1.5s;"></div>
                                <span style="position: relative; z-index: 1;">
                                    🔍 加入推理 - 寻找真相
                                </span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="phone-label">首页 - 神秘沉浸式设计</div>
        </div>

        <!-- 创建房间页面 - 神秘主题设计 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span style="color: #ffffff; font-weight: 700; text-shadow: 0 1px 2px rgba(0,0,0,0.8);">10:29</span>
                    <div style="display: flex; align-items: center; gap: 6px;">
                        <span style="background: #ffd700; color: #000; padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 700;">100%</span>
                        <span style="color: #ffd700; font-size: 14px;">🔋</span>
                    </div>
                </div>
                <!-- 深色神秘背景 -->
                <div class="page-content" style="background: var(--mystery-gradient); position: relative; overflow: hidden;">
                    <!-- 微妙的星空效果 -->
                    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background:
                        radial-gradient(1px 1px at 30px 50px, rgba(255,255,255,0.6), transparent),
                        radial-gradient(1px 1px at 80px 20px, rgba(255,255,255,0.4), transparent),
                        radial-gradient(1px 1px at 150px 80px, rgba(255,255,255,0.7), transparent);
                        animation: twinkle 3s ease-in-out infinite alternate; pointer-events: none;"></div>

                    <!-- 顶部导航栏 -->
                    <div style="background: rgba(0,0,0,0.3); backdrop-filter: blur(10px); padding: 16px 20px; display: flex; align-items: center; justify-content: space-between; position: relative; z-index: 10;">
                        <svg viewBox="0 0 24 24" fill="white" style="width: 24px; height: 24px; cursor: pointer;">
                            <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                        </svg>
                        <h1 style="color: white; font-size: 20px; font-weight: 700; margin: 0; text-shadow: 0 2px 4px rgba(0,0,0,0.5);">
                            🏰 创建密室
                        </h1>
                        <div style="width: 24px;"></div>
                    </div>
                    <!-- 主要内容区域 -->
                    <div style="padding: 20px; position: relative; z-index: 10;">
                        <!-- 配置卡片 -->
                        <div style="background: rgba(255,255,255,0.95); backdrop-filter: blur(10px);
                            border-radius: 20px; padding: 24px; margin-bottom: 20px;
                            box-shadow: 0 8px 32px rgba(0,0,0,0.3), 0 0 0 1px rgba(255,255,255,0.2);
                            border: 1px solid rgba(255,215,0,0.2);">

                            <!-- 标题区域 -->
                            <div style="text-align: center; margin-bottom: 24px;">
                                <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
                                    border-radius: 50%; margin: 0 auto 16px; display: flex; align-items: center; justify-content: center;
                                    box-shadow: var(--shadow-mystery), var(--shadow-glow); animation: pulse 2s ease-in-out infinite;">
                                    <span style="color: white; font-size: 36px;">🏰</span>
                                </div>
                                <h2 style="color: #333; margin-bottom: 8px; font-size: 24px; font-weight: 800;">密室配置</h2>
                                <p style="color: #666; font-size: 14px; line-height: 1.5;">
                                    精心设计每一个细节，为玩家打造难忘的推理体验
                                </p>
                            </div>

                            <!-- 房间名称 -->
                            <div style="margin-bottom: 20px;">
                                <label style="color: #333; font-size: 16px; margin-bottom: 8px; display: block; font-weight: 600;">
                                    🏷️ 密室名称
                                </label>
                                <input type="text" placeholder="为你的密室起个神秘的名字..." value="雾夜庄园疑案"
                                    style="width: 100%; padding: 16px; border: 2px solid rgba(255,215,0,0.3);
                                    border-radius: 12px; background: rgba(255,255,255,0.9);
                                    font-size: 16px; color: #333; box-sizing: border-box;
                                    transition: all 0.3s ease; outline: none;">
                            </div>

                            <!-- 房间密码 -->
                            <div style="margin-bottom: 20px;">
                                <label style="color: #333; font-size: 16px; margin-bottom: 8px; display: block; font-weight: 600;">
                                    🔒 密室密码 <span style="color: #999; font-weight: 400; font-size: 14px;">(可选)</span>
                                </label>
                                <input type="password" placeholder="设置密码保护你的密室..."
                                    style="width: 100%; padding: 16px; border: 2px solid rgba(255,215,0,0.3);
                                    border-radius: 12px; background: rgba(255,255,255,0.9);
                                    font-size: 16px; color: #333; box-sizing: border-box;
                                    transition: all 0.3s ease; outline: none;">
                            </div>

                            <!-- 玩家人数 -->
                            <div style="margin-bottom: 20px;">
                                <label style="color: #333; font-size: 16px; margin-bottom: 12px; display: block; font-weight: 600;">
                                    👥 玩家人数
                                </label>
                                <div style="display: flex; gap: 12px;">
                                    <button style="flex: 1; padding: 16px; border: 2px solid rgba(255,215,0,0.3);
                                        border-radius: 12px; background: rgba(255,255,255,0.8); color: #666;
                                        font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.3s ease;
                                        text-align: center;">
                                        <div style="font-size: 18px; margin-bottom: 4px;">4人</div>
                                        <div style="font-size: 12px; opacity: 0.8;">紧凑推理</div>
                                    </button>
                                    <button style="flex: 1; padding: 16px; border: 2px solid var(--gold-accent);
                                        border-radius: 12px; background: rgba(255,215,0,0.2); color: #333;
                                        font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.3s ease;
                                        text-align: center; box-shadow: 0 4px 12px rgba(255,215,0,0.3);">
                                        <div style="font-size: 18px; margin-bottom: 4px;">6人</div>
                                        <div style="font-size: 12px; opacity: 0.8;">经典配置</div>
                                    </button>
                                    <button style="flex: 1; padding: 16px; border: 2px solid rgba(255,215,0,0.3);
                                        border-radius: 12px; background: rgba(255,255,255,0.8); color: #666;
                                        font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.3s ease;
                                        text-align: center;">
                                        <div style="font-size: 18px; margin-bottom: 4px;">8人</div>
                                        <div style="font-size: 12px; opacity: 0.8;">热闹对抗</div>
                                    </button>
                                </div>
                            </div>

                            <!-- 游戏模式 -->
                            <div style="margin-bottom: 20px;">
                                <label style="color: #333; font-size: 16px; margin-bottom: 12px; display: block; font-weight: 600;">
                                    🎭 推理模式
                                </label>
                                <select style="width: 100%; padding: 16px; border: 2px solid rgba(255,215,0,0.3);
                                    border-radius: 12px; background: rgba(255,255,255,0.9);
                                    font-size: 16px; color: #333; cursor: pointer; outline: none;
                                    appearance: none; background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"%23ffd700\"><path d=\"M7 10l5 5 5-5z\"/></svg>');
                                    background-repeat: no-repeat; background-position: right 16px center; background-size: 20px;">
                                    <option value="story" selected>📖 沉浸剧情 - 深度角色扮演体验</option>
                                    <option value="classic">🕵️ 经典推理 - 传统逻辑分析</option>
                                    <option value="competitive">⚔️ 竞技对抗 - 快节奏推理竞赛</option>
                                    <option value="cooperative">🤝 合作解谜 - 团队协作破案</option>
                                    <option value="social">💬 社交互动 - 轻松娱乐模式</option>
                                </select>
                            </div>

                            <label style="color: #666; font-size: 14px; margin-bottom: 8px; display: block; font-weight: 500;">⭐ 难度等级</label>
                            <div style="display: flex; gap: 8px; margin-bottom: 16px;">
                                <button class="persistence-choice" style="flex: 1; padding: 12px; margin: 0; background: rgba(76, 175, 80, 0.1); border-color: #4caf50;">
                                    <div style="text-align: center;">
                                        <div style="color: #4caf50; font-size: 14px; font-weight: 600;">简单</div>
                                        <div style="color: #666; font-size: 10px;">新手友好</div>
                                    </div>
                                </button>
                                <button class="persistence-choice selected" style="flex: 1; padding: 12px; margin: 0;">
                                    <div style="text-align: center;">
                                        <div style="color: #333; font-size: 14px; font-weight: 600;">中等</div>
                                        <div style="color: #666; font-size: 10px;">适度挑战</div>
                                    </div>
                                </button>
                                <button class="persistence-choice" style="flex: 1; padding: 12px; margin: 0; background: rgba(255, 193, 7, 0.1); border-color: #ffc107;">
                                    <div style="text-align: center;">
                                        <div style="color: #ffc107; font-size: 14px; font-weight: 600;">困难</div>
                                        <div style="color: #666; font-size: 10px;">高难度</div>
                                    </div>
                                </button>
                                <button class="persistence-choice" style="flex: 1; padding: 12px; margin: 0; background: rgba(255, 107, 107, 0.1); border-color: #ff6b6b;">
                                    <div style="text-align: center;">
                                        <div style="color: #ff6b6b; font-size: 14px; font-weight: 600;">专家</div>
                                        <div style="color: #666; font-size: 10px;">极限挑战</div>
                                    </div>
                                </button>
                            </div>

                            <label style="color: #666; font-size: 14px; margin-bottom: 12px; display: block; font-weight: 500;">🎯 特殊机制</label>
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px; margin-bottom: 20px;">
                                <label style="display: flex; align-items: center; background: rgba(102, 126, 234, 0.1); padding: 12px; border-radius: 12px; cursor: pointer; font-size: 12px; color: #667eea; border: 2px solid rgba(102, 126, 234, 0.2);">
                                    <input type="checkbox" style="margin-right: 8px;" checked>
                                    <div>
                                        <div style="font-weight: 600;">🎤 语音聊天</div>
                                        <div style="font-size: 10px; opacity: 0.8;">实时语音交流</div>
                                    </div>
                                </label>
                                <label style="display: flex; align-items: center; background: rgba(235, 47, 150, 0.1); padding: 12px; border-radius: 12px; cursor: pointer; font-size: 12px; color: #eb2f96; border: 2px solid rgba(235, 47, 150, 0.2);">
                                    <input type="checkbox" style="margin-right: 8px;" checked>
                                    <div>
                                        <div style="font-weight: 600;">💕 真心话环节</div>
                                        <div style="font-size: 10px; opacity: 0.8;">深度社交互动</div>
                                    </div>
                                </label>
                                <label style="display: flex; align-items: center; background: rgba(255, 193, 7, 0.1); padding: 12px; border-radius: 12px; cursor: pointer; font-size: 12px; color: #ffc107; border: 2px solid rgba(255, 193, 7, 0.2);">
                                    <input type="checkbox" style="margin-right: 8px;">
                                    <div>
                                        <div style="font-weight: 600;">🎨 小游戏互动</div>
                                        <div style="font-size: 10px; opacity: 0.8;">你画我猜等</div>
                                    </div>
                                </label>
                                <label style="display: flex; align-items: center; background: rgba(156, 39, 176, 0.1); padding: 12px; border-radius: 12px; cursor: pointer; font-size: 12px; color: #9c27b0; border: 2px solid rgba(156, 39, 176, 0.2);">
                                    <input type="checkbox" style="margin-right: 8px;" checked>
                                    <div>
                                        <div style="font-weight: 600;">🤖 AI智能推演</div>
                                        <div style="font-size: 10px; opacity: 0.8;">动态剧情生成</div>
                                    </div>
                                </label>
                            </div>

                            <!-- 创建按钮 -->
                            <button style="width: 100%; padding: 20px; margin-top: 24px;
                                background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
                                color: white; border: none; border-radius: 16px;
                                font-size: 18px; font-weight: 700; cursor: pointer;
                                box-shadow: var(--shadow-mystery), 0 0 30px rgba(255, 107, 107, 0.4);
                                transform: translateY(0); transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                                position: relative; overflow: hidden;">
                                <div style="position: absolute; top: 0; left: -100%; width: 100%; height: 100%;
                                    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                                    animation: shimmer 3s infinite;"></div>
                                <span style="position: relative; z-index: 1;">
                                    🏰 创建密室 - 开启推理之旅
                                </span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="phone-label">创建房间 - 神秘主题设计</div>

                        <div class="artistic-card fade-in">
                            <h4 style="color: #333; margin-bottom: 16px; display: flex; align-items: center;">
                                <span style="margin-right: 8px; font-size: 20px;">📋</span>
                                游戏规则
                            </h4>
                            <div style="background: rgba(102, 126, 234, 0.05); border-radius: 12px; padding: 16px;">
                                <ul style="color: #666; font-size: 13px; line-height: 1.6; margin: 0; padding-left: 16px;">
                                    <li>🤖 AI将为每位玩家分配独特角色和背景故事</li>
                                    <li>🔍 通过讨论和推理找出真相，揭开谜团</li>
                                    <li>🎮 穿插小游戏和真心话环节增加趣味性</li>
                                    <li>🗳️ 投票决定剧情走向，影响游戏结局</li>
                                    <li>⚡ 坚持机制让你能改变集体决定</li>
                                    <li>🏆 根据表现获得影响力和成就奖励</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="phone-label">创建房间 - 增强版</div>
        </div>

        <!-- 加入房间页面 - 神秘主题设计 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span style="color: #ffffff; font-weight: 700; text-shadow: 0 1px 2px rgba(0,0,0,0.8);">9:41</span>
                    <div style="display: flex; align-items: center; gap: 6px;">
                        <span style="background: #ffd700; color: #000; padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 700;">100%</span>
                        <span style="color: #ffd700; font-size: 14px;">🔋</span>
                    </div>
                </div>
                <!-- 神秘背景层 -->
                <div class="page-content" style="background: var(--mystery-gradient); position: relative; overflow: hidden;">
                    <!-- 星空效果 -->
                    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background:
                        radial-gradient(1px 1px at 50px 40px, rgba(255,255,255,0.7), transparent),
                        radial-gradient(2px 2px at 120px 80px, rgba(255,255,255,0.5), transparent),
                        radial-gradient(1px 1px at 200px 30px, rgba(255,255,255,0.8), transparent);
                        animation: twinkle 3s ease-in-out infinite alternate; pointer-events: none;"></div>

                    <!-- 顶部导航栏 -->
                    <div style="background: rgba(0,0,0,0.3); backdrop-filter: blur(10px); padding: 16px 20px; display: flex; align-items: center; justify-content: space-between; position: relative; z-index: 10;">
                        <svg viewBox="0 0 24 24" fill="white" style="width: 24px; height: 24px; cursor: pointer;">
                            <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                        </svg>
                        <h1 style="color: white; font-size: 20px; font-weight: 700; margin: 0; text-shadow: 0 2px 4px rgba(0,0,0,0.5);">
                            🔍 加入推理
                        </h1>
                        <div style="width: 24px;"></div>
                    </div>
                    <!-- 主要内容区域 -->
                    <div style="padding: 20px; position: relative; z-index: 10;">
                        <!-- 加入房间卡片 -->
                        <div style="background: rgba(255,255,255,0.95); backdrop-filter: blur(10px);
                            border-radius: 20px; padding: 24px; margin-bottom: 20px;
                            box-shadow: 0 8px 32px rgba(0,0,0,0.3), 0 0 0 1px rgba(255,255,255,0.2);
                            border: 1px solid rgba(255,215,0,0.2);">

                            <!-- 标题区域 -->
                            <div style="text-align: center; margin-bottom: 24px;">
                                <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
                                    border-radius: 50%; margin: 0 auto 16px; display: flex; align-items: center; justify-content: center;
                                    box-shadow: var(--shadow-mystery), 0 0 30px rgba(78, 205, 196, 0.4); animation: pulse 2s ease-in-out infinite;">
                                    <span style="color: white; font-size: 36px;">🔍</span>
                                </div>
                                <h2 style="color: #333; margin-bottom: 8px; font-size: 24px; font-weight: 800;">加入密室</h2>
                                <p style="color: #666; font-size: 14px; line-height: 1.5;">
                                    输入房间号码，开始你的推理之旅
                                </p>
                            </div>

                            <!-- 房间号输入 -->
                            <div style="margin-bottom: 20px;">
                                <label style="color: #333; font-size: 16px; margin-bottom: 12px; display: block; font-weight: 600;">
                                    🏷️ 密室号码
                                </label>
                                <input type="text" placeholder="输入6位房间号" maxlength="6"
                                    style="width: 100%; padding: 20px; border: 2px solid rgba(255,215,0,0.3);
                                    border-radius: 12px; background: rgba(255,255,255,0.9);
                                    font-size: 28px; font-weight: 800; color: #333; box-sizing: border-box;
                                    text-align: center; letter-spacing: 8px; outline: none;
                                    transition: all 0.3s ease;">
                            </div>

                            <!-- 密码输入 -->
                            <div style="margin-bottom: 24px;">
                                <label style="color: #333; font-size: 16px; margin-bottom: 8px; display: block; font-weight: 600;">
                                    🔒 密室密码 <span style="color: #999; font-weight: 400; font-size: 14px;">(如需要)</span>
                                </label>
                                <input type="password" placeholder="输入密室密码..."
                                    style="width: 100%; padding: 16px; border: 2px solid rgba(255,215,0,0.3);
                                    border-radius: 12px; background: rgba(255,255,255,0.9);
                                    font-size: 16px; color: #333; box-sizing: border-box;
                                    transition: all 0.3s ease; outline: none;">
                            </div>

                            <!-- 加入按钮 -->
                            <button style="width: 100%; padding: 20px; margin-bottom: 16px;
                                background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
                                color: white; border: none; border-radius: 16px;
                                font-size: 18px; font-weight: 700; cursor: pointer;
                                box-shadow: var(--shadow-mystery), 0 0 30px rgba(78, 205, 196, 0.4);
                                transform: translateY(0); transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                                position: relative; overflow: hidden;">
                                <div style="position: absolute; top: 0; left: -100%; width: 100%; height: 100%;
                                    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                                    animation: shimmer 3s infinite;"></div>
                                <span style="position: relative; z-index: 1;">
                                    🚀 加入推理 - 开始探索
                                </span>
                            </button>

                            <!-- 二维码选项 -->
                            <div style="text-align: center;">
                                <button style="background: transparent; border: none; color: rgba(78, 205, 196, 0.8);
                                    font-size: 14px; cursor: pointer; padding: 8px; border-radius: 8px;
                                    transition: all 0.3s ease;">
                                    📱 扫描二维码快速加入
                                </button>
                            </div>
                        </div>

                        <div class="artistic-card fade-in" style="margin-bottom: 20px;">
                            <h4 style="color: #333; margin-bottom: 16px; display: flex; align-items: center;">
                                <span style="margin-right: 8px; font-size: 20px;">🔥</span>
                                推荐房间
                            </h4>

                            <div style="display: flex; flex-direction: column; gap: 12px;">
                                <div style="background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(255, 107, 107, 0.05) 100%); border-radius: 12px; padding: 16px; border: 2px solid rgba(255, 107, 107, 0.2); cursor: pointer; transition: all 0.3s ease;" onclick="this.style.transform='scale(1.02)'; setTimeout(() => this.style.transform='', 200);">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <h5 style="color: #333; margin: 0; font-size: 16px; font-weight: 600;">🏰 神秘庄园</h5>
                                        <div style="background: #52c41a; color: white; padding: 2px 8px; border-radius: 10px; font-size: 10px; font-weight: 500;">进行中</div>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <span style="color: #666; font-size: 12px;">房间号: 123456</span>
                                        <span style="color: #666; font-size: 12px;">5/6人</span>
                                    </div>
                                    <div style="display: flex; gap: 6px;">
                                        <span style="background: rgba(255, 107, 107, 0.2); color: #d4380d; padding: 2px 8px; border-radius: 10px; font-size: 10px;">高难度</span>
                                        <span style="background: rgba(82, 196, 26, 0.2); color: #389e0d; padding: 2px 8px; border-radius: 10px; font-size: 10px;">语音开启</span>
                                    </div>
                                </div>

                                <div style="background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(76, 175, 80, 0.05) 100%); border-radius: 12px; padding: 16px; border: 2px solid rgba(76, 175, 80, 0.2); cursor: pointer; transition: all 0.3s ease;" onclick="this.style.transform='scale(1.02)'; setTimeout(() => this.style.transform='', 200);">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <h5 style="color: #333; margin: 0; font-size: 16px; font-weight: 600;">🏛️ 古堡疑云</h5>
                                        <div style="background: #ffc107; color: white; padding: 2px 8px; border-radius: 10px; font-size: 10px; font-weight: 500;">等待中</div>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <span style="color: #666; font-size: 12px;">房间号: 789012</span>
                                        <span style="color: #666; font-size: 12px;">4/8人</span>
                                    </div>
                                    <div style="display: flex; gap: 6px;">
                                        <span style="background: rgba(76, 175, 80, 0.2); color: #389e0d; padding: 2px 8px; border-radius: 10px; font-size: 10px;">中等难度</span>
                                        <span style="background: rgba(33, 150, 243, 0.2); color: #1976d2; padding: 2px 8px; border-radius: 10px; font-size: 10px;">新手友好</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="artistic-card fade-in">
                            <h4 style="color: #333; margin-bottom: 16px; display: flex; align-items: center;">
                                <span style="margin-right: 8px; font-size: 20px;">💡</span>
                                加入提示
                            </h4>
                            <div style="background: rgba(78, 205, 196, 0.05); border-radius: 12px; padding: 16px;">
                                <ul style="color: #666; font-size: 13px; line-height: 1.6; margin: 0; padding-left: 16px;">
                                    <li>🔢 房间号由房主创建房间时自动生成</li>
                                    <li>🔒 部分房间可能需要密码才能加入</li>
                                    <li>👥 确保房间还有空余位置</li>
                                    <li>🎮 加入后等待房主开始游戏</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="phone-label">加入房间</div>
        </div>

        <!-- 房间大厅页面 - 神秘主题重新设计 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span style="color: #ffffff; font-weight: 700; text-shadow: 0 1px 2px rgba(0,0,0,0.8);">10:28</span>
                    <div style="display: flex; align-items: center; gap: 6px;">
                        <span style="background: #ffd700; color: #000; padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 700;">100%</span>
                        <span style="color: #ffd700; font-size: 14px;">🔋</span>
                    </div>
                </div>
                <!-- 神秘大厅背景 -->
                <div style="background: var(--mystery-gradient); min-height: 100vh; position: relative; overflow: hidden;">
                    <!-- 星空效果 -->
                    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background:
                        radial-gradient(2px 2px at 30px 50px, rgba(255,255,255,0.8), transparent),
                        radial-gradient(1px 1px at 80px 20px, rgba(255,255,255,0.6), transparent),
                        radial-gradient(2px 2px at 150px 80px, rgba(255,255,255,0.9), transparent),
                        radial-gradient(1px 1px at 200px 120px, rgba(255,255,255,0.7), transparent);
                        animation: twinkle 4s ease-in-out infinite alternate; pointer-events: none;"></div>

                    <!-- 顶部导航栏 -->
                    <div style="background: rgba(0,0,0,0.4); backdrop-filter: blur(15px); padding: 16px 20px;
                        display: flex; align-items: center; justify-content: space-between; position: relative; z-index: 10;
                        border-bottom: 1px solid rgba(255,215,0,0.2);">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="white" style="cursor: pointer;">
                            <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                        </svg>
                        <div style="color: white; font-size: 18px; font-weight: 700; text-shadow: 0 2px 4px rgba(0,0,0,0.5);">
                            🏰 雾夜庄园 #934886
                        </div>
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="white" style="cursor: pointer;">
                            <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                        </svg>
                    </div>

                    <!-- 房间信息卡片 -->
                    <div style="padding: 20px; position: relative; z-index: 10;">
                        <div style="background: rgba(255,255,255,0.15); backdrop-filter: blur(15px);
                            border-radius: 20px; padding: 20px; margin-bottom: 20px;
                            border: 1px solid rgba(255,215,0,0.3); box-shadow: 0 8px 32px rgba(0,0,0,0.3);">

                            <div style="text-align: center; margin-bottom: 16px;">
                                <h2 style="color: white; font-size: 20px; font-weight: 800; margin: 0 0 8px 0; text-shadow: 0 2px 4px rgba(0,0,0,0.5);">
                                    📖 沉浸剧情模式
                                </h2>
                                <p style="color: rgba(255,255,255,0.8); font-size: 14px; margin: 0;">
                                    深度角色扮演 · 6人局 · 中等难度
                                </p>
                            </div>

                            <div style="display: flex; align-items: center; justify-content: center; gap: 12px;">
                                <div style="background: rgba(255,215,0,0.2); border: 1px solid rgba(255,215,0,0.4);
                                    border-radius: 12px; padding: 8px 16px; backdrop-filter: blur(10px);">
                                    <span style="color: var(--gold-accent); font-size: 14px; font-weight: 600;">⏱ 等待开始</span>
                                </div>
                                <div style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3);
                                    border-radius: 12px; padding: 8px 16px; backdrop-filter: blur(10px);">
                                    <span style="color: white; font-size: 14px; font-weight: 600;">🎭 第1局</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 玩家网格 -->
                    <div style="padding: 0 20px; position: relative; z-index: 10;">
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px; margin-bottom: 24px;">

                            <!-- 房主玩家 -->
                            <div style="background: rgba(255,215,0,0.15); backdrop-filter: blur(10px);
                                border-radius: 16px; padding: 16px; text-align: center;
                                border: 2px solid rgba(255,215,0,0.4); box-shadow: 0 4px 16px rgba(255,215,0,0.2);">
                                <div style="position: relative; margin-bottom: 12px;">
                                    <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
                                        border-radius: 50%; margin: 0 auto; display: flex; align-items: center; justify-content: center;
                                        box-shadow: 0 4px 12px rgba(255,107,107,0.4); font-size: 24px; font-weight: 800; color: white;">
                                        🎭
                                    </div>
                                    <div style="position: absolute; top: -4px; right: -4px; width: 16px; height: 16px;
                                        background: #52c41a; border-radius: 50%; border: 2px solid white;"></div>
                                </div>
                                <div style="color: white; font-size: 14px; font-weight: 600; margin-bottom: 4px;">推理大师</div>
                                <div style="background: var(--gold-accent); color: #333; padding: 4px 8px;
                                    border-radius: 12px; font-size: 10px; font-weight: 700; display: inline-block;">👑 房主</div>
                            </div>

                            <!-- 普通玩家 -->
                            <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);
                                border-radius: 16px; padding: 16px; text-align: center;
                                border: 1px solid rgba(255,255,255,0.2);">
                                <div style="position: relative; margin-bottom: 12px;">
                                    <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
                                        border-radius: 50%; margin: 0 auto; display: flex; align-items: center; justify-content: center;
                                        box-shadow: 0 4px 12px rgba(78,205,196,0.4); font-size: 24px; font-weight: 800; color: white;">
                                        🕵️
                                    </div>
                                    <div style="position: absolute; top: -4px; right: -4px; width: 16px; height: 16px;
                                        background: #52c41a; border-radius: 50%; border: 2px solid white;"></div>
                                </div>
                                <div style="color: white; font-size: 14px; font-weight: 600; margin-bottom: 4px;">神秘侦探</div>
                                <div style="background: rgba(76,175,80,0.8); color: white; padding: 4px 8px;
                                    border-radius: 12px; font-size: 10px; font-weight: 600; display: inline-block;">✓ 已准备</div>
                            </div>

                            <!-- 更多玩家... -->
                            <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);
                                border-radius: 16px; padding: 16px; text-align: center;
                                border: 1px solid rgba(255,255,255,0.2);">
                                <div style="position: relative; margin-bottom: 12px;">
                                    <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #9254de 0%, #722ed1 100%);
                                        border-radius: 50%; margin: 0 auto; display: flex; align-items: center; justify-content: center;
                                        box-shadow: 0 4px 12px rgba(146,84,222,0.4); font-size: 24px; font-weight: 800; color: white;">
                                        🎪
                                    </div>
                                    <div style="position: absolute; top: -4px; right: -4px; width: 16px; height: 16px;
                                        background: #faad14; border-radius: 50%; border: 2px solid white;"></div>
                                </div>
                                <div style="color: white; font-size: 14px; font-weight: 600; margin-bottom: 4px;">魔术师</div>
                                <div style="background: rgba(250,173,20,0.8); color: white; padding: 4px 8px;
                                    border-radius: 12px; font-size: 10px; font-weight: 600; display: inline-block;">⏳ 准备中</div>
                            </div>

                            <!-- 空位 -->
                            <div style="background: rgba(255,255,255,0.05); backdrop-filter: blur(10px);
                                border-radius: 16px; padding: 16px; text-align: center;
                                border: 2px dashed rgba(255,255,255,0.3);">
                                <div style="margin-bottom: 12px;">
                                    <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.1);
                                        border-radius: 50%; margin: 0 auto; display: flex; align-items: center; justify-content: center;
                                        font-size: 32px; color: rgba(255,255,255,0.5);">
                                        +
                                    </div>
                                </div>
                                <div style="color: rgba(255,255,255,0.6); font-size: 14px; font-weight: 500;">等待加入</div>
                            </div>

                        </div>
                    </div>

                    <!-- 底部操作按钮 -->
                    <div style="padding: 0 20px 20px; position: relative; z-index: 10;">
                        <div style="display: flex; gap: 12px;">
                            <button style="flex: 1; padding: 16px; background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
                                color: white; border: none; border-radius: 16px; font-size: 16px; font-weight: 700;
                                box-shadow: 0 4px 16px rgba(82,196,26,0.4); cursor: pointer; transition: all 0.3s ease;">
                                ✓ 准备就绪
                            </button>
                            <button style="flex: 1; padding: 16px; background: rgba(255,255,255,0.2); backdrop-filter: blur(10px);
                                color: white; border: 1px solid rgba(255,255,255,0.3); border-radius: 16px;
                                font-size: 16px; font-weight: 700; cursor: pointer; transition: all 0.3s ease;">
                                👥 邀请好友
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="phone-label">房间大厅 - 神秘主题设计</div>
        </div>

        <!-- 游戏等待页面 - 神秘主题设计 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span style="color: #ffffff; font-weight: 700; text-shadow: 0 1px 2px rgba(0,0,0,0.8);">9:41</span>
                    <div style="display: flex; align-items: center; gap: 6px;">
                        <span style="background: #ffd700; color: #000; padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 700;">100%</span>
                        <span style="color: #ffd700; font-size: 14px;">🔋</span>
                    </div>
                </div>
                <!-- 神秘等待背景 -->
                <div class="page-content" style="background: var(--mystery-gradient); position: relative; overflow: hidden; min-height: 100vh;">
                    <!-- 星空效果 -->
                    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background:
                        radial-gradient(2px 2px at 40px 60px, rgba(255,255,255,0.8), transparent),
                        radial-gradient(1px 1px at 100px 30px, rgba(255,255,255,0.6), transparent),
                        radial-gradient(2px 2px at 180px 100px, rgba(255,255,255,0.9), transparent),
                        radial-gradient(1px 1px at 250px 40px, rgba(255,255,255,0.7), transparent);
                        animation: twinkle 4s ease-in-out infinite alternate; pointer-events: none;"></div>

                    <!-- 顶部标题 -->
                    <div style="text-align: center; padding: 40px 20px 20px; position: relative; z-index: 10;">
                        <h1 style="color: white; font-size: 24px; font-weight: 800; margin: 0; text-shadow: 0 2px 8px rgba(0,0,0,0.5);">
                            ⏳ 密室准备中
                        </h1>
                        <p style="color: rgba(255,255,255,0.8); font-size: 16px; margin: 8px 0 0; font-weight: 300;">
                            房主正在进行最后的设置...
                        </p>
                    </div>
                    <!-- 主要内容区域 -->
                    <div style="padding: 20px; position: relative; z-index: 10; display: flex; flex-direction: column; justify-content: center; align-items: center; min-height: 500px;">

                        <!-- 等待状态卡片 -->
                        <div style="background: rgba(255,255,255,0.95); backdrop-filter: blur(15px);
                            border-radius: 24px; padding: 32px; margin-bottom: 24px; text-align: center;
                            box-shadow: 0 12px 48px rgba(0,0,0,0.3), 0 0 0 1px rgba(255,255,255,0.2);
                            border: 1px solid rgba(255,215,0,0.2); max-width: 320px; width: 100%;">

                            <!-- 中央动画图标 -->
                            <div style="margin-bottom: 24px;">
                                <div style="width: 120px; height: 120px; background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
                                    border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;
                                    box-shadow: var(--shadow-mystery), 0 0 40px rgba(114, 46, 209, 0.4);
                                    animation: pulse 2s ease-in-out infinite; position: relative;">
                                    <span style="color: white; font-size: 56px;">🎭</span>
                                    <!-- 旋转光环 -->
                                    <div style="position: absolute; top: -8px; left: -8px; right: -8px; bottom: -8px;
                                        border: 3px solid transparent; border-top: 3px solid var(--gold-accent);
                                        border-radius: 50%; animation: spin 3s linear infinite;"></div>
                                </div>
                                <h2 style="color: #333; margin-bottom: 12px; font-size: 24px; font-weight: 800;">AI正在编织谜团</h2>
                                <p style="color: #666; font-size: 16px; line-height: 1.6; margin-bottom: 0;">
                                    智能系统正在为你们量身定制<br>
                                    <span style="color: #722ed1; font-weight: 600;">专属推理剧本</span>
                                </p>
                            </div>

                            <!-- 进度条 -->
                            <div style="margin-bottom: 24px;">
                                <div style="background: rgba(114, 46, 209, 0.1); height: 12px; border-radius: 6px; overflow: hidden; margin-bottom: 12px; position: relative;">
                                    <div style="background: linear-gradient(90deg, #722ed1, #9254de, var(--gold-accent));
                                        height: 100%; width: 75%; border-radius: 6px; position: relative; overflow: hidden;">
                                        <div style="position: absolute; top: 0; left: -100%; width: 100%; height: 100%;
                                            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
                                            animation: shimmer 2s infinite;"></div>
                                    </div>
                                </div>
                                <div style="color: #722ed1; font-size: 16px; font-weight: 600;">准备进度: 75%</div>
                            </div>

                            <!-- 状态列表 -->
                            <div style="text-align: left; background: rgba(114, 46, 209, 0.05); border-radius: 16px; padding: 20px;">
                                <div style="color: #52c41a; font-size: 14px; margin-bottom: 8px; display: flex; align-items: center;">
                                    <span style="margin-right: 8px;">✅</span> 密室环境构建完成
                                </div>
                                <div style="color: #52c41a; font-size: 14px; margin-bottom: 8px; display: flex; align-items: center;">
                                    <span style="margin-right: 8px;">✅</span> 玩家身份验证完成
                                </div>
                                <div style="color: #1890ff; font-size: 14px; margin-bottom: 8px; display: flex; align-items: center;">
                                    <span style="margin-right: 8px;">🔄</span> AI剧本智能生成中...
                                </div>
                                <div style="color: #faad14; font-size: 14px; display: flex; align-items: center;">
                                    <span style="margin-right: 8px;">⏳</span> 角色分配即将开始
                                </div>
                            </div>
                        </div>

                        <div class="artistic-card fade-in" style="margin-top: 20px; max-width: 300px;">
                            <h4 style="color: #333; margin-bottom: 16px; text-align: center;">👥 当前玩家</h4>
                            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px;">
                                <div style="text-align: center;">
                                    <div style="width: 40px; height: 40px; background: linear-gradient(45deg, #667eea, #764ba2); border-radius: 50%; margin: 0 auto 8px; display: flex; align-items: center; justify-content: center;">
                                        <span style="color: white; font-size: 16px;">游</span>
                                    </div>
                                    <div style="color: #333; font-size: 11px;">游客MdEg</div>
                                    <div style="background: #ffd700; color: #333; padding: 1px 6px; border-radius: 8px; font-size: 9px; margin-top: 2px;">房主</div>
                                </div>
                                <div style="text-align: center;">
                                    <div style="width: 40px; height: 40px; background: linear-gradient(45deg, #4ecdc4, #44a08d); border-radius: 50%; margin: 0 auto 8px; display: flex; align-items: center; justify-content: center;">
                                        <span style="color: white; font-size: 16px;">张</span>
                                    </div>
                                    <div style="color: #333; font-size: 11px;">张三</div>
                                    <div style="background: #52c41a; color: white; padding: 1px 6px; border-radius: 8px; font-size: 9px; margin-top: 2px;">就绪</div>
                                </div>
                                <div style="text-align: center;">
                                    <div style="width: 40px; height: 40px; background: linear-gradient(45deg, #ff6b6b, #ee5a52); border-radius: 50%; margin: 0 auto 8px; display: flex; align-items: center; justify-content: center;">
                                        <span style="color: white; font-size: 16px;">李</span>
                                    </div>
                                    <div style="color: #333; font-size: 11px;">李四</div>
                                    <div style="background: #52c41a; color: white; padding: 1px 6px; border-radius: 8px; font-size: 9px; margin-top: 2px;">就绪</div>
                                </div>
                                <div style="text-align: center;">
                                    <div style="width: 40px; height: 40px; background: linear-gradient(45deg, #ffc107, #ff8f00); border-radius: 50%; margin: 0 auto 8px; display: flex; align-items: center; justify-content: center;">
                                        <span style="color: white; font-size: 16px;">王</span>
                                    </div>
                                    <div style="color: #333; font-size: 11px;">王五</div>
                                    <div style="background: #faad14; color: white; padding: 1px 6px; border-radius: 8px; font-size: 9px; margin-top: 2px;">等待</div>
                                </div>
                                <div style="text-align: center;">
                                    <div style="width: 40px; height: 40px; background: linear-gradient(45deg, #9c27b0, #7b1fa2); border-radius: 50%; margin: 0 auto 8px; display: flex; align-items: center; justify-content: center;">
                                        <span style="color: white; font-size: 16px;">赵</span>
                                    </div>
                                    <div style="color: #333; font-size: 11px;">赵六</div>
                                    <div style="background: #52c41a; color: white; padding: 1px 6px; border-radius: 8px; font-size: 9px; margin-top: 2px;">就绪</div>
                                </div>
                                <div style="text-align: center;">
                                    <div style="width: 40px; height: 40px; background: #e0e0e0; border-radius: 50%; margin: 0 auto 8px; display: flex; align-items: center; justify-content: center; border: 2px dashed #ccc;">
                                        <span style="color: #999; font-size: 16px;">+</span>
                                    </div>
                                    <div style="color: #999; font-size: 11px;">等待加入</div>
                                </div>
                            </div>
                        </div>

                        <div style="background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%); border-radius: 20px; padding: 16px; margin: 20px 0; border: 2px solid #ff9800; max-width: 300px;">
                            <div style="text-align: center;">
                                <div style="color: #f57c00; font-size: 14px; font-weight: 600; margin-bottom: 8px;">💡 游戏即将开始</div>
                                <div style="color: #e65100; font-size: 12px; line-height: 1.4;">
                                    请耐心等待，AI正在为你们生成独特的剧本体验
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="phone-label">游戏等待页面</div>
        </div>

        <!-- 角色分配页面 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="page-content">
                    <div class="page-header">
                        <div style="width: 24px;"></div>
                        <div class="page-title">角色分配</div>
                        <div style="width: 24px;"></div>
                    </div>
                    <div class="page-body">
                        <div class="role-card fade-in">
                            <div class="role-title">🎭 庄园管家</div>
                            <div class="role-desc">你是庄园的忠实管家，掌握着庄园的所有秘密。你对每个人的行踪都了如指掌，但你也有自己不可告人的秘密...</div>
                        </div>

                        <div class="card fade-in">
                            <h4 style="color: #333; margin-bottom: 16px;">🔍 私密线索</h4>
                            <div class="clue-item slide-in">
                                <h4>线索一</h4>
                                <p>昨晚11点，你看到一个神秘身影在花园里徘徊，但你没有告诉任何人。</p>
                            </div>
                            <div class="clue-item slide-in">
                                <h4>线索二</h4>
                                <p>主人的保险箱密码只有你知道，里面藏着一份重要的遗嘱。</p>
                            </div>
                            <div class="clue-item slide-in">
                                <h4>线索三</h4>
                                <p>你发现厨房的刀具少了一把，但你选择保持沉默。</p>
                            </div>
                        </div>

                        <div class="card fade-in">
                            <h4 style="color: #333; margin-bottom: 12px;">🎯 角色目标</h4>
                            <p style="color: #666; font-size: 14px; line-height: 1.5;">
                                保护庄园的秘密，同时找出真正的凶手。你需要在适当的时候透露关键信息，但也要保护自己不被怀疑。
                            </p>
                        </div>

                        <button class="btn-primary">确认角色</button>
                    </div>
                </div>
            </div>
            <div class="phone-label">角色分配</div>
        </div>

        <!-- 讨论阶段页面 - 增强版 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="page-content">
                    <div class="page-header">
                        <div style="width: 24px;"></div>
                        <div class="page-title">讨论阶段</div>
                        <div style="color: white; font-size: 14px;">05:30</div>
                    </div>
                    <div class="page-body" style="padding: 0;">
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 16px; margin: 0; color: white; text-align: center;">
                            <div style="font-size: 14px; font-weight: 500; margin-bottom: 4px;">🏰 神秘庄园案件</div>
                            <div style="font-size: 12px; opacity: 0.9;">庄园主人在书房中被发现死亡，现场一片混乱...</div>
                        </div>

                        <div class="chat-container fade-in" style="height: 450px; margin: 0; border-radius: 0;">
                            <div class="chat-tabs">
                                <button class="chat-tab active">🌍 全员频道</button>
                                <button class="chat-tab">💬 私聊</button>
                                <button class="chat-tab">🔊 语音</button>
                            </div>

                            <div class="chat-messages" style="background: linear-gradient(180deg, #f8f9ff 0%, #f0f4ff 100%);">
                                <div class="message slide-in">
                                    <div class="message-sender" style="color: #667eea;">🕵️ 张三 (侦探)</div>
                                    <div class="message-content" style="background: rgba(102, 126, 234, 0.1); color: #333; border-left: 3px solid #667eea;">
                                        大家好，我是私家侦探。根据现场情况，死者是在昨晚11点到12点之间遇害的。
                                    </div>
                                </div>
                                <div class="message slide-in">
                                    <div class="message-sender" style="color: #52c41a;">👨‍⚕️ 李四 (医生)</div>
                                    <div class="message-content" style="background: rgba(82, 196, 26, 0.1); color: #333; border-left: 3px solid #52c41a;">
                                        我检查了尸体，死因是头部重击，凶器应该是钝器。
                                    </div>
                                </div>
                                <div class="message own slide-in">
                                    <div class="message-sender" style="color: #0052d9; text-align: right;">🏠 我 (管家)</div>
                                    <div class="message-content" style="background: linear-gradient(135deg, #0052d9, #1890ff); color: white; margin-left: auto; border-radius: 18px 18px 4px 18px;">
                                        昨晚我一直在厨房整理，没有听到任何异常声音。
                                    </div>
                                </div>
                                <div class="message slide-in">
                                    <div class="message-sender" style="color: #722ed1;">👩‍💼 王五 (秘书)</div>
                                    <div class="message-content" style="background: rgba(114, 46, 209, 0.1); color: #333; border-left: 3px solid #722ed1;">
                                        等等，管家你确定吗？我记得昨晚11点半左右听到了争吵声。
                                    </div>
                                </div>
                                <div class="message slide-in">
                                    <div class="message-sender" style="color: #fa8c16;">🌱 赵六 (园丁)</div>
                                    <div class="message-content" style="background: rgba(250, 140, 22, 0.1); color: #333; border-left: 3px solid #fa8c16;">
                                        我在花园里工作到很晚，确实看到有人在庄园里走动。
                                        <div style="margin-top: 8px; padding: 8px; background: rgba(250, 140, 22, 0.1); border-radius: 8px; font-size: 12px;">
                                            🎤 <span style="color: #fa8c16;">语音消息 0:15</span>
                                            <button style="background: #fa8c16; color: white; border: none; border-radius: 50%; width: 24px; height: 24px; margin-left: 8px; cursor: pointer;">▶</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="voice-controls">
                                <button class="voice-btn">🎤</button>
                                <input type="text" placeholder="输入消息或长按语音..." style="flex: 1; padding: 8px 12px; border: 1px solid rgba(255,255,255,0.3); border-radius: 20px; background: rgba(255,255,255,0.1); color: white; font-size: 14px;">
                                <button style="background: linear-gradient(45deg, #0052d9, #1890ff); color: white; border: none; border-radius: 20px; padding: 8px 16px; font-size: 14px; cursor: pointer;">发送</button>
                            </div>
                        </div>

                        <div style="padding: 12px; background: rgba(0, 0, 0, 0.05); display: flex; justify-content: space-between; align-items: center;">
                            <div style="display: flex; gap: 8px;">
                                <button style="background: rgba(102, 126, 234, 0.1); color: #667eea; border: 1px solid #667eea; border-radius: 16px; padding: 4px 12px; font-size: 12px; cursor: pointer;">💡 分享线索</button>
                                <button style="background: rgba(250, 140, 22, 0.1); color: #fa8c16; border: 1px solid #fa8c16; border-radius: 16px; padding: 4px 12px; font-size: 12px; cursor: pointer;">🤔 提出质疑</button>
                            </div>
                            <div style="color: #666; font-size: 12px;">在线: 6人</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="phone-label">讨论阶段 - 增强版</div>
        </div>

        <!-- 小游戏页面 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="page-content">
                    <div class="page-header">
                        <div style="width: 24px;"></div>
                        <div class="page-title">你画我猜</div>
                        <div style="color: white; font-size: 14px;">01:30</div>
                    </div>
                    <div class="page-body">
                        <div class="card fade-in" style="margin-bottom: 12px; padding: 12px;">
                            <p style="color: #333; font-size: 16px; text-align: center; font-weight: 500;">
                                题目：<span style="color: #0052d9;">钥匙</span>
                            </p>
                            <p style="color: #666; font-size: 14px; text-align: center; margin-top: 4px;">
                                轮到张三作画，大家猜猜看！
                            </p>
                        </div>

                        <div class="game-canvas fade-in">
                            <div style="text-align: center;">
                                <div style="font-size: 48px; margin-bottom: 8px;">🎨</div>
                                <div>画板区域</div>
                                <div style="font-size: 12px; color: #999; margin-top: 4px;">张三正在作画中...</div>
                            </div>
                        </div>

                        <div class="card fade-in">
                            <h4 style="color: #333; margin-bottom: 12px;">玩家猜测</h4>
                            <div style="background: #f9f9f9; border-radius: 8px; padding: 12px; margin-bottom: 12px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <span style="color: #333; font-size: 14px; font-weight: 500;">李四:</span>
                                    <span style="color: #666; font-size: 14px;">门？</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <span style="color: #333; font-size: 14px; font-weight: 500;">王五:</span>
                                    <span style="color: #666; font-size: 14px;">锁？</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span style="color: #333; font-size: 14px; font-weight: 500;">赵六:</span>
                                    <span style="color: #52c41a; font-size: 14px; font-weight: 500;">钥匙！✓</span>
                                </div>
                            </div>
                            <input type="text" class="input-field" placeholder="输入你的猜测..." style="margin-bottom: 0;">
                        </div>
                    </div>
                </div>
            </div>
            <div class="phone-label">小游戏 - 你画我猜</div>
        </div>

        <!-- 真心话环节页面 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="page-content" style="background: linear-gradient(180deg, #fff0f6 0%, #ffe7f0 100%);">
                    <div class="page-header" style="background: linear-gradient(135deg, #eb2f96 0%, #c41d7f 100%); position: relative; overflow: hidden;">
                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 25% 50%, rgba(255, 255, 255, 0.2) 0%, transparent 60%);"></div>
                        <div style="position: relative; z-index: 1; width: 24px;"></div>
                        <div class="page-title neon-text">💕 真心话时间</div>
                        <div style="color: white; font-size: 14px; position: relative; z-index: 1;">02:30</div>
                    </div>
                    <div class="page-body" style="padding: 0;">
                        <div style="background: linear-gradient(135deg, #eb2f96 0%, #c41d7f 100%); padding: 24px; color: white; text-align: center; position: relative; overflow: hidden;">
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 80% 30%, rgba(255, 255, 255, 0.15) 0%, transparent 60%);"></div>
                            <div style="position: relative; z-index: 1;">
                                <h2 style="font-size: 22px; font-weight: bold; margin-bottom: 8px; text-shadow: 0 2px 8px rgba(0,0,0,0.3);">💖 真心话环节</h2>
                                <p style="font-size: 14px; opacity: 0.95; line-height: 1.6;">
                                    AI为你准备了特别的问题，诚实回答可获得线索或影响力奖励
                                </p>
                            </div>
                        </div>

                        <div style="padding: 24px;">
                            <div class="artistic-card fade-in" style="margin-bottom: 20px; background: linear-gradient(135deg, rgba(235, 47, 150, 0.1) 0%, rgba(196, 29, 127, 0.05) 100%); border: 2px solid rgba(235, 47, 150, 0.2);">
                                <div style="text-align: center; margin-bottom: 20px;">
                                    <div style="width: 60px; height: 60px; background: linear-gradient(45deg, #eb2f96, #c41d7f); border-radius: 50%; margin: 0 auto 16px; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 24px rgba(235, 47, 150, 0.3); animation: pulse 2s infinite;">
                                        <span style="color: white; font-size: 28px;">🤖</span>
                                    </div>
                                    <h3 style="color: #c41d7f; font-size: 18px; margin-bottom: 8px;">AI的问题</h3>
                                    <div style="background: rgba(255, 255, 255, 0.9); border-radius: 16px; padding: 20px; margin-bottom: 16px;">
                                        <p style="color: #333; font-size: 16px; line-height: 1.6; margin: 0; font-weight: 500;">
                                            "你有过几次恋爱？带给你什么感悟"
                                        </p>
                                    </div>
                                    <div style="background: rgba(235, 47, 150, 0.1); border-radius: 12px; padding: 12px;">
                                        <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
                                            <span style="color: #c41d7f; font-size: 14px;">💎 价值观类问题</span>
                                            <span style="color: #666; font-size: 12px;">|</span>
                                            <span style="color: #c41d7f; font-size: 14px;">⭐ 高奖励潜力</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="artistic-card fade-in" style="margin-bottom: 20px;">
                                <h4 style="color: #333; margin-bottom: 16px; display: flex; align-items: center;">
                                    <span style="margin-right: 8px; font-size: 20px;">✍️</span>
                                    你的回答
                                </h4>
                                <textarea class="input-field" style="min-height: 120px; resize: vertical; font-family: inherit; line-height: 1.5;" placeholder="请诚实地分享你的想法和理由...&#10;&#10;提示：详细的回答和真诚的情感表达更容易获得高分"></textarea>
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 12px; font-size: 12px; color: #666;">
                                    <span>💡 提示：真诚度和深度是评分关键</span>
                                    <span id="charCount">0/500字</span>
                                </div>
                            </div>

                            <div class="artistic-card fade-in" style="margin-bottom: 20px;">
                                <h4 style="color: #333; margin-bottom: 16px; display: flex; align-items: center;">
                                    <span style="margin-right: 8px; font-size: 20px;">👥</span>
                                    其他玩家回答
                                </h4>

                                <div style="background: rgba(102, 126, 234, 0.05); border-radius: 12px; padding: 16px; margin-bottom: 12px; border-left: 4px solid #667eea;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <span style="color: #667eea; font-weight: 500; font-size: 14px;">🕵️ 张三 (侦探)</span>
                                        <div style="display: flex; gap: 4px;">
                                            <span style="color: #ff6b6b; font-size: 12px;">❤️ 8.5</span>
                                            <span style="color: #4ecdc4; font-size: 12px;">🎯 9.2</span>
                                            <span style="color: #feca57; font-size: 12px;">💫 8.8</span>
                                        </div>
                                    </div>
                                    <p style="color: #333; font-size: 13px; line-height: 1.5; margin-bottom: 8px;">
                                        "5次，真爱值得等待。"
                                    </p>
                                    <div style="background: rgba(82, 196, 26, 0.1); border-radius: 8px; padding: 8px; font-size: 11px; color: #52c41a;">
                                        🤖 AI评价：回答体现了强烈的爱情观，获得额外线索奖励！
                                    </div>
                                </div>

                                <div style="background: rgba(76, 175, 80, 0.05); border-radius: 12px; padding: 16px; margin-bottom: 12px; border-left: 4px solid #4caf50;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <span style="color: #4caf50; font-weight: 500; font-size: 14px;">👨‍⚕️ 李四 (医生)</span>
                                        <div style="display: flex; gap: 4px;">
                                            <span style="color: #ff6b6b; font-size: 12px;">❤️ 7.2</span>
                                            <span style="color: #4ecdc4; font-size: 12px;">🎯 8.5</span>
                                            <span style="color: #feca57; font-size: 12px;">💫 7.8</span>
                                        </div>
                                    </div>
                                    <p style="color: #333; font-size: 13px; line-height: 1.5; margin-bottom: 8px;">
                                        "20次，男人拜倒在我的石榴裙。"
                                    </p>
                                    <div style="background: rgba(255, 193, 7, 0.1); border-radius: 8px; padding: 8px; font-size: 11px; color: #ffc107;">
                                        🤖 AI评价：诚实地承认了内心的欲望，体现了人性的复杂，获得影响力+15！
                                    </div>
                                </div>
                            </div>

                            <div style="background: linear-gradient(135deg, #fff7e6 0%, #fff2d9 100%); border-radius: 20px; padding: 20px; margin: 20px 0; border: 2px solid #ffd591; position: relative; overflow: hidden;">
                                <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.4) 0%, transparent 50%);"></div>
                                <div style="position: relative; z-index: 1;">
                                    <div style="display: flex; align-items: center; margin-bottom: 16px;">
                                        <span style="font-size: 28px; margin-right: 12px;">🏆</span>
                                        <div>
                                            <h4 style="color: #d46b08; margin: 0; font-size: 16px;">评分标准</h4>
                                            <p style="color: #ad6800; margin: 0; font-size: 12px;">AI将从三个维度评价你的回答</p>
                                        </div>
                                    </div>
                                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px;">
                                        <div style="background: rgba(255, 255, 255, 0.8); border-radius: 12px; padding: 12px; text-align: center;">
                                            <div style="color: #ff6b6b; font-size: 20px; margin-bottom: 4px;">❤️</div>
                                            <div style="color: #333; font-size: 12px; font-weight: 500;">真诚度</div>
                                            <div style="color: #666; font-size: 10px;">情感表达</div>
                                        </div>
                                        <div style="background: rgba(255, 255, 255, 0.8); border-radius: 12px; padding: 12px; text-align: center;">
                                            <div style="color: #4ecdc4; font-size: 20px; margin-bottom: 4px;">🎯</div>
                                            <div style="color: #333; font-size: 12px; font-weight: 500;">深度</div>
                                            <div style="color: #666; font-size: 10px;">思考层次</div>
                                        </div>
                                        <div style="background: rgba(255, 255, 255, 0.8); border-radius: 12px; padding: 12px; text-align: center;">
                                            <div style="color: #feca57; font-size: 20px; margin-bottom: 4px;">💫</div>
                                            <div style="color: #333; font-size: 12px; font-weight: 500;">共鸣度</div>
                                            <div style="color: #666; font-size: 10px;">引起共鸣</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <button class="artistic-btn" style="width: 100%; margin-top: 16px; background: linear-gradient(135deg, #eb2f96 0%, #c41d7f 100%);">
                                💕 提交真心话
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="phone-label">真心话环节</div>
        </div>

        <!-- AI剧本生成Loading页面 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="page-content" style="background: linear-gradient(180deg, #f0f8ff 0%, #e6f3ff 100%); position: relative; overflow: hidden;">
                    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 30% 20%, rgba(102, 126, 234, 0.1) 0%, transparent 50%), radial-gradient(circle at 70% 80%, rgba(138, 43, 226, 0.08) 0%, transparent 50%); pointer-events: none;"></div>
                    <div class="page-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); position: relative; overflow: hidden;">
                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.2) 0%, transparent 70%);"></div>
                        <div style="position: relative; z-index: 1; width: 24px;"></div>
                        <div class="page-title neon-text">🤖 AI剧本生成</div>
                        <div style="width: 24px; position: relative; z-index: 1;"></div>
                    </div>
                    <div class="page-body" style="position: relative; z-index: 1; display: flex; flex-direction: column; justify-content: center; align-items: center; min-height: 500px;">
                        <div class="artistic-card fade-in" style="text-align: center; max-width: 300px; background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%);">
                            <div style="margin-bottom: 24px;">
                                <div style="width: 100px; height: 100px; background: linear-gradient(45deg, #667eea, #764ba2); border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; box-shadow: 0 12px 32px rgba(102, 126, 234, 0.3); animation: pulse 2s infinite;">
                                    <span style="color: white; font-size: 48px;">🧠</span>
                                </div>
                                <h2 style="color: #333; margin-bottom: 12px; font-size: 22px; font-weight: bold;">AI正在创作剧本</h2>
                                <p style="color: #666; font-size: 14px; line-height: 1.6; margin-bottom: 20px;">
                                    基于你们的设置，AI正在生成独特的推理剧本...
                                </p>
                            </div>

                            <div style="margin-bottom: 24px;">
                                <div style="background: #f0f0f0; height: 8px; border-radius: 4px; overflow: hidden; margin-bottom: 12px;">
                                    <div style="background: linear-gradient(90deg, #667eea, #764ba2); height: 100%; width: 75%; border-radius: 4px; animation: shimmer 2s infinite;"></div>
                                </div>
                                <div style="color: #667eea; font-size: 14px; font-weight: 500;">生成进度: 75%</div>
                            </div>

                            <div style="text-align: left; background: rgba(102, 126, 234, 0.05); border-radius: 12px; padding: 16px;">
                                <div style="color: #52c41a; font-size: 13px; margin-bottom: 6px;">✅ 背景故事生成完成</div>
                                <div style="color: #52c41a; font-size: 13px; margin-bottom: 6px;">✅ 角色设定创建完成</div>
                                <div style="color: #52c41a; font-size: 13px; margin-bottom: 6px;">✅ 线索系统构建完成</div>
                                <div style="color: #1890ff; font-size: 13px; margin-bottom: 6px;">🔄 人物关系网络生成中...</div>
                                <div style="color: #d9d9d9; font-size: 13px;">⏳ 剧情分支优化待处理</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="phone-label">AI剧本生成中</div>
        </div>

        <!-- 背景故事展示页面 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="page-content" style="background: linear-gradient(180deg, #f6f0ff 0%, #f0e6ff 100%);">
                    <div class="page-header" style="background: linear-gradient(135deg, #722ed1 0%, #9254de 100%); position: relative; overflow: hidden;">
                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.2) 0%, transparent 70%);"></div>
                        <svg style="position: relative; z-index: 1;" width="24" height="24" viewBox="0 0 24 24" fill="white">
                            <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                        </svg>
                        <div class="page-title neon-text" style="position: relative; z-index: 1;">📖 剧本背景</div>
                        <div style="width: 24px; position: relative; z-index: 1;"></div>
                    </div>
                    <div class="page-body" style="padding: 0;">
                        <div style="background: linear-gradient(135deg, #722ed1 0%, #9254de 100%); padding: 24px; color: white; text-align: center; position: relative; overflow: hidden;">
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 80% 30%, rgba(255, 255, 255, 0.15) 0%, transparent 60%);"></div>
                            <div style="position: relative; z-index: 1;">
                                <h2 style="font-size: 24px; font-weight: bold; margin-bottom: 8px; text-shadow: 0 2px 8px rgba(0,0,0,0.3);">🏰 雾夜庄园疑案</h2>
                                <p style="font-size: 14px; opacity: 0.95; line-height: 1.6;">
                                    AI为你们量身定制的推理剧本
                                </p>
                            </div>
                        </div>

                        <div style="padding: 24px;">
                            <div class="artistic-card fade-in" style="margin-bottom: 20px;">
                                <div style="text-align: center; margin-bottom: 20px;">
                                    <div style="width: 80px; height: 80px; background: linear-gradient(45deg, #722ed1, #9254de); border-radius: 50%; margin: 0 auto 16px; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 24px rgba(114, 46, 209, 0.3);">
                                        <span style="color: white; font-size: 36px;">🌙</span>
                                    </div>
                                    <h3 style="color: #333; font-size: 20px; margin-bottom: 16px;">故事背景</h3>
                                </div>

                                <div style="background: rgba(114, 46, 209, 0.05); border-radius: 16px; padding: 20px; margin-bottom: 20px; border-left: 4px solid #722ed1;">
                                    <p style="color: #333; font-size: 15px; line-height: 1.7; margin: 0;">
                                        1920年的一个雾夜，著名收藏家威廉·阿什福德在自己的庄园中举办了一场私人聚会。客人们包括他的私人医生、秘书、园丁，以及一位神秘的古董商人。然而，当夜幕降临时，威廉却在自己的书房中被发现死亡，现场一片混乱...
                                    </p>
                                </div>

                                <div style="background: rgba(255, 193, 7, 0.1); border-radius: 12px; padding: 16px; margin-bottom: 16px;">
                                    <h4 style="color: #d46b08; margin-bottom: 12px; display: flex; align-items: center;">
                                        <span style="margin-right: 8px;">🎭</span>
                                        剧本特色
                                    </h4>
                                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px;">
                                        <div style="background: rgba(255, 255, 255, 0.8); border-radius: 8px; padding: 8px; text-align: center;">
                                            <div style="color: #722ed1; font-size: 16px; margin-bottom: 2px;">🕰️</div>
                                            <div style="color: #333; font-size: 11px;">复古时代</div>
                                        </div>
                                        <div style="background: rgba(255, 255, 255, 0.8); border-radius: 8px; padding: 8px; text-align: center;">
                                            <div style="color: #722ed1; font-size: 16px; margin-bottom: 2px;">🔍</div>
                                            <div style="color: #333; font-size: 11px;">推理解谜</div>
                                        </div>
                                        <div style="background: rgba(255, 255, 255, 0.8); border-radius: 8px; padding: 8px; text-align: center;">
                                            <div style="color: #722ed1; font-size: 16px; margin-bottom: 2px;">👥</div>
                                            <div style="color: #333; font-size: 11px;">人物关系</div>
                                        </div>
                                        <div style="background: rgba(255, 255, 255, 0.8); border-radius: 8px; padding: 8px; text-align: center;">
                                            <div style="color: #722ed1; font-size: 16px; margin-bottom: 2px;">🎲</div>
                                            <div style="color: #333; font-size: 11px;">多重结局</div>
                                        </div>
                                    </div>
                                </div>

                                <div style="background: rgba(82, 196, 26, 0.1); border-radius: 12px; padding: 16px;">
                                    <h4 style="color: #389e0d; margin-bottom: 8px; display: flex; align-items: center;">
                                        <span style="margin-right: 8px;">💡</span>
                                        游戏提示
                                    </h4>
                                    <ul style="color: #333; font-size: 13px; line-height: 1.6; margin: 0; padding-left: 16px;">
                                        <li>仔细观察每个角色的言行举止</li>
                                        <li>注意时间线的逻辑关系</li>
                                        <li>善用真心话环节获取关键信息</li>
                                        <li>团队合作比个人推理更重要</li>
                                    </ul>
                                </div>
                            </div>

                            <button class="artistic-btn" style="width: 100%; background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);">
                                📋 查看角色分配
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="phone-label">剧本背景故事</div>
        </div>

        <!-- 人物关系网络图页面 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="page-content" style="background: linear-gradient(180deg, #f0f9ff 0%, #e6f4ff 100%);">
                    <div class="page-header" style="background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%); position: relative; overflow: hidden;">
                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.2) 0%, transparent 60%);"></div>
                        <svg style="position: relative; z-index: 1;" width="24" height="24" viewBox="0 0 24 24" fill="white">
                            <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                        </svg>
                        <div class="page-title neon-text" style="position: relative; z-index: 1;">🕸️ 人物关系</div>
                        <div style="width: 24px; position: relative; z-index: 1;"></div>
                    </div>
                    <div class="page-body" style="padding: 0;">
                        <div style="background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%); padding: 24px; color: white; text-align: center; position: relative; overflow: hidden;">
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 70% 40%, rgba(255, 255, 255, 0.15) 0%, transparent 60%);"></div>
                            <div style="position: relative; z-index: 1;">
                                <h2 style="font-size: 22px; font-weight: bold; margin-bottom: 8px; text-shadow: 0 2px 8px rgba(0,0,0,0.3);">🔗 关系网络</h2>
                                <p style="font-size: 14px; opacity: 0.95; line-height: 1.6;">
                                    探索角色间的复杂关系，寻找隐藏的线索
                                </p>
                            </div>
                        </div>

                        <div style="padding: 24px;">
                            <div class="artistic-card fade-in" style="margin-bottom: 20px; position: relative; min-height: 300px;">
                                <h3 style="color: #333; text-align: center; margin-bottom: 24px; font-size: 18px;">庄园人物关系图</h3>

                                <!-- 中心人物 -->
                                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 10;">
                                    <div style="width: 80px; height: 80px; background: linear-gradient(45deg, #ff6b6b, #ee5a52); border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 24px rgba(255, 107, 107, 0.4); border: 4px solid white;">
                                        <span style="color: white; font-size: 24px;">👑</span>
                                    </div>
                                    <div style="text-align: center; margin-top: 8px;">
                                        <div style="color: #333; font-size: 12px; font-weight: 600;">威廉·阿什福德</div>
                                        <div style="color: #666; font-size: 10px;">庄园主人 (死者)</div>
                                    </div>
                                </div>

                                <!-- 连接线 -->
                                <svg style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1;" viewBox="0 0 300 300">
                                    <!-- 连接线 -->
                                    <line x1="150" y1="150" x2="80" y2="80" stroke="#1890ff" stroke-width="2" stroke-dasharray="5,5" opacity="0.6"/>
                                    <line x1="150" y1="150" x2="220" y2="80" stroke="#52c41a" stroke-width="2" stroke-dasharray="5,5" opacity="0.6"/>
                                    <line x1="150" y1="150" x2="80" y2="220" stroke="#722ed1" stroke-width="2" stroke-dasharray="5,5" opacity="0.6"/>
                                    <line x1="150" y1="150" x2="220" y2="220" stroke="#fa8c16" stroke-width="2" stroke-dasharray="5,5" opacity="0.6"/>

                                    <!-- 关系标签 -->
                                    <text x="115" y="115" fill="#1890ff" font-size="8" text-anchor="middle">雇佣关系</text>
                                    <text x="185" y="115" fill="#52c41a" font-size="8" text-anchor="middle">医患关系</text>
                                    <text x="115" y="185" fill="#722ed1" font-size="8" text-anchor="middle">商业伙伴</text>
                                    <text x="185" y="185" fill="#fa8c16" font-size="8" text-anchor="middle">旧识</text>
                                </svg>

                                <!-- 周围人物 -->
                                <div style="position: absolute; top: 20px; left: 20px;">
                                    <div style="width: 60px; height: 60px; background: linear-gradient(45deg, #1890ff, #096dd9); border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 6px 16px rgba(24, 144, 255, 0.3); border: 3px solid white;">
                                        <span style="color: white; font-size: 20px;">👩‍💼</span>
                                    </div>
                                    <div style="text-align: center; margin-top: 6px;">
                                        <div style="color: #333; font-size: 11px; font-weight: 500;">艾米丽</div>
                                        <div style="color: #666; font-size: 9px;">私人秘书</div>
                                    </div>
                                </div>

                                <div style="position: absolute; top: 20px; right: 20px;">
                                    <div style="width: 60px; height: 60px; background: linear-gradient(45deg, #52c41a, #389e0d); border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 6px 16px rgba(82, 196, 26, 0.3); border: 3px solid white;">
                                        <span style="color: white; font-size: 20px;">👨‍⚕️</span>
                                    </div>
                                    <div style="text-align: center; margin-top: 6px;">
                                        <div style="color: #333; font-size: 11px; font-weight: 500;">约翰医生</div>
                                        <div style="color: #666; font-size: 9px;">私人医生</div>
                                    </div>
                                </div>

                                <div style="position: absolute; bottom: 60px; left: 20px;">
                                    <div style="width: 60px; height: 60px; background: linear-gradient(45deg, #722ed1, #9254de); border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 6px 16px rgba(114, 46, 209, 0.3); border: 3px solid white;">
                                        <span style="color: white; font-size: 20px;">🎩</span>
                                    </div>
                                    <div style="text-align: center; margin-top: 6px;">
                                        <div style="color: #333; font-size: 11px; font-weight: 500;">查尔斯</div>
                                        <div style="color: #666; font-size: 9px;">古董商人</div>
                                    </div>
                                </div>

                                <div style="position: absolute; bottom: 60px; right: 20px;">
                                    <div style="width: 60px; height: 60px; background: linear-gradient(45deg, #fa8c16, #d46b08); border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 6px 16px rgba(250, 140, 22, 0.3); border: 3px solid white;">
                                        <span style="color: white; font-size: 20px;">🌱</span>
                                    </div>
                                    <div style="text-align: center; margin-top: 6px;">
                                        <div style="color: #333; font-size: 11px; font-weight: 500;">汤姆</div>
                                        <div style="color: #666; font-size: 9px;">园丁</div>
                                    </div>
                                </div>
                            </div>

                            <div class="artistic-card fade-in" style="margin-bottom: 20px;">
                                <h4 style="color: #333; margin-bottom: 16px; display: flex; align-items: center;">
                                    <span style="margin-right: 8px;">🔍</span>
                                    关系详情
                                </h4>

                                <div style="display: flex; flex-direction: column; gap: 12px;">
                                    <div style="background: rgba(24, 144, 255, 0.05); border-radius: 12px; padding: 12px; border-left: 4px solid #1890ff;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                                            <span style="color: #1890ff; font-weight: 500; font-size: 13px;">👩‍💼 艾米丽 (秘书)</span>
                                            <span style="background: rgba(24, 144, 255, 0.2); color: #1890ff; padding: 2px 8px; border-radius: 10px; font-size: 10px;">工作5年</span>
                                        </div>
                                        <p style="color: #333; font-size: 12px; margin: 0; line-height: 1.4;">
                                            掌握庄园主的所有商业秘密，最近因为薪资问题与威廉产生分歧
                                        </p>
                                    </div>

                                    <div style="background: rgba(82, 196, 26, 0.05); border-radius: 12px; padding: 12px; border-left: 4px solid #52c41a;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                                            <span style="color: #52c41a; font-weight: 500; font-size: 13px;">👨‍⚕️ 约翰医生</span>
                                            <span style="background: rgba(82, 196, 26, 0.2); color: #52c41a; padding: 2px 8px; border-radius: 10px; font-size: 10px;">老友</span>
                                        </div>
                                        <p style="color: #333; font-size: 12px; margin: 0; line-height: 1.4;">
                                            威廉的大学同窗，了解他的健康状况，最近建议他修改遗嘱
                                        </p>
                                    </div>

                                    <div style="background: rgba(114, 46, 209, 0.05); border-radius: 12px; padding: 12px; border-left: 4px solid #722ed1;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                                            <span style="color: #722ed1; font-weight: 500; font-size: 13px;">🎩 查尔斯 (古董商)</span>
                                            <span style="background: rgba(114, 46, 209, 0.2); color: #722ed1; padding: 2px 8px; border-radius: 10px; font-size: 10px;">新合作</span>
                                        </div>
                                        <p style="color: #333; font-size: 12px; margin: 0; line-height: 1.4;">
                                            神秘的古董商人，与威廉有一笔价值不菲的古董交易正在进行
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <button class="artistic-btn" style="width: 100%; background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);">
                                🎭 开始角色扮演
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="phone-label">人物关系网络图</div>
        </div>

        <!-- 游戏设置页面 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="page-content" style="background: linear-gradient(180deg, #f9f0ff 0%, #f0e6ff 100%);">
                    <div class="page-header" style="background: linear-gradient(135deg, #9254de 0%, #722ed1 100%); position: relative; overflow: hidden;">
                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 60% 40%, rgba(255, 255, 255, 0.2) 0%, transparent 60%);"></div>
                        <svg style="position: relative; z-index: 1;" width="24" height="24" viewBox="0 0 24 24" fill="white">
                            <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                        </svg>
                        <div class="page-title neon-text" style="position: relative; z-index: 1;">⚙️ 游戏设置</div>
                        <div style="width: 24px; position: relative; z-index: 1;"></div>
                    </div>
                    <div class="page-body">
                        <div class="artistic-card fade-in" style="margin-bottom: 20px;">
                            <h3 style="color: #333; margin-bottom: 20px; display: flex; align-items: center;">
                                <span style="margin-right: 12px; font-size: 24px;">🎮</span>
                                房间规则设置
                            </h3>

                            <div style="margin-bottom: 20px;">
                                <label style="color: #666; font-size: 14px; margin-bottom: 8px; display: block; font-weight: 500;">讨论时间限制</label>
                                <div style="display: flex; gap: 8px; margin-bottom: 16px;">
                                    <button class="persistence-choice" style="flex: 1; padding: 12px; margin: 0; background: rgba(146, 84, 222, 0.1); border-color: #9254de;">
                                        <div style="text-align: center;">
                                            <div style="color: #9254de; font-size: 16px; font-weight: 600;">5分钟</div>
                                            <div style="color: #666; font-size: 11px;">快节奏</div>
                                        </div>
                                    </button>
                                    <button class="persistence-choice selected" style="flex: 1; padding: 12px; margin: 0;">
                                        <div style="text-align: center;">
                                            <div style="color: #333; font-size: 16px; font-weight: 600;">10分钟</div>
                                            <div style="color: #666; font-size: 11px;">标准</div>
                                        </div>
                                    </button>
                                    <button class="persistence-choice" style="flex: 1; padding: 12px; margin: 0; background: rgba(146, 84, 222, 0.1); border-color: #9254de;">
                                        <div style="text-align: center;">
                                            <div style="color: #9254de; font-size: 16px; font-weight: 600;">15分钟</div>
                                            <div style="color: #666; font-size: 11px;">深度讨论</div>
                                        </div>
                                    </button>
                                </div>

                                <label style="color: #666; font-size: 14px; margin-bottom: 8px; display: block; font-weight: 500;">投票时间</label>
                                <div style="display: flex; gap: 8px; margin-bottom: 16px;">
                                    <button class="persistence-choice" style="flex: 1; padding: 12px; margin: 0; background: rgba(146, 84, 222, 0.1); border-color: #9254de;">
                                        <div style="text-align: center;">
                                            <div style="color: #9254de; font-size: 16px; font-weight: 600;">2分钟</div>
                                            <div style="color: #666; font-size: 11px;">快速决策</div>
                                        </div>
                                    </button>
                                    <button class="persistence-choice selected" style="flex: 1; padding: 12px; margin: 0;">
                                        <div style="text-align: center;">
                                            <div style="color: #333; font-size: 16px; font-weight: 600;">3分钟</div>
                                            <div style="color: #666; font-size: 11px;">标准</div>
                                        </div>
                                    </button>
                                    <button class="persistence-choice" style="flex: 1; padding: 12px; margin: 0; background: rgba(146, 84, 222, 0.1); border-color: #9254de;">
                                        <div style="text-align: center;">
                                            <div style="color: #9254de; font-size: 16px; font-weight: 600;">5分钟</div>
                                            <div style="color: #666; font-size: 11px;">充分思考</div>
                                        </div>
                                    </button>
                                </div>

                                <label style="color: #666; font-size: 14px; margin-bottom: 8px; display: block; font-weight: 500;">坚持机制时间</label>
                                <div style="display: flex; gap: 8px;">
                                    <button class="persistence-choice selected" style="flex: 1; padding: 12px; margin: 0;">
                                        <div style="text-align: center;">
                                            <div style="color: #333; font-size: 16px; font-weight: 600;">30秒</div>
                                            <div style="color: #666; font-size: 11px;">标准</div>
                                        </div>
                                    </button>
                                    <button class="persistence-choice" style="flex: 1; padding: 12px; margin: 0; background: rgba(146, 84, 222, 0.1); border-color: #9254de;">
                                        <div style="text-align: center;">
                                            <div style="color: #9254de; font-size: 16px; font-weight: 600;">60秒</div>
                                            <div style="color: #666; font-size: 11px;">充分考虑</div>
                                        </div>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="artistic-card fade-in" style="margin-bottom: 20px;">
                            <h3 style="color: #333; margin-bottom: 20px; display: flex; align-items: center;">
                                <span style="margin-right: 12px; font-size: 24px;">🎯</span>
                                游戏难度调整
                            </h3>

                            <div style="margin-bottom: 20px;">
                                <label style="color: #666; font-size: 14px; margin-bottom: 12px; display: block; font-weight: 500;">线索提示程度</label>
                                <div style="background: #f0f0f0; height: 8px; border-radius: 4px; margin-bottom: 8px; position: relative;">
                                    <div style="background: linear-gradient(90deg, #52c41a, #faad14, #ff4d4f); height: 100%; width: 60%; border-radius: 4px;"></div>
                                    <div style="position: absolute; top: -2px; left: 60%; width: 12px; height: 12px; background: white; border: 2px solid #9254de; border-radius: 50%; cursor: pointer;"></div>
                                </div>
                                <div style="display: flex; justify-content: space-between; font-size: 11px; color: #666;">
                                    <span>明显提示</span>
                                    <span>适中</span>
                                    <span>隐晦线索</span>
                                </div>
                            </div>

                            <div style="margin-bottom: 20px;">
                                <label style="color: #666; font-size: 14px; margin-bottom: 12px; display: block; font-weight: 500;">AI干预程度</label>
                                <div style="background: #f0f0f0; height: 8px; border-radius: 4px; margin-bottom: 8px; position: relative;">
                                    <div style="background: linear-gradient(90deg, #1890ff, #722ed1); height: 100%; width: 40%; border-radius: 4px;"></div>
                                    <div style="position: absolute; top: -2px; left: 40%; width: 12px; height: 12px; background: white; border: 2px solid #9254de; border-radius: 50%; cursor: pointer;"></div>
                                </div>
                                <div style="display: flex; justify-content: space-between; font-size: 11px; color: #666;">
                                    <span>最少干预</span>
                                    <span>适度引导</span>
                                    <span>积极参与</span>
                                </div>
                            </div>
                        </div>

                        <div class="artistic-card fade-in" style="margin-bottom: 20px;">
                            <h3 style="color: #333; margin-bottom: 20px; display: flex; align-items: center;">
                                <span style="margin-right: 12px; font-size: 24px;">🔧</span>
                                高级设置
                            </h3>

                            <div style="display: flex; flex-direction: column; gap: 16px;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <div style="color: #333; font-size: 14px; font-weight: 500;">允许中途加入</div>
                                        <div style="color: #666; font-size: 12px;">游戏开始后允许新玩家加入</div>
                                    </div>
                                    <div style="width: 44px; height: 24px; background: #0052d9; border-radius: 12px; position: relative; cursor: pointer;">
                                        <div style="width: 20px; height: 20px; background: white; border-radius: 50%; position: absolute; top: 2px; right: 2px; transition: all 0.3s;"></div>
                                    </div>
                                </div>

                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <div style="color: #333; font-size: 14px; font-weight: 500;">观战模式</div>
                                        <div style="color: #666; font-size: 12px;">允许观众观看游戏进程</div>
                                    </div>
                                    <div style="width: 44px; height: 24px; background: #e0e0e0; border-radius: 12px; position: relative; cursor: pointer;">
                                        <div style="width: 20px; height: 20px; background: white; border-radius: 50%; position: absolute; top: 2px; left: 2px; transition: all 0.3s;"></div>
                                    </div>
                                </div>

                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <div style="color: #333; font-size: 14px; font-weight: 500;">自动保存回放</div>
                                        <div style="color: #666; font-size: 12px;">自动保存游戏过程供回看</div>
                                    </div>
                                    <div style="width: 44px; height: 24px; background: #0052d9; border-radius: 12px; position: relative; cursor: pointer;">
                                        <div style="width: 20px; height: 20px; background: white; border-radius: 50%; position: absolute; top: 2px; right: 2px; transition: all 0.3s;"></div>
                                    </div>
                                </div>

                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <div style="color: #333; font-size: 14px; font-weight: 500;">匿名模式</div>
                                        <div style="color: #666; font-size: 12px;">隐藏玩家真实身份</div>
                                    </div>
                                    <div style="width: 44px; height: 24px; background: #e0e0e0; border-radius: 12px; position: relative; cursor: pointer;">
                                        <div style="width: 20px; height: 20px; background: white; border-radius: 50%; position: absolute; top: 2px; left: 2px; transition: all 0.3s;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button class="artistic-btn" style="width: 100%; background: linear-gradient(135deg, #9254de 0%, #722ed1 100%);">
                            💾 保存设置
                        </button>
                    </div>
                </div>
            </div>
            <div class="phone-label">游戏设置</div>
        </div>

        <!-- 私密线索页面 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="page-content" style="background: linear-gradient(180deg, #fff7e6 0%, #fff2d9 100%);">
                    <div class="page-header" style="background: linear-gradient(135deg, #fa8c16 0%, #d46b08 100%); position: relative; overflow: hidden;">
                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.2) 0%, transparent 60%);"></div>
                        <svg style="position: relative; z-index: 1;" width="24" height="24" viewBox="0 0 24 24" fill="white">
                            <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                        </svg>
                        <div class="page-title neon-text" style="position: relative; z-index: 1;">🔐 私密线索</div>
                        <div style="width: 24px; position: relative; z-index: 1;"></div>
                    </div>
                    <div class="page-body" style="padding: 0;">
                        <div style="background: linear-gradient(135deg, #fa8c16 0%, #d46b08 100%); padding: 24px; color: white; text-align: center; position: relative; overflow: hidden;">
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 60%);"></div>
                            <div style="position: relative; z-index: 1;">
                                <h2 style="font-size: 22px; font-weight: bold; margin-bottom: 8px; text-shadow: 0 2px 8px rgba(0,0,0,0.3);">🕵️ 你的秘密</h2>
                                <p style="font-size: 14px; opacity: 0.95; line-height: 1.6;">
                                    只有你知道的关键信息，合理使用将影响游戏走向
                                </p>
                            </div>
                        </div>

                        <div style="padding: 24px;">
                            <div class="artistic-card fade-in" style="margin-bottom: 20px; background: linear-gradient(135deg, rgba(250, 140, 22, 0.1) 0%, rgba(212, 107, 8, 0.05) 100%); border: 2px solid rgba(250, 140, 22, 0.3);">
                                <div style="text-align: center; margin-bottom: 20px;">
                                    <div style="width: 60px; height: 60px; background: linear-gradient(45deg, #fa8c16, #d46b08); border-radius: 50%; margin: 0 auto 16px; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 24px rgba(250, 140, 22, 0.3); animation: glow 2s ease-in-out infinite;">
                                        <span style="color: white; font-size: 28px;">🔒</span>
                                    </div>
                                    <h3 style="color: #d46b08; font-size: 18px; margin-bottom: 8px;">你的角色身份</h3>
                                    <div style="background: rgba(255, 255, 255, 0.9); border-radius: 16px; padding: 16px;">
                                        <h4 style="color: #333; font-size: 16px; margin-bottom: 8px;">👩‍💼 艾米丽·哈里森</h4>
                                        <p style="color: #666; font-size: 14px; margin: 0;">威廉·阿什福德的私人秘书</p>
                                    </div>
                                </div>
                            </div>

                            <div class="artistic-card fade-in" style="margin-bottom: 20px;">
                                <h4 style="color: #333; margin-bottom: 16px; display: flex; align-items: center;">
                                    <span style="margin-right: 8px; font-size: 20px;">📋</span>
                                    你的背景故事
                                </h4>
                                <div style="background: rgba(250, 140, 22, 0.05); border-radius: 12px; padding: 16px; border-left: 4px solid #fa8c16;">
                                    <p style="color: #333; font-size: 14px; line-height: 1.6; margin: 0;">
                                        你在阿什福德庄园工作了5年，深受威廉信任。你掌握着他所有的商业秘密和私人事务。最近，你发现威廉准备修改遗嘱，这让你感到不安，因为你一直期望能在遗嘱中获得一份遗产作为多年忠诚服务的回报。
                                    </p>
                                </div>
                            </div>

                            <div class="artistic-card fade-in" style="margin-bottom: 20px;">
                                <h4 style="color: #333; margin-bottom: 16px; display: flex; align-items: center;">
                                    <span style="margin-right: 8px; font-size: 20px;">🔍</span>
                                    私密线索
                                </h4>

                                <div style="display: flex; flex-direction: column; gap: 12px;">
                                    <div style="background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(238, 90, 82, 0.05) 100%); border-radius: 12px; padding: 16px; border: 2px solid rgba(255, 107, 107, 0.2); position: relative;">
                                        <div style="position: absolute; top: 8px; right: 8px; background: #ff6b6b; color: white; padding: 2px 8px; border-radius: 10px; font-size: 10px; font-weight: 500;">重要</div>
                                        <h5 style="color: #d32f2f; margin-bottom: 8px; font-size: 14px; font-weight: 600;">🗝️ 保险箱密码</h5>
                                        <p style="color: #333; font-size: 13px; line-height: 1.5; margin: 0;">
                                            你知道威廉书房保险箱的密码是他已故妻子的生日：0312。里面可能藏着重要文件。
                                        </p>
                                    </div>

                                    <div style="background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 143, 0, 0.05) 100%); border-radius: 12px; padding: 16px; border: 2px solid rgba(255, 193, 7, 0.2); position: relative;">
                                        <div style="position: absolute; top: 8px; right: 8px; background: #ffc107; color: white; padding: 2px 8px; border-radius: 10px; font-size: 10px; font-weight: 500;">机密</div>
                                        <h5 style="color: #f57c00; margin-bottom: 8px; font-size: 14px; font-weight: 600;">📞 神秘电话</h5>
                                        <p style="color: #333; font-size: 13px; line-height: 1.5; margin: 0;">
                                            案发前一天，你接到一个神秘电话，对方询问威廉的日程安排。你没有告诉任何人这件事。
                                        </p>
                                    </div>

                                    <div style="background: linear-gradient(135deg, rgba(114, 46, 209, 0.1) 0%, rgba(146, 84, 222, 0.05) 100%); border-radius: 12px; padding: 16px; border: 2px solid rgba(114, 46, 209, 0.2); position: relative;">
                                        <div style="position: absolute; top: 8px; right: 8px; background: #722ed1; color: white; padding: 2px 8px; border-radius: 10px; font-size: 10px; font-weight: 500;">隐秘</div>
                                        <h5 style="color: #722ed1; margin-bottom: 8px; font-size: 14px; font-weight: 600;">💰 财务异常</h5>
                                        <p style="color: #333; font-size: 13px; line-height: 1.5; margin: 0;">
                                            最近你发现威廉的账户有大笔资金异常流动，但他拒绝向你解释原因。
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div class="artistic-card fade-in" style="margin-bottom: 20px;">
                                <h4 style="color: #333; margin-bottom: 16px; display: flex; align-items: center;">
                                    <span style="margin-right: 8px; font-size: 20px;">🎯</span>
                                    你的目标
                                </h4>
                                <div style="background: rgba(82, 196, 26, 0.1); border-radius: 12px; padding: 16px; border-left: 4px solid #52c41a;">
                                    <ul style="color: #333; font-size: 13px; line-height: 1.6; margin: 0; padding-left: 16px;">
                                        <li>保护自己不被怀疑</li>
                                        <li>找出真正的凶手</li>
                                        <li>合理使用你掌握的秘密信息</li>
                                        <li>在适当时机分享关键线索</li>
                                    </ul>
                                </div>
                            </div>

                            <div style="background: linear-gradient(135deg, #fff2e8 0%, #ffe7d3 100%); border-radius: 20px; padding: 20px; margin: 20px 0; border: 2px solid #ff7a45; position: relative; overflow: hidden;">
                                <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.4) 0%, transparent 50%);"></div>
                                <div style="position: relative; z-index: 1;">
                                    <div style="display: flex; align-items: center; margin-bottom: 12px;">
                                        <span style="font-size: 24px; margin-right: 12px;">⚠️</span>
                                        <div>
                                            <h4 style="color: #d4380d; margin: 0; font-size: 16px;">使用提示</h4>
                                            <p style="color: #ad2102; margin: 0; font-size: 12px;">合理时机分享线索更有效</p>
                                        </div>
                                    </div>
                                    <ul style="color: #333; font-size: 12px; line-height: 1.5; margin: 0; padding-left: 16px;">
                                        <li>过早暴露所有信息可能让你成为目标</li>
                                        <li>在关键时刻分享线索能获得更多信任</li>
                                        <li>观察其他人的反应，判断分享时机</li>
                                    </ul>
                                </div>
                            </div>

                            <button class="artistic-btn" style="width: 100%; background: linear-gradient(135deg, #fa8c16 0%, #d46b08 100%);">
                                🎭 开始游戏
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="phone-label">私密线索页面</div>
        </div>

        <!-- 历史记录页面 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="page-content" style="background: linear-gradient(180deg, #f0f5ff 0%, #e6f0ff 100%);">
                    <div class="page-header" style="background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%); position: relative; overflow: hidden;">
                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.2) 0%, transparent 60%);"></div>
                        <svg style="position: relative; z-index: 1;" width="24" height="24" viewBox="0 0 24 24" fill="white">
                            <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                        </svg>
                        <div class="page-title neon-text" style="position: relative; z-index: 1;">📚 历史记录</div>
                        <div style="width: 24px; position: relative; z-index: 1;"></div>
                    </div>
                    <div class="page-body">
                        <div class="artistic-card fade-in" style="margin-bottom: 20px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                <h3 style="color: #333; margin: 0; display: flex; align-items: center;">
                                    <span style="margin-right: 12px; font-size: 24px;">🎮</span>
                                    游戏历史
                                </h3>
                                <div style="background: rgba(24, 144, 255, 0.1); color: #1890ff; padding: 4px 12px; border-radius: 16px; font-size: 12px; font-weight: 500;">
                                    总计 15 场
                                </div>
                            </div>

                            <div style="display: flex; flex-direction: column; gap: 12px;">
                                <div style="background: linear-gradient(135deg, rgba(82, 196, 26, 0.1) 0%, rgba(56, 142, 60, 0.05) 100%); border-radius: 12px; padding: 16px; border-left: 4px solid #52c41a; position: relative;">
                                    <div style="position: absolute; top: 12px; right: 12px; background: #52c41a; color: white; padding: 2px 8px; border-radius: 10px; font-size: 10px; font-weight: 500;">胜利</div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <h4 style="color: #333; margin: 0; font-size: 15px; font-weight: 600;">🏰 雾夜庄园疑案</h4>
                                        <span style="color: #666; font-size: 11px;">2小时前</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <span style="color: #666; font-size: 12px;">扮演角色: 私人秘书</span>
                                        <span style="color: #52c41a; font-size: 12px; font-weight: 500;">+25 影响力</span>
                                    </div>
                                    <div style="display: flex; gap: 6px;">
                                        <span style="background: rgba(82, 196, 26, 0.2); color: #52c41a; padding: 2px 8px; border-radius: 10px; font-size: 10px;">推理正确</span>
                                        <span style="background: rgba(255, 193, 7, 0.2); color: #ffc107; padding: 2px 8px; border-radius: 10px; font-size: 10px;">MVP</span>
                                        <span style="background: rgba(24, 144, 255, 0.2); color: #1890ff; padding: 2px 8px; border-radius: 10px; font-size: 10px;">团队合作</span>
                                    </div>
                                </div>

                                <div style="background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(238, 90, 82, 0.05) 100%); border-radius: 12px; padding: 16px; border-left: 4px solid #ff6b6b; position: relative;">
                                    <div style="position: absolute; top: 12px; right: 12px; background: #ff6b6b; color: white; padding: 2px 8px; border-radius: 10px; font-size: 10px; font-weight: 500;">失败</div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <h4 style="color: #333; margin: 0; font-size: 15px; font-weight: 600;">🏫 校园悬案</h4>
                                        <span style="color: #666; font-size: 11px;">1天前</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <span style="color: #666; font-size: 12px;">扮演角色: 学生会长</span>
                                        <span style="color: #ff6b6b; font-size: 12px; font-weight: 500;">-5 影响力</span>
                                    </div>
                                    <div style="display: flex; gap: 6px;">
                                        <span style="background: rgba(255, 107, 107, 0.2); color: #ff6b6b; padding: 2px 8px; border-radius: 10px; font-size: 10px;">推理错误</span>
                                        <span style="background: rgba(156, 39, 176, 0.2); color: #9c27b0; padding: 2px 8px; border-radius: 10px; font-size: 10px;">被投出局</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="artistic-card fade-in" style="margin-bottom: 20px;">
                            <h3 style="color: #333; margin-bottom: 20px; display: flex; align-items: center;">
                                <span style="margin-right: 12px; font-size: 24px;">🏆</span>
                                成就系统
                            </h3>

                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px;">
                                <div style="background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%); border-radius: 12px; padding: 16px; text-align: center; border: 2px solid rgba(255, 215, 0, 0.3);">
                                    <div style="font-size: 32px; margin-bottom: 8px;">🕵️</div>
                                    <div style="color: #333; font-size: 12px; font-weight: 600; margin-bottom: 4px;">推理大师</div>
                                    <div style="color: #666; font-size: 10px;">连续推理正确5次</div>
                                    <div style="background: #ffd700; height: 4px; border-radius: 2px; margin-top: 8px; overflow: hidden;">
                                        <div style="background: #fff; height: 100%; width: 80%; border-radius: 2px;"></div>
                                    </div>
                                </div>

                                <div style="background: rgba(156, 39, 176, 0.05); border-radius: 12px; padding: 16px; text-align: center; border: 2px dashed rgba(156, 39, 176, 0.3);">
                                    <div style="font-size: 32px; margin-bottom: 8px; opacity: 0.5;">👑</div>
                                    <div style="color: #666; font-size: 12px; font-weight: 600; margin-bottom: 4px;">影响力之王</div>
                                    <div style="color: #999; font-size: 10px;">达到1000影响力</div>
                                    <div style="background: #e0e0e0; height: 4px; border-radius: 2px; margin-top: 8px; overflow: hidden;">
                                        <div style="background: #9c27b0; height: 100%; width: 35%; border-radius: 2px;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button class="artistic-btn" style="width: 100%; background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);">
                            📊 查看详细统计
                        </button>
                    </div>
                </div>
            </div>
            <div class="phone-label">历史记录页面</div>
        </div>

        <!-- 个人中心页面 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="page-content" style="background: linear-gradient(180deg, #f6f0ff 0%, #f0e6ff 100%);">
                    <div class="page-header" style="background: linear-gradient(135deg, #722ed1 0%, #9254de 100%); position: relative; overflow: hidden;">
                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.2) 0%, transparent 60%);"></div>
                        <svg style="position: relative; z-index: 1;" width="24" height="24" viewBox="0 0 24 24" fill="white">
                            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 7.5V9M15 11.5C15.8 11.5 16.5 12.2 16.5 13S15.8 14.5 15 14.5 13.5 13.8 13.5 13 14.2 11.5 15 11.5M5 12C5.8 12 6.5 12.7 6.5 13.5S5.8 15 5 15 3.5 14.3 3.5 13.5 4.2 12 5 12M12 13.5C12.8 13.5 13.5 14.2 13.5 15S12.8 16.5 12 16.5 10.5 15.8 10.5 15 11.2 13.5 12 13.5M12 20C16 20 20 16 20 12S16 4 12 4 4 8 4 12 8 20 12 20Z"/>
                        </svg>
                        <div class="page-title neon-text" style="position: relative; z-index: 1;">👤 个人中心</div>
                        <svg style="position: relative; z-index: 1;" width="24" height="24" viewBox="0 0 24 24" fill="white">
                            <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
                        </svg>
                    </div>
                    <div class="page-body" style="padding: 0;">
                        <div style="background: linear-gradient(135deg, #722ed1 0%, #9254de 100%); padding: 24px; color: white; text-align: center; position: relative; overflow: hidden;">
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.15) 0%, transparent 60%);"></div>
                            <div style="position: relative; z-index: 1;">
                                <div style="width: 80px; height: 80px; background: linear-gradient(45deg, #fff, #f0f0f0); border-radius: 50%; margin: 0 auto 16px; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2); border: 4px solid white;">
                                    <span style="color: #722ed1; font-size: 36px; font-weight: bold;">游</span>
                                </div>
                                <h2 style="font-size: 20px; font-weight: bold; margin-bottom: 4px;">游客MdEg</h2>
                                <p style="font-size: 14px; opacity: 0.9; margin-bottom: 16px;">推理游戏爱好者</p>
                                <div style="display: flex; justify-content: center; gap: 16px;">
                                    <div style="text-align: center;">
                                        <div style="color: white; font-size: 20px; font-weight: bold;">385</div>
                                        <div style="color: rgba(255,255,255,0.8); font-size: 12px;">影响力</div>
                                    </div>
                                    <div style="text-align: center;">
                                        <div style="color: white; font-size: 20px; font-weight: bold;">15</div>
                                        <div style="color: rgba(255,255,255,0.8); font-size: 12px;">游戏场次</div>
                                    </div>
                                    <div style="text-align: center;">
                                        <div style="color: white; font-size: 20px; font-weight: bold;">73%</div>
                                        <div style="color: rgba(255,255,255,0.8); font-size: 12px;">胜率</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div style="padding: 24px;">
                            <div class="artistic-card fade-in" style="margin-bottom: 20px;">
                                <h3 style="color: #333; margin-bottom: 20px; display: flex; align-items: center;">
                                    <span style="margin-right: 12px; font-size: 24px;">📊</span>
                                    数据统计
                                </h3>

                                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px; margin-bottom: 20px;">
                                    <div style="background: rgba(82, 196, 26, 0.1); border-radius: 12px; padding: 16px; text-align: center;">
                                        <div style="color: #52c41a; font-size: 24px; font-weight: bold; margin-bottom: 4px;">11</div>
                                        <div style="color: #666; font-size: 12px;">胜利场次</div>
                                    </div>
                                    <div style="background: rgba(255, 107, 107, 0.1); border-radius: 12px; padding: 16px; text-align: center;">
                                        <div style="color: #ff6b6b; font-size: 24px; font-weight: bold; margin-bottom: 4px;">4</div>
                                        <div style="color: #666; font-size: 12px;">失败场次</div>
                                    </div>
                                    <div style="background: rgba(24, 144, 255, 0.1); border-radius: 12px; padding: 16px; text-align: center;">
                                        <div style="color: #1890ff; font-size: 24px; font-weight: bold; margin-bottom: 4px;">8.7</div>
                                        <div style="color: #666; font-size: 12px;">平均评分</div>
                                    </div>
                                    <div style="background: rgba(255, 193, 7, 0.1); border-radius: 12px; padding: 16px; text-align: center;">
                                        <div style="color: #ffc107; font-size: 24px; font-weight: bold; margin-bottom: 4px;">3</div>
                                        <div style="color: #666; font-size: 12px;">MVP次数</div>
                                    </div>
                                </div>

                                <div style="background: rgba(114, 46, 209, 0.05); border-radius: 12px; padding: 16px;">
                                    <h4 style="color: #722ed1; margin-bottom: 12px; font-size: 14px;">擅长角色类型</h4>
                                    <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                                        <span style="background: rgba(114, 46, 209, 0.2); color: #722ed1; padding: 4px 12px; border-radius: 16px; font-size: 12px;">推理型</span>
                                        <span style="background: rgba(24, 144, 255, 0.2); color: #1890ff; padding: 4px 12px; border-radius: 16px; font-size: 12px;">社交型</span>
                                        <span style="background: rgba(82, 196, 26, 0.2); color: #52c41a; padding: 4px 12px; border-radius: 16px; font-size: 12px;">合作型</span>
                                    </div>
                                </div>
                            </div>

                            <div class="artistic-card fade-in" style="margin-bottom: 20px;">
                                <h3 style="color: #333; margin-bottom: 20px; display: flex; align-items: center;">
                                    <span style="margin-right: 12px; font-size: 24px;">⚙️</span>
                                    账户设置
                                </h3>

                                <div style="display: flex; flex-direction: column; gap: 16px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #f0f0f0;">
                                        <div style="display: flex; align-items: center;">
                                            <span style="margin-right: 12px; font-size: 20px;">👤</span>
                                            <div>
                                                <div style="color: #333; font-size: 14px; font-weight: 500;">修改昵称</div>
                                                <div style="color: #666; font-size: 12px;">当前: 游客MdEg</div>
                                            </div>
                                        </div>
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="#ccc">
                                            <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                        </svg>
                                    </div>

                                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #f0f0f0;">
                                        <div style="display: flex; align-items: center;">
                                            <span style="margin-right: 12px; font-size: 20px;">🎨</span>
                                            <div>
                                                <div style="color: #333; font-size: 14px; font-weight: 500;">头像设置</div>
                                                <div style="color: #666; font-size: 12px;">个性化你的形象</div>
                                            </div>
                                        </div>
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="#ccc">
                                            <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                        </svg>
                                    </div>

                                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #f0f0f0;">
                                        <div style="display: flex; align-items: center;">
                                            <span style="margin-right: 12px; font-size: 20px;">🔔</span>
                                            <div>
                                                <div style="color: #333; font-size: 14px; font-weight: 500;">消息通知</div>
                                                <div style="color: #666; font-size: 12px;">游戏邀请和好友消息</div>
                                            </div>
                                        </div>
                                        <div style="width: 44px; height: 24px; background: #0052d9; border-radius: 12px; position: relative; cursor: pointer;">
                                            <div style="width: 20px; height: 20px; background: white; border-radius: 50%; position: absolute; top: 2px; right: 2px; transition: all 0.3s;"></div>
                                        </div>
                                    </div>

                                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #f0f0f0;">
                                        <div style="display: flex; align-items: center;">
                                            <span style="margin-right: 12px; font-size: 20px;">🔒</span>
                                            <div>
                                                <div style="color: #333; font-size: 14px; font-weight: 500;">隐私设置</div>
                                                <div style="color: #666; font-size: 12px;">控制个人信息可见性</div>
                                            </div>
                                        </div>
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="#ccc">
                                            <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                        </svg>
                                    </div>

                                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0;">
                                        <div style="display: flex; align-items: center;">
                                            <span style="margin-right: 12px; font-size: 20px;">❓</span>
                                            <div>
                                                <div style="color: #333; font-size: 14px; font-weight: 500;">帮助与反馈</div>
                                                <div style="color: #666; font-size: 12px;">使用指南和问题反馈</div>
                                            </div>
                                        </div>
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="#ccc">
                                            <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>

                            <button class="artistic-btn" style="width: 100%; background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%); margin-bottom: 12px;">
                                🚪 退出登录
                            </button>

                            <div style="text-align: center; color: #999; font-size: 12px;">
                                版本 v1.2.0 | © 2024 AI推理游戏
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="phone-label">个人中心页面</div>
        </div>

        <!-- 历史记录页面 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="page-content" style="background: linear-gradient(180deg, #f0f5ff 0%, #e6f0ff 100%);">
                    <div class="page-header" style="background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%); position: relative; overflow: hidden;">
                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.2) 0%, transparent 60%);"></div>
                        <svg style="position: relative; z-index: 1;" width="24" height="24" viewBox="0 0 24 24" fill="white">
                            <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                        </svg>
                        <div class="page-title neon-text" style="position: relative; z-index: 1;">📚 历史记录</div>
                        <div style="width: 24px; position: relative; z-index: 1;"></div>
                    </div>
                    <div class="page-body">
                        <div class="artistic-card fade-in" style="margin-bottom: 20px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                <h3 style="color: #333; margin: 0; display: flex; align-items: center;">
                                    <span style="margin-right: 12px; font-size: 24px;">🎮</span>
                                    游戏历史
                                </h3>
                                <div style="background: rgba(24, 144, 255, 0.1); color: #1890ff; padding: 4px 12px; border-radius: 16px; font-size: 12px; font-weight: 500;">
                                    总计 15 场
                                </div>
                            </div>

                            <div style="display: flex; flex-direction: column; gap: 12px;">
                                <div style="background: linear-gradient(135deg, rgba(82, 196, 26, 0.1) 0%, rgba(56, 142, 60, 0.05) 100%); border-radius: 12px; padding: 16px; border-left: 4px solid #52c41a; position: relative;">
                                    <div style="position: absolute; top: 12px; right: 12px; background: #52c41a; color: white; padding: 2px 8px; border-radius: 10px; font-size: 10px; font-weight: 500;">胜利</div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <h4 style="color: #333; margin: 0; font-size: 15px; font-weight: 600;">🏰 雾夜庄园疑案</h4>
                                        <span style="color: #666; font-size: 11px;">2小时前</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <span style="color: #666; font-size: 12px;">扮演角色: 私人秘书</span>
                                        <span style="color: #52c41a; font-size: 12px; font-weight: 500;">+25 影响力</span>
                                    </div>
                                    <div style="display: flex; gap: 6px;">
                                        <span style="background: rgba(82, 196, 26, 0.2); color: #52c41a; padding: 2px 8px; border-radius: 10px; font-size: 10px;">推理正确</span>
                                        <span style="background: rgba(255, 193, 7, 0.2); color: #ffc107; padding: 2px 8px; border-radius: 10px; font-size: 10px;">MVP</span>
                                        <span style="background: rgba(24, 144, 255, 0.2); color: #1890ff; padding: 2px 8px; border-radius: 10px; font-size: 10px;">团队合作</span>
                                    </div>
                                </div>

                                <div style="background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(238, 90, 82, 0.05) 100%); border-radius: 12px; padding: 16px; border-left: 4px solid #ff6b6b; position: relative;">
                                    <div style="position: absolute; top: 12px; right: 12px; background: #ff6b6b; color: white; padding: 2px 8px; border-radius: 10px; font-size: 10px; font-weight: 500;">失败</div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <h4 style="color: #333; margin: 0; font-size: 15px; font-weight: 600;">🏫 校园悬案</h4>
                                        <span style="color: #666; font-size: 11px;">1天前</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <span style="color: #666; font-size: 12px;">扮演角色: 学生会长</span>
                                        <span style="color: #ff6b6b; font-size: 12px; font-weight: 500;">-5 影响力</span>
                                    </div>
                                    <div style="display: flex; gap: 6px;">
                                        <span style="background: rgba(255, 107, 107, 0.2); color: #ff6b6b; padding: 2px 8px; border-radius: 10px; font-size: 10px;">推理错误</span>
                                        <span style="background: rgba(156, 39, 176, 0.2); color: #9c27b0; padding: 2px 8px; border-radius: 10px; font-size: 10px;">被投出局</span>
                                    </div>
                                </div>

                                <div style="background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 143, 0, 0.05) 100%); border-radius: 12px; padding: 16px; border-left: 4px solid #ffc107; position: relative;">
                                    <div style="position: absolute; top: 12px; right: 12px; background: #ffc107; color: white; padding: 2px 8px; border-radius: 10px; font-size: 10px; font-weight: 500;">平局</div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <h4 style="color: #333; margin: 0; font-size: 15px; font-weight: 600;">🏛️ 古堡疑云</h4>
                                        <span style="color: #666; font-size: 11px;">3天前</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <span style="color: #666; font-size: 12px;">扮演角色: 管家</span>
                                        <span style="color: #ffc107; font-size: 12px; font-weight: 500;">+10 影响力</span>
                                    </div>
                                    <div style="display: flex; gap: 6px;">
                                        <span style="background: rgba(255, 193, 7, 0.2); color: #ffc107; padding: 2px 8px; border-radius: 10px; font-size: 10px;">时间耗尽</span>
                                        <span style="background: rgba(114, 46, 209, 0.2); color: #722ed1; padding: 2px 8px; border-radius: 10px; font-size: 10px;">坚持选择</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="artistic-card fade-in" style="margin-bottom: 20px;">
                            <h3 style="color: #333; margin-bottom: 20px; display: flex; align-items: center;">
                                <span style="margin-right: 12px; font-size: 24px;">👥</span>
                                好友互动
                            </h3>

                            <div style="display: flex; flex-direction: column; gap: 12px;">
                                <div style="background: rgba(102, 126, 234, 0.05); border-radius: 12px; padding: 16px; border-left: 4px solid #667eea;">
                                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                        <div style="width: 32px; height: 32px; background: linear-gradient(45deg, #667eea, #764ba2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                                            <span style="color: white; font-size: 14px;">张</span>
                                        </div>
                                        <div style="flex: 1;">
                                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                                <span style="color: #333; font-size: 14px; font-weight: 500;">张三</span>
                                                <span style="color: #666; font-size: 11px;">刚刚</span>
                                            </div>
                                            <div style="color: #666; font-size: 12px;">在"雾夜庄园疑案"中给你点赞</div>
                                        </div>
                                    </div>
                                </div>

                                <div style="background: rgba(76, 175, 80, 0.05); border-radius: 12px; padding: 16px; border-left: 4px solid #4caf50;">
                                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                        <div style="width: 32px; height: 32px; background: linear-gradient(45deg, #4caf50, #388e3c); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                                            <span style="color: white; font-size: 14px;">李</span>
                                        </div>
                                        <div style="flex: 1;">
                                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                                <span style="color: #333; font-size: 14px; font-weight: 500;">李四</span>
                                                <span style="color: #666; font-size: 11px;">10分钟前</span>
                                            </div>
                                            <div style="color: #666; font-size: 12px;">邀请你加入新游戏"神秘岛屿"</div>
                                        </div>
                                    </div>
                                </div>

                                <div style="background: rgba(233, 30, 99, 0.05); border-radius: 12px; padding: 16px; border-left: 4px solid #e91e63;">
                                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                        <div style="width: 32px; height: 32px; background: linear-gradient(45deg, #e91e63, #c2185b); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                                            <span style="color: white; font-size: 14px;">王</span>
                                        </div>
                                        <div style="flex: 1;">
                                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                                <span style="color: #333; font-size: 14px; font-weight: 500;">王五</span>
                                                <span style="color: #666; font-size: 11px;">1小时前</span>
                                            </div>
                                            <div style="color: #666; font-size: 12px;">评论了你的真心话回答</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="artistic-card fade-in" style="margin-bottom: 20px;">
                            <h3 style="color: #333; margin-bottom: 20px; display: flex; align-items: center;">
                                <span style="margin-right: 12px; font-size: 24px;">🏆</span>
                                成就系统
                            </h3>

                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px;">
                                <div style="background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%); border-radius: 12px; padding: 16px; text-align: center; border: 2px solid rgba(255, 215, 0, 0.3);">
                                    <div style="font-size: 32px; margin-bottom: 8px;">🕵️</div>
                                    <div style="color: #333; font-size: 12px; font-weight: 600; margin-bottom: 4px;">推理大师</div>
                                    <div style="color: #666; font-size: 10px;">连续推理正确5次</div>
                                    <div style="background: #ffd700; height: 4px; border-radius: 2px; margin-top: 8px; overflow: hidden;">
                                        <div style="background: #fff; height: 100%; width: 80%; border-radius: 2px;"></div>
                                    </div>
                                </div>

                                <div style="background: rgba(156, 39, 176, 0.05); border-radius: 12px; padding: 16px; text-align: center; border: 2px dashed rgba(156, 39, 176, 0.3);">
                                    <div style="font-size: 32px; margin-bottom: 8px; opacity: 0.5;">👑</div>
                                    <div style="color: #666; font-size: 12px; font-weight: 600; margin-bottom: 4px;">影响力之王</div>
                                    <div style="color: #999; font-size: 10px;">达到1000影响力</div>
                                    <div style="background: #e0e0e0; height: 4px; border-radius: 2px; margin-top: 8px; overflow: hidden;">
                                        <div style="background: #9c27b0; height: 100%; width: 35%; border-radius: 2px;"></div>
                                    </div>
                                </div>

                                <div style="background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(56, 142, 60, 0.05) 100%); border-radius: 12px; padding: 16px; text-align: center; border: 2px solid rgba(76, 175, 80, 0.3);">
                                    <div style="font-size: 32px; margin-bottom: 8px;">🤝</div>
                                    <div style="color: #333; font-size: 12px; font-weight: 600; margin-bottom: 4px;">团队合作</div>
                                    <div style="color: #666; font-size: 10px;">与好友合作获胜10次</div>
                                    <div style="background: #4caf50; height: 4px; border-radius: 2px; margin-top: 8px; overflow: hidden;">
                                        <div style="background: #fff; height: 100%; width: 100%; border-radius: 2px;"></div>
                                    </div>
                                </div>

                                <div style="background: rgba(255, 107, 107, 0.05); border-radius: 12px; padding: 16px; text-align: center; border: 2px dashed rgba(255, 107, 107, 0.3);">
                                    <div style="font-size: 32px; margin-bottom: 8px; opacity: 0.5;">💕</div>
                                    <div style="color: #666; font-size: 12px; font-weight: 600; margin-bottom: 4px;">真心话达人</div>
                                    <div style="color: #999; font-size: 10px;">获得50次真心话好评</div>
                                    <div style="background: #e0e0e0; height: 4px; border-radius: 2px; margin-top: 8px; overflow: hidden;">
                                        <div style="background: #ff6b6b; height: 100%; width: 60%; border-radius: 2px;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button class="artistic-btn" style="width: 100%; background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);">
                            📊 查看详细统计
                        </button>
                    </div>
                </div>
            </div>
            <div class="phone-label">历史记录页面</div>
        </div>

        <!-- 剧情分支投票页面 - 重新设计 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="page-content" style="background: linear-gradient(180deg, #f0f2ff 0%, #e6f0ff 100%);">
                    <div class="page-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); position: relative; overflow: hidden;">
                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 30% 40%, rgba(255, 255, 255, 0.15) 0%, transparent 70%);"></div>
                        <div style="position: relative; z-index: 1; width: 24px;"></div>
                        <div class="page-title neon-text">🎭 剧情分支</div>
                        <div style="color: white; font-size: 14px; position: relative; z-index: 1;">03:45</div>
                    </div>
                    <div class="page-body" style="padding: 0; position: relative;">
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 24px; color: white; text-align: center; position: relative; overflow: hidden;">
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);"></div>
                            <div style="position: relative; z-index: 1;">
                                <h2 style="font-size: 22px; font-weight: bold; margin-bottom: 8px; text-shadow: 0 2px 8px rgba(0,0,0,0.3);">� 关键时刻</h2>
                                <p style="font-size: 14px; opacity: 0.95; line-height: 1.6;">
                                    选择下一步行动方向，每个选择都将影响剧情发展
                                </p>
                            </div>
                        </div>

                        <div style="padding: 24px;">
                            <div class="artistic-card fade-in" style="margin-bottom: 20px;">
                                <h4 style="color: #333; margin-bottom: 16px; display: flex; align-items: center; font-size: 18px;">
                                    <span style="margin-right: 12px; font-size: 24px;">🎯</span>
                                    剧情分支投票
                                </h4>

                                <div class="vote-option fade-in" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); border: 2px solid #2196f3; margin-bottom: 16px; transform: scale(1.02); box-shadow: 0 8px 24px rgba(33, 150, 243, 0.2);">
                                    <div style="display: flex; align-items: center; margin-bottom: 12px;">
                                        <div style="width: 56px; height: 56px; background: linear-gradient(45deg, #2196f3, #1976d2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 16px; box-shadow: 0 4px 16px rgba(33, 150, 243, 0.3);">
                                            <span style="color: white; font-size: 24px;">🔍</span>
                                        </div>
                                        <div style="flex: 1;">
                                            <h4 style="color: #1976d2; margin: 0; font-size: 16px; font-weight: 600;">深入调查神秘访客</h4>
                                            <div style="display: flex; gap: 8px; margin-top: 6px;">
                                                <span style="background: rgba(33, 150, 243, 0.2); color: #1976d2; padding: 2px 10px; border-radius: 12px; font-size: 11px; font-weight: 500;">难度: 高</span>
                                                <span style="background: rgba(76, 175, 80, 0.2); color: #388e3c; padding: 2px 10px; border-radius: 12px; font-size: 11px; font-weight: 500;">线索获得: 高</span>
                                            </div>
                                        </div>
                                    </div>
                                    <p style="color: #333; font-size: 14px; line-height: 1.5; margin-bottom: 12px;">
                                        追查当晚来访的神秘人身份，可能揭露更多隐藏的秘密和关键线索
                                    </p>
                                    <div style="background: rgba(255, 255, 255, 0.8); border-radius: 12px; padding: 12px; font-size: 12px; color: #666;">
                                        <strong>当前支持:</strong> 4票 (67%) | <strong>预期结果:</strong> 获得重要线索
                                    </div>
                                </div>

                                <div class="vote-option fade-in" style="background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%); border: 2px solid #4caf50; margin-bottom: 16px;">
                                    <div style="display: flex; align-items: center; margin-bottom: 12px;">
                                        <div style="width: 56px; height: 56px; background: linear-gradient(45deg, #4caf50, #388e3c); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 16px; box-shadow: 0 4px 16px rgba(76, 175, 80, 0.3);">
                                            <span style="color: white; font-size: 24px;">🏠</span>
                                        </div>
                                        <div style="flex: 1;">
                                            <h4 style="color: #388e3c; margin: 0; font-size: 16px; font-weight: 600;">搜查庄园其他房间</h4>
                                            <div style="display: flex; gap: 8px; margin-top: 6px;">
                                                <span style="background: rgba(76, 175, 80, 0.2); color: #388e3c; padding: 2px 10px; border-radius: 12px; font-size: 11px; font-weight: 500;">难度: 低</span>
                                                <span style="background: rgba(255, 193, 7, 0.2); color: #f57c00; padding: 2px 10px; border-radius: 12px; font-size: 11px; font-weight: 500;">线索获得: 中</span>
                                            </div>
                                        </div>
                                    </div>
                                    <p style="color: #333; font-size: 14px; line-height: 1.5; margin-bottom: 12px;">
                                        系统性搜查庄园各个房间，寻找被遗漏的物证和线索
                                    </p>
                                    <div style="background: rgba(255, 255, 255, 0.8); border-radius: 12px; padding: 12px; font-size: 12px; color: #666;">
                                        <strong>当前支持:</strong> 2票 (33%) | <strong>预期结果:</strong> 发现物证
                                    </div>
                                </div>
                            </div>

                            <div style="background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%); border-radius: 20px; padding: 20px; margin: 20px 0; border: 2px solid #ff9800; position: relative; overflow: hidden;">
                                <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.3) 0%, transparent 50%);"></div>
                                <div style="position: relative; z-index: 1;">
                                    <div style="display: flex; align-items: center; margin-bottom: 16px;">
                                        <span style="font-size: 28px; margin-right: 12px;">⏱️</span>
                                        <div>
                                            <h4 style="color: #f57c00; margin: 0; font-size: 16px;">投票倒计时</h4>
                                            <p style="color: #e65100; margin: 0; font-size: 12px;">请在时间结束前做出选择</p>
                                        </div>
                                    </div>
                                    <div style="background: rgba(255, 255, 255, 0.9); border-radius: 16px; padding: 16px; text-align: center;">
                                        <div style="color: #ff9800; font-size: 32px; font-weight: bold; margin-bottom: 4px;">02:15</div>
                                        <div style="background: #ff9800; height: 6px; border-radius: 3px; overflow: hidden; margin-top: 8px;">
                                            <div style="background: linear-gradient(90deg, #fff, rgba(255,255,255,0.8)); height: 100%; width: 45%; border-radius: 3px; animation: countdown 135s linear;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <button class="artistic-btn" style="width: 100%; margin-top: 16px;">
                                🗳️ 确认投票
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="phone-label">剧情分支投票</div>
        </div>

        <!-- 投票结果与坚持机制页面 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="page-content" style="background: linear-gradient(180deg, #f0f8ff 0%, #e6f3ff 100%);">
                    <div class="page-header" style="background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%); position: relative; overflow: hidden;">
                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 20% 60%, rgba(255, 255, 255, 0.2) 0%, transparent 60%);"></div>
                        <div style="position: relative; z-index: 1; width: 24px;"></div>
                        <div class="page-title neon-text">📊 投票结果</div>
                        <div style="color: white; font-size: 14px; position: relative; z-index: 1;">00:30</div>
                    </div>
                    <div class="page-body" style="padding: 0;">
                        <div style="background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%); padding: 24px; color: white; text-align: center; position: relative; overflow: hidden;">
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 60%);"></div>
                            <div style="position: relative; z-index: 1;">
                                <h2 style="font-size: 22px; font-weight: bold; margin-bottom: 8px; text-shadow: 0 2px 8px rgba(0,0,0,0.3);">🎯 投票已结束</h2>
                                <p style="font-size: 14px; opacity: 0.95; line-height: 1.6;">
                                    大家的选择已统计完成，现在是坚持机制时间
                                </p>
                            </div>
                        </div>

                        <div style="padding: 24px;">
                            <div class="vote-result-card fade-in">
                                <div style="text-align: center; margin-bottom: 20px;">
                                    <h3 style="color: #333; font-size: 20px; margin-bottom: 8px;">🏆 获胜选项</h3>
                                    <div style="background: linear-gradient(45deg, #52c41a, #389e0d); color: white; padding: 16px; border-radius: 20px; display: inline-block; box-shadow: 0 8px 24px rgba(82, 196, 26, 0.3);">
                                        <div style="font-size: 24px; margin-bottom: 4px;">🔍</div>
                                        <div style="font-size: 16px; font-weight: 600;">深入调查神秘访客</div>
                                        <div style="font-size: 12px; opacity: 0.9; margin-top: 4px;">4票 (67%)</div>
                                    </div>
                                </div>

                                <div style="background: rgba(255, 255, 255, 0.8); border-radius: 16px; padding: 20px; margin-bottom: 20px;">
                                    <h4 style="color: #333; margin-bottom: 16px; text-align: center;">📈 投票详情</h4>
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                        <div style="display: flex; align-items: center;">
                                            <span style="font-size: 20px; margin-right: 8px;">🔍</span>
                                            <span style="color: #333; font-size: 14px;">深入调查神秘访客</span>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <div style="background: #52c41a; height: 8px; width: 80px; border-radius: 4px;"></div>
                                            <span style="color: #52c41a; font-weight: 600; font-size: 14px;">4票</span>
                                        </div>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <div style="display: flex; align-items: center;">
                                            <span style="font-size: 20px; margin-right: 8px;">🏠</span>
                                            <span style="color: #333; font-size: 14px;">搜查庄园其他房间</span>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <div style="background: #d9d9d9; height: 8px; width: 40px; border-radius: 4px;"></div>
                                            <span style="color: #666; font-weight: 600; font-size: 14px;">2票</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="persistence-panel fade-in">
                                <div style="text-align: center; margin-bottom: 20px;">
                                    <h3 style="color: #f57c00; font-size: 18px; margin-bottom: 8px;">⚡ 个人坚持机制</h3>
                                    <p style="color: #e65100; font-size: 14px; line-height: 1.5;">
                                        你可以选择坚持自己的选择，发挥个人影响力改变剧情走向
                                    </p>
                                </div>

                                <div style="background: rgba(255, 255, 255, 0.9); border-radius: 16px; padding: 20px; margin-bottom: 20px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                                        <div>
                                            <h4 style="color: #333; margin: 0; font-size: 16px;">你的影响力</h4>
                                            <p style="color: #666; margin: 0; font-size: 12px;">基于角色地位和游戏表现</p>
                                        </div>
                                        <div style="text-align: right;">
                                            <div style="color: #f57c00; font-size: 24px; font-weight: bold;">85</div>
                                            <div style="color: #666; font-size: 12px;">影响力值</div>
                                        </div>
                                    </div>
                                    <div class="influence-meter">
                                        <div class="influence-fill" style="width: 85%;"></div>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; margin-top: 8px; font-size: 11px; color: #666;">
                                        <span>低影响</span>
                                        <span>中等影响</span>
                                        <span>高影响</span>
                                    </div>
                                </div>

                                <div class="persistence-choice selected" style="margin-bottom: 12px;">
                                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                        <div style="width: 40px; height: 40px; background: linear-gradient(45deg, #52c41a, #389e0d); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                                            <span style="color: white; font-size: 18px;">✓</span>
                                        </div>
                                        <div>
                                            <h4 style="color: #333; margin: 0; font-size: 16px;">跟随集体剧情</h4>
                                            <p style="color: #666; margin: 0; font-size: 12px;">与团队保持一致</p>
                                        </div>
                                    </div>
                                    <p style="color: #333; font-size: 13px; line-height: 1.4;">
                                        接受"深入调查神秘访客"的决定，剧情按多数选择发展
                                    </p>
                                </div>

                                <div class="persistence-choice" style="margin-bottom: 12px;">
                                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                        <div style="width: 40px; height: 40px; background: linear-gradient(45deg, #ff6b6b, #ee5a52); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                                            <span style="color: white; font-size: 18px;">⚡</span>
                                        </div>
                                        <div>
                                            <h4 style="color: #333; margin: 0; font-size: 16px;">开启个人剧情线</h4>
                                            <p style="color: #666; margin: 0; font-size: 12px;">消耗影响力值 -30</p>
                                        </div>
                                    </div>
                                    <p style="color: #333; font-size: 13px; line-height: 1.4;">
                                        选择"搜查庄园其他房间"，开启独特的个人剧情发展，最终与主线汇合
                                    </p>
                                </div>
                            </div>

                            <div style="background: linear-gradient(135deg, #fff2e8 0%, #ffe7d3 100%); border-radius: 20px; padding: 20px; margin: 20px 0; border: 2px solid #ff7a45; position: relative; overflow: hidden;">
                                <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.4) 0%, transparent 50%);"></div>
                                <div style="position: relative; z-index: 1;">
                                    <div style="display: flex; align-items: center; margin-bottom: 16px;">
                                        <span style="font-size: 28px; margin-right: 12px;">⏰</span>
                                        <div>
                                            <h4 style="color: #d4380d; margin: 0; font-size: 16px;">坚持机制倒计时</h4>
                                            <p style="color: #ad2102; margin: 0; font-size: 12px;">请在时间结束前做出最终决定</p>
                                        </div>
                                    </div>
                                    <div style="background: rgba(255, 255, 255, 0.9); border-radius: 16px; padding: 16px; text-align: center;">
                                        <div style="color: #ff7a45; font-size: 32px; font-weight: bold; margin-bottom: 4px;">00:30</div>
                                        <div style="background: #ff7a45; height: 6px; border-radius: 3px; overflow: hidden; margin-top: 8px;">
                                            <div style="background: linear-gradient(90deg, #fff, rgba(255,255,255,0.8)); height: 100%; width: 50%; border-radius: 3px; animation: countdown 30s linear;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <button class="artistic-btn" style="width: 100%; margin-top: 16px; background: linear-gradient(135deg, #ff7a45 0%, #d4380d 100%);">
                                ⚡ 确认坚持选择
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="phone-label">投票结果与坚持机制</div>
        </div>

        <!-- 投票阶段页面 - 艺术化重新设计 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="page-content">
                    <div class="page-header" style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);">
                        <div style="width: 24px;"></div>
                        <div class="page-title">🗳️ 关键投票</div>
                        <div style="color: white; font-size: 14px;">02:00</div>
                    </div>
                    <div class="page-body" style="background: linear-gradient(180deg, #fff5f5 0%, #ffe7e7 100%); padding: 0;">
                        <div style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%); padding: 20px; color: white; text-align: center; position: relative;">
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 30% 40%, rgba(255, 255, 255, 0.1) 0%, transparent 50%); pointer-events: none;"></div>
                            <div style="position: relative; z-index: 1;">
                                <h2 style="font-size: 18px; font-weight: bold; margin-bottom: 8px;">⚖️ 决定性时刻</h2>
                                <p style="font-size: 14px; opacity: 0.9;">你的选择将影响剧情走向</p>
                            </div>
                        </div>

                        <div style="padding: 20px;">
                            <div style="background: rgba(255, 107, 107, 0.1); border-radius: 16px; padding: 16px; margin-bottom: 20px; border: 2px solid rgba(255, 107, 107, 0.2);">
                                <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 12px;">
                                    <div style="background: linear-gradient(45deg, #ff6b6b, #ee5a52); width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                                        <span style="color: white; font-size: 20px;">🎯</span>
                                    </div>
                                    <div>
                                        <h3 style="color: #d32f2f; margin: 0; font-size: 16px;">投票主题</h3>
                                        <p style="color: #f57c00; margin: 0; font-size: 12px;">第3轮 - 最终审判</p>
                                    </div>
                                </div>
                                <div style="background: rgba(255, 255, 255, 0.8); border-radius: 12px; padding: 12px; text-align: center;">
                                    <h4 style="color: #d32f2f; margin-bottom: 8px;">🔍 谁是真正的凶手？</h4>
                                    <p style="color: #666; font-size: 13px; line-height: 1.4;">
                                        基于所有线索和讨论，做出你的最终判断
                                    </p>
                                </div>
                            </div>

                            <div class="vote-option fade-in" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); border: 2px solid #2196f3; margin-bottom: 12px;">
                                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                    <div style="width: 50px; height: 50px; background: linear-gradient(45deg, #2196f3, #1976d2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                                        <span style="color: white; font-size: 20px;">🕵️</span>
                                    </div>
                                    <div>
                                        <h4 style="color: #1976d2; margin: 0;">张三 (侦探)</h4>
                                        <div style="display: flex; gap: 8px; margin-top: 4px;">
                                            <span style="background: rgba(33, 150, 243, 0.2); color: #1976d2; padding: 2px 8px; border-radius: 12px; font-size: 10px;">推理专家</span>
                                            <span style="background: rgba(255, 193, 7, 0.2); color: #f57c00; padding: 2px 8px; border-radius: 12px; font-size: 10px;">线索掌握者</span>
                                        </div>
                                    </div>
                                </div>
                                <p style="color: #333; font-size: 13px; line-height: 1.4; margin-bottom: 8px;">
                                    作为侦探，他对案件了解最多，也最有动机隐瞒真相。而且他一直在引导大家的思路...
                                </p>
                                <div style="background: rgba(255, 255, 255, 0.7); border-radius: 8px; padding: 8px; font-size: 11px; color: #666;">
                                    <strong>当前票数:</strong> 2票 | <strong>支持率:</strong> 33%
                                </div>
                            </div>

                            <div class="vote-option selected fade-in" style="background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%); border: 2px solid #4caf50; margin-bottom: 12px; transform: scale(1.02); box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);">
                                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                    <div style="width: 50px; height: 50px; background: linear-gradient(45deg, #4caf50, #388e3c); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                                        <span style="color: white; font-size: 20px;">👨‍⚕️</span>
                                    </div>
                                    <div>
                                        <h4 style="color: #388e3c; margin: 0;">李四 (医生) ✓</h4>
                                        <div style="display: flex; gap: 8px; margin-top: 4px;">
                                            <span style="background: rgba(76, 175, 80, 0.2); color: #388e3c; padding: 2px 8px; border-radius: 12px; font-size: 10px;">医学专家</span>
                                            <span style="background: rgba(255, 193, 7, 0.2); color: #f57c00; padding: 2px 8px; border-radius: 12px; font-size: 10px;">现场检验</span>
                                        </div>
                                    </div>
                                </div>
                                <p style="color: #333; font-size: 13px; line-height: 1.4; margin-bottom: 8px;">
                                    他是第一个检查尸体的人，有机会伪造死因。而且他对解剖学很了解，知道如何下手。
                                </p>
                                <div style="background: rgba(255, 255, 255, 0.9); border-radius: 8px; padding: 8px; font-size: 11px; color: #666;">
                                    <strong>当前票数:</strong> 3票 | <strong>支持率:</strong> 50% | <span style="color: #4caf50; font-weight: bold;">你的选择</span>
                                </div>
                            </div>

                            <div class="vote-option fade-in" style="background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%); border: 2px solid #e91e63; margin-bottom: 12px;">
                                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                    <div style="width: 50px; height: 50px; background: linear-gradient(45deg, #e91e63, #c2185b); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                                        <span style="color: white; font-size: 20px;">👩‍💼</span>
                                    </div>
                                    <div>
                                        <h4 style="color: #c2185b; margin: 0;">王五 (秘书)</h4>
                                        <div style="display: flex; gap: 8px; margin-top: 4px;">
                                            <span style="background: rgba(233, 30, 99, 0.2); color: #c2185b; padding: 2px 8px; border-radius: 12px; font-size: 10px;">信息掌握</span>
                                            <span style="background: rgba(255, 193, 7, 0.2); color: #f57c00; padding: 2px 8px; border-radius: 12px; font-size: 10px;">内部人员</span>
                                        </div>
                                    </div>
                                </div>
                                <p style="color: #333; font-size: 13px; line-height: 1.4; margin-bottom: 8px;">
                                    她最了解庄园主的秘密和财务状况，可能因为某些利益冲突而下手。
                                </p>
                                <div style="background: rgba(255, 255, 255, 0.7); border-radius: 8px; padding: 8px; font-size: 11px; color: #666;">
                                    <strong>当前票数:</strong> 1票 | <strong>支持率:</strong> 17%
                                </div>
                            </div>

                            <div style="background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%); border-radius: 16px; padding: 16px; margin: 20px 0; border: 2px solid #ff9800;">
                                <div style="display: flex; align-items: center; margin-bottom: 12px;">
                                    <span style="font-size: 24px; margin-right: 12px;">⚡</span>
                                    <div>
                                        <h4 style="color: #f57c00; margin: 0;">坚持机制</h4>
                                        <p style="color: #e65100; margin: 0; font-size: 12px;">最后的改变机会</p>
                                    </div>
                                </div>
                                <div style="background: rgba(255, 255, 255, 0.8); border-radius: 12px; padding: 12px;">
                                    <p style="color: #333; font-size: 13px; line-height: 1.4; margin-bottom: 8px;">
                                        投票结束后，你还有<strong style="color: #f57c00;">30秒</strong>时间重新考虑并改变选择
                                    </p>
                                    <div style="background: #ff9800; height: 4px; border-radius: 2px; overflow: hidden;">
                                        <div style="background: #fff; height: 100%; width: 60%; border-radius: 2px; animation: countdown 30s linear;"></div>
                                    </div>
                                </div>
                            </div>

                            <button class="btn-primary" style="background: linear-gradient(45deg, #ff6b6b, #ee5a52); box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);">🗳️ 确认投票</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="phone-label">投票阶段 - 艺术化设计</div>
        </div>

        <!-- 最终投票结果页面 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="page-content" style="background: linear-gradient(180deg, #f0f8ff 0%, #e6f3ff 100%);">
                    <div class="page-header" style="background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%); position: relative; overflow: hidden;">
                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.2) 0%, transparent 60%);"></div>
                        <div style="position: relative; z-index: 1; width: 24px;"></div>
                        <div class="page-title neon-text" style="position: relative; z-index: 1;">📊 投票结果</div>
                        <div style="width: 24px; position: relative; z-index: 1;"></div>
                    </div>
                    <div class="page-body" style="padding: 0;">
                        <div style="background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%); padding: 24px; color: white; text-align: center; position: relative; overflow: hidden;">
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.15) 0%, transparent 60%);"></div>
                            <div style="position: relative; z-index: 1;">
                                <h2 style="font-size: 22px; font-weight: bold; margin-bottom: 8px; text-shadow: 0 2px 8px rgba(0,0,0,0.3);">🎯 投票已结束</h2>
                                <p style="font-size: 14px; opacity: 0.95; line-height: 1.6;">
                                    所有玩家的选择已统计完成
                                </p>
                            </div>
                        </div>

                        <div style="padding: 24px;">
                            <div class="vote-result-card fade-in">
                                <div style="text-align: center; margin-bottom: 20px;">
                                    <h3 style="color: #333; font-size: 20px; margin-bottom: 16px;">🏆 最终投票结果</h3>

                                    <div style="background: linear-gradient(45deg, #ff6b6b, #ee5a52); color: white; padding: 20px; border-radius: 20px; display: inline-block; box-shadow: 0 8px 24px rgba(255, 107, 107, 0.3); margin-bottom: 20px;">
                                        <div style="font-size: 32px; margin-bottom: 8px;">👨‍⚕️</div>
                                        <div style="font-size: 18px; font-weight: 600;">李四 (医生)</div>
                                        <div style="font-size: 14px; opacity: 0.9; margin-top: 4px;">获得最多票数</div>
                                        <div style="font-size: 24px; font-weight: bold; margin-top: 8px;">4票</div>
                                    </div>
                                </div>

                                <div style="background: rgba(255, 255, 255, 0.8); border-radius: 16px; padding: 20px; margin-bottom: 20px;">
                                    <h4 style="color: #333; margin-bottom: 16px; text-align: center;">📈 详细投票统计</h4>

                                    <div style="display: flex; flex-direction: column; gap: 12px;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: rgba(255, 107, 107, 0.1); border-radius: 12px; border-left: 4px solid #ff6b6b;">
                                            <div style="display: flex; align-items: center;">
                                                <span style="font-size: 20px; margin-right: 8px;">👨‍⚕️</span>
                                                <span style="color: #333; font-size: 14px; font-weight: 600;">李四 (医生)</span>
                                            </div>
                                            <div style="display: flex; align-items: center; gap: 8px;">
                                                <div style="background: #ff6b6b; height: 8px; width: 120px; border-radius: 4px;"></div>
                                                <span style="color: #ff6b6b; font-weight: 600; font-size: 14px;">4票 (67%)</span>
                                            </div>
                                        </div>

                                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: rgba(102, 126, 234, 0.1); border-radius: 12px; border-left: 4px solid #667eea;">
                                            <div style="display: flex; align-items: center;">
                                                <span style="font-size: 20px; margin-right: 8px;">🕵️</span>
                                                <span style="color: #333; font-size: 14px; font-weight: 600;">张三 (侦探)</span>
                                            </div>
                                            <div style="display: flex; align-items: center; gap: 8px;">
                                                <div style="background: #667eea; height: 8px; width: 60px; border-radius: 4px;"></div>
                                                <span style="color: #667eea; font-weight: 600; font-size: 14px;">2票 (33%)</span>
                                            </div>
                                        </div>

                                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: rgba(233, 30, 99, 0.1); border-radius: 12px; border-left: 4px solid #e91e63;">
                                            <div style="display: flex; align-items: center;">
                                                <span style="font-size: 20px; margin-right: 8px;">👩‍💼</span>
                                                <span style="color: #333; font-size: 14px; font-weight: 600;">王五 (秘书)</span>
                                            </div>
                                            <div style="display: flex; align-items: center; gap: 8px;">
                                                <div style="background: #d9d9d9; height: 8px; width: 0px; border-radius: 4px;"></div>
                                                <span style="color: #666; font-weight: 600; font-size: 14px;">0票 (0%)</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div style="background: rgba(255, 193, 7, 0.1); border-radius: 16px; padding: 16px; margin-bottom: 20px;">
                                    <h4 style="color: #f57c00; margin-bottom: 12px; text-align: center;">⚡ 坚持机制统计</h4>
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <span style="color: #333; font-size: 13px;">使用坚持机制的玩家:</span>
                                        <span style="color: #f57c00; font-size: 13px; font-weight: 600;">2人</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <span style="color: #333; font-size: 13px;">影响力消耗总计:</span>
                                        <span style="color: #f57c00; font-size: 13px; font-weight: 600;">65点</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <span style="color: #333; font-size: 13px;">个人剧情线开启:</span>
                                        <span style="color: #f57c00; font-size: 13px; font-weight: 600;">1条</span>
                                    </div>
                                </div>
                            </div>

                            <div style="background: linear-gradient(135deg, #fff2e8 0%, #ffe7d3 100%); border-radius: 20px; padding: 20px; margin: 20px 0; border: 2px solid #ff7a45; position: relative; overflow: hidden;">
                                <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.4) 0%, transparent 50%);"></div>
                                <div style="position: relative; z-index: 1; text-align: center;">
                                    <div style="color: #d4380d; font-size: 18px; font-weight: 600; margin-bottom: 8px;">🎭 剧情即将揭晓</div>
                                    <div style="color: #ad2102; font-size: 14px; line-height: 1.4;">
                                        根据投票结果，AI将为你们展示最终的真相...
                                    </div>
                                </div>
                            </div>

                            <button class="artistic-btn" style="width: 100%; margin-top: 16px; background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);">
                                🎬 查看真相揭晓
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="phone-label">最终投票结果</div>
        </div>

        <!-- 结果展示页面 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="page-content">
                    <div class="page-header">
                        <div style="width: 24px;"></div>
                        <div class="page-title">游戏结果</div>
                        <div style="width: 24px;"></div>
                    </div>
                    <div class="page-body">
                        <div class="result-summary fade-in">
                            <div class="result-title">🎉 真相大白！</div>
                            <div class="result-desc">凶手是李四(医生)，恭喜大家成功破案！</div>
                        </div>

                        <div class="card fade-in">
                            <h4 style="color: #333; margin-bottom: 16px;">🎭 角色揭晓</h4>
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px;">
                                <div style="background: #f9f9f9; border-radius: 8px; padding: 12px; text-align: center;">
                                    <div style="font-size: 20px; margin-bottom: 4px;">🕵️</div>
                                    <div style="font-size: 12px; color: #333; font-weight: 500;">张三</div>
                                    <div style="font-size: 11px; color: #666;">侦探</div>
                                </div>
                                <div style="background: #ffebee; border-radius: 8px; padding: 12px; text-align: center;">
                                    <div style="font-size: 20px; margin-bottom: 4px;">👨‍⚕️</div>
                                    <div style="font-size: 12px; color: #333; font-weight: 500;">李四</div>
                                    <div style="font-size: 11px; color: #d32f2f;">凶手</div>
                                </div>
                                <div style="background: #f9f9f9; border-radius: 8px; padding: 12px; text-align: center;">
                                    <div style="font-size: 20px; margin-bottom: 4px;">👩‍💼</div>
                                    <div style="font-size: 12px; color: #333; font-weight: 500;">王五</div>
                                    <div style="font-size: 11px; color: #666;">秘书</div>
                                </div>
                                <div style="background: #f9f9f9; border-radius: 8px; padding: 12px; text-align: center;">
                                    <div style="font-size: 20px; margin-bottom: 4px;">🌱</div>
                                    <div style="font-size: 12px; color: #333; font-weight: 500;">赵六</div>
                                    <div style="font-size: 11px; color: #666;">园丁</div>
                                </div>
                            </div>
                        </div>

                        <div class="card fade-in">
                            <h4 style="color: #333; margin-bottom: 16px;">📊 本局统计</h4>
                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-number">15</div>
                                    <div class="stat-label">游戏时长(分钟)</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">3/4</div>
                                    <div class="stat-label">投票正确</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">85</div>
                                    <div class="stat-label">参与度分数</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">2</div>
                                    <div class="stat-label">小游戏获胜</div>
                                </div>
                            </div>
                        </div>

                        <div class="card fade-in">
                            <h4 style="color: #333; margin-bottom: 12px;">🤖 AI点评</h4>
                            <div style="background: #f0f7ff; border-radius: 8px; padding: 12px;">
                                <p style="color: #0052d9; font-size: 14px; line-height: 1.5;">
                                    "作为管家，你在游戏中表现出色！善于隐藏关键信息的同时，在关键时刻提供了重要线索。你的推理能力和团队合作精神都很棒！"
                                </p>
                            </div>
                        </div>

                        <button class="btn-primary">再来一局</button>
                        <button class="btn-secondary">返回首页</button>
                    </div>
                </div>
            </div>
            <div class="phone-label">结果展示</div>
        </div>

        <!-- 游戏复盘页面 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="page-content" style="background: linear-gradient(180deg, #f9f0ff 0%, #f0e6ff 100%);">
                    <div class="page-header" style="background: linear-gradient(135deg, #9254de 0%, #722ed1 100%); position: relative; overflow: hidden;">
                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 60% 40%, rgba(255, 255, 255, 0.2) 0%, transparent 60%);"></div>
                        <svg style="position: relative; z-index: 1;" width="24" height="24" viewBox="0 0 24 24" fill="white">
                            <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                        </svg>
                        <div class="page-title neon-text" style="position: relative; z-index: 1;">📊 游戏复盘</div>
                        <div style="width: 24px; position: relative; z-index: 1;"></div>
                    </div>
                    <div class="page-body">
                        <div class="artistic-card fade-in" style="margin-bottom: 20px;">
                            <div style="text-align: center; margin-bottom: 20px;">
                                <div style="width: 80px; height: 80px; background: linear-gradient(45deg, #9254de, #722ed1); border-radius: 50%; margin: 0 auto 16px; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 24px rgba(146, 84, 222, 0.3);">
                                    <span style="color: white; font-size: 36px;">🎭</span>
                                </div>
                                <h2 style="color: #333; margin-bottom: 8px; font-size: 22px; font-weight: bold;">雾夜庄园疑案</h2>
                                <p style="color: #666; font-size: 14px;">游戏时长: 45分钟 | 难度: 中等</p>
                            </div>

                            <div style="background: rgba(146, 84, 222, 0.05); border-radius: 16px; padding: 20px;">
                                <h3 style="color: #9254de; margin-bottom: 16px; text-align: center;">🏆 最终结果</h3>
                                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px;">
                                    <div style="background: rgba(82, 196, 26, 0.1); border-radius: 12px; padding: 16px; text-align: center;">
                                        <div style="color: #52c41a; font-size: 24px; font-weight: bold; margin-bottom: 4px;">✓</div>
                                        <div style="color: #333; font-size: 12px; font-weight: 600;">推理成功</div>
                                        <div style="color: #666; font-size: 10px;">找出真凶</div>
                                    </div>
                                    <div style="background: rgba(255, 193, 7, 0.1); border-radius: 12px; padding: 16px; text-align: center;">
                                        <div style="color: #ffc107; font-size: 24px; font-weight: bold; margin-bottom: 4px;">4/6</div>
                                        <div style="color: #333; font-size: 12px; font-weight: 600;">投票正确</div>
                                        <div style="color: #666; font-size: 10px;">玩家数量</div>
                                    </div>
                                    <div style="background: rgba(24, 144, 255, 0.1); border-radius: 12px; padding: 16px; text-align: center;">
                                        <div style="color: #1890ff; font-size: 24px; font-weight: bold; margin-bottom: 4px;">92</div>
                                        <div style="color: #333; font-size: 12px; font-weight: 600;">团队评分</div>
                                        <div style="color: #666; font-size: 10px;">满分100</div>
                                    </div>
                                    <div style="background: rgba(235, 47, 150, 0.1); border-radius: 12px; padding: 16px; text-align: center;">
                                        <div style="color: #eb2f96; font-size: 24px; font-weight: bold; margin-bottom: 4px;">3</div>
                                        <div style="color: #333; font-size: 12px; font-weight: 600;">剧情分支</div>
                                        <div style="color: #666; font-size: 10px;">触发数量</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="artistic-card fade-in" style="margin-bottom: 20px;">
                            <h3 style="color: #333; margin-bottom: 20px; display: flex; align-items: center;">
                                <span style="margin-right: 12px; font-size: 24px;">👥</span>
                                玩家表现分析
                            </h3>

                            <div style="display: flex; flex-direction: column; gap: 16px;">
                                <div style="background: rgba(102, 126, 234, 0.05); border-radius: 12px; padding: 16px; border-left: 4px solid #667eea;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 32px; height: 32px; background: linear-gradient(45deg, #667eea, #764ba2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                                                <span style="color: white; font-size: 14px;">游</span>
                                            </div>
                                            <div>
                                                <div style="color: #333; font-size: 14px; font-weight: 600;">游客MdEg (管家)</div>
                                                <div style="color: #666; font-size: 12px;">你的角色</div>
                                            </div>
                                        </div>
                                        <div style="text-align: right;">
                                            <div style="color: #667eea; font-size: 16px; font-weight: bold;">95分</div>
                                            <div style="background: #ffd700; color: #333; padding: 2px 8px; border-radius: 10px; font-size: 10px; font-weight: 500;">MVP</div>
                                        </div>
                                    </div>
                                    <div style="display: flex; gap: 6px; margin-bottom: 8px;">
                                        <span style="background: rgba(82, 196, 26, 0.2); color: #52c41a; padding: 2px 8px; border-radius: 10px; font-size: 10px;">推理正确</span>
                                        <span style="background: rgba(255, 193, 7, 0.2); color: #ffc107; padding: 2px 8px; border-radius: 10px; font-size: 10px;">线索贡献</span>
                                        <span style="background: rgba(24, 144, 255, 0.2); color: #1890ff; padding: 2px 8px; border-radius: 10px; font-size: 10px;">团队合作</span>
                                    </div>
                                    <div style="background: rgba(255, 255, 255, 0.8); border-radius: 8px; padding: 8px; font-size: 11px; color: #666;">
                                        <strong>AI评价:</strong> 出色的角色扮演和推理能力，在关键时刻提供了重要线索，是团队获胜的关键因素。
                                    </div>
                                </div>

                                <div style="background: rgba(76, 175, 80, 0.05); border-radius: 12px; padding: 16px; border-left: 4px solid #4caf50;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 32px; height: 32px; background: linear-gradient(45deg, #4caf50, #388e3c); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                                                <span style="color: white; font-size: 14px;">张</span>
                                            </div>
                                            <div>
                                                <div style="color: #333; font-size: 14px; font-weight: 600;">张三 (侦探)</div>
                                                <div style="color: #666; font-size: 12px;">推理专家</div>
                                            </div>
                                        </div>
                                        <div style="text-align: right;">
                                            <div style="color: #4caf50; font-size: 16px; font-weight: bold;">88分</div>
                                            <div style="background: #52c41a; color: white; padding: 2px 8px; border-radius: 10px; font-size: 10px; font-weight: 500;">优秀</div>
                                        </div>
                                    </div>
                                    <div style="display: flex; gap: 6px; margin-bottom: 8px;">
                                        <span style="background: rgba(82, 196, 26, 0.2); color: #52c41a; padding: 2px 8px; border-radius: 10px; font-size: 10px;">逻辑清晰</span>
                                        <span style="background: rgba(24, 144, 255, 0.2); color: #1890ff; padding: 2px 8px; border-radius: 10px; font-size: 10px;">引导讨论</span>
                                    </div>
                                </div>

                                <div style="background: rgba(255, 107, 107, 0.05); border-radius: 12px; padding: 16px; border-left: 4px solid #ff6b6b;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 32px; height: 32px; background: linear-gradient(45deg, #ff6b6b, #ee5a52); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                                                <span style="color: white; font-size: 14px;">李</span>
                                            </div>
                                            <div>
                                                <div style="color: #333; font-size: 14px; font-weight: 600;">李四 (医生)</div>
                                                <div style="color: #666; font-size: 12px;">真正凶手</div>
                                            </div>
                                        </div>
                                        <div style="text-align: right;">
                                            <div style="color: #ff6b6b; font-size: 16px; font-weight: bold;">76分</div>
                                            <div style="background: #ff6b6b; color: white; padding: 2px 8px; border-radius: 10px; font-size: 10px; font-weight: 500;">败北</div>
                                        </div>
                                    </div>
                                    <div style="display: flex; gap: 6px; margin-bottom: 8px;">
                                        <span style="background: rgba(255, 107, 107, 0.2); color: #ff6b6b; padding: 2px 8px; border-radius: 10px; font-size: 10px;">身份暴露</span>
                                        <span style="background: rgba(156, 39, 176, 0.2); color: #9c27b0; padding: 2px 8px; border-radius: 10px; font-size: 10px;">演技出色</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="artistic-card fade-in" style="margin-bottom: 20px;">
                            <h3 style="color: #333; margin-bottom: 20px; display: flex; align-items: center;">
                                <span style="margin-right: 12px; font-size: 24px;">📈</span>
                                游戏进程回顾
                            </h3>

                            <div style="position: relative;">
                                <div style="position: absolute; left: 20px; top: 0; bottom: 0; width: 2px; background: linear-gradient(to bottom, #9254de, #722ed1); border-radius: 1px;"></div>

                                <div style="display: flex; flex-direction: column; gap: 16px; padding-left: 50px;">
                                    <div style="position: relative;">
                                        <div style="position: absolute; left: -38px; top: 4px; width: 12px; height: 12px; background: #52c41a; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 8px rgba(0,0,0,0.1);"></div>
                                        <div style="background: rgba(82, 196, 26, 0.1); border-radius: 12px; padding: 12px;">
                                            <div style="color: #333; font-size: 13px; font-weight: 600; margin-bottom: 4px;">第1轮讨论 (10分钟)</div>
                                            <div style="color: #666; font-size: 12px;">初步信息收集，各角色展示线索</div>
                                        </div>
                                    </div>

                                    <div style="position: relative;">
                                        <div style="position: absolute; left: -38px; top: 4px; width: 12px; height: 12px; background: #1890ff; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 8px rgba(0,0,0,0.1);"></div>
                                        <div style="background: rgba(24, 144, 255, 0.1); border-radius: 12px; padding: 12px;">
                                            <div style="color: #333; font-size: 13px; font-weight: 600; margin-bottom: 4px;">真心话环节 (8分钟)</div>
                                            <div style="color: #666; font-size: 12px;">深度社交互动，获得额外线索</div>
                                        </div>
                                    </div>

                                    <div style="position: relative;">
                                        <div style="position: absolute; left: -38px; top: 4px; width: 12px; height: 12px; background: #ffc107; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 8px rgba(0,0,0,0.1);"></div>
                                        <div style="background: rgba(255, 193, 7, 0.1); border-radius: 12px; padding: 12px;">
                                            <div style="color: #333; font-size: 13px; font-weight: 600; margin-bottom: 4px;">剧情分支投票 (5分钟)</div>
                                            <div style="color: #666; font-size: 12px;">选择深入调查神秘访客</div>
                                        </div>
                                    </div>

                                    <div style="position: relative;">
                                        <div style="position: absolute; left: -38px; top: 4px; width: 12px; height: 12px; background: #eb2f96; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 8px rgba(0,0,0,0.1);"></div>
                                        <div style="background: rgba(235, 47, 150, 0.1); border-radius: 12px; padding: 12px;">
                                            <div style="color: #333; font-size: 13px; font-weight: 600; margin-bottom: 4px;">小游戏互动 (6分钟)</div>
                                            <div style="color: #666; font-size: 12px;">你画我猜，增进团队默契</div>
                                        </div>
                                    </div>

                                    <div style="position: relative;">
                                        <div style="position: absolute; left: -38px; top: 4px; width: 12px; height: 12px; background: #ff6b6b; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 8px rgba(0,0,0,0.1);"></div>
                                        <div style="background: rgba(255, 107, 107, 0.1); border-radius: 12px; padding: 12px;">
                                            <div style="color: #333; font-size: 13px; font-weight: 600; margin-bottom: 4px;">最终投票 (3分钟)</div>
                                            <div style="color: #666; font-size: 12px;">成功识别真凶李四</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="artistic-card fade-in" style="margin-bottom: 20px;">
                            <h3 style="color: #333; margin-bottom: 20px; display: flex; align-items: center;">
                                <span style="margin-right: 12px; font-size: 24px;">🤖</span>
                                AI总结点评
                            </h3>
                            <div style="background: rgba(146, 84, 222, 0.05); border-radius: 16px; padding: 20px;">
                                <div style="color: #333; font-size: 14px; line-height: 1.6; margin-bottom: 16px;">
                                    <strong style="color: #9254de;">本局亮点：</strong><br>
                                    团队协作出色，在真心话环节获得了关键突破。管家角色的线索分享时机把握得当，侦探的逻辑推理清晰有力。剧情分支的选择也很明智，为最终破案奠定了基础。
                                </div>
                                <div style="color: #333; font-size: 14px; line-height: 1.6; margin-bottom: 16px;">
                                    <strong style="color: #9254de;">改进建议：</strong><br>
                                    部分玩家在讨论阶段参与度不够高，建议更积极地分享观点。坚持机制的使用可以更加大胆，能够创造更多意想不到的剧情发展。
                                </div>
                                <div style="background: rgba(255, 255, 255, 0.8); border-radius: 12px; padding: 12px; text-align: center;">
                                    <div style="color: #9254de; font-size: 16px; font-weight: 600; margin-bottom: 4px;">综合评价</div>
                                    <div style="color: #333; font-size: 24px; font-weight: bold;">A级</div>
                                    <div style="color: #666; font-size: 12px;">优秀的团队表现</div>
                                </div>
                            </div>
                        </div>

                        <div style="display: flex; gap: 12px;">
                            <button class="artistic-btn" style="flex: 1; background: linear-gradient(135deg, #9254de 0%, #722ed1 100%);">
                                📤 分享复盘
                            </button>
                            <button class="artistic-btn" style="flex: 1; background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);">
                                🔄 再来一局
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="phone-label">游戏复盘页面</div>
        </div>

        <!-- 个人中心页面 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                <div class="page-content">
                    <div class="page-header">
                        <svg class="back-btn" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                        </svg>
                        <div class="page-title">个人中心</div>
                        <div style="width: 24px;"></div>
                    </div>
                    <div class="page-body">
                        <div class="card fade-in">
                            <div style="display: flex; align-items: center; margin-bottom: 20px;">
                                <div style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 32px; font-weight: bold; margin-right: 16px;">
                                    张
                                </div>
                                <div>
                                    <h3 style="color: #333; margin-bottom: 4px;">张三</h3>
                                    <p style="color: #666; font-size: 14px;">推理大师 · Lv.8</p>
                                    <div style="background: #f0f7ff; color: #0052d9; padding: 2px 8px; border-radius: 12px; font-size: 12px; display: inline-block; margin-top: 4px;">
                                        经验值: 2580/3000
                                    </div>
                                </div>
                            </div>
                            <div style="background: #f9f9f9; border-radius: 8px; padding: 12px;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                    <span style="color: #666; font-size: 14px;">本周排名</span>
                                    <span style="color: #0052d9; font-size: 14px; font-weight: 500;">#15</span>
                                </div>
                                <div style="background: #e0e0e0; height: 6px; border-radius: 3px; overflow: hidden;">
                                    <div style="background: #0052d9; height: 100%; width: 86%; border-radius: 3px;"></div>
                                </div>
                            </div>
                        </div>

                        <div class="card fade-in">
                            <h4 style="color: #333; margin-bottom: 16px;">📈 游戏统计</h4>
                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-number">42</div>
                                    <div class="stat-label">总游戏局数</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">68%</div>
                                    <div class="stat-label">胜率</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">156</div>
                                    <div class="stat-label">真心话获赞</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">23</div>
                                    <div class="stat-label">小游戏胜利</div>
                                </div>
                            </div>
                        </div>

                        <div class="card fade-in">
                            <h4 style="color: #333; margin-bottom: 16px;">🏆 成就徽章</h4>
                            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px;">
                                <div style="text-align: center; padding: 12px;">
                                    <div style="font-size: 32px; margin-bottom: 8px;">🕵️</div>
                                    <div style="font-size: 12px; color: #333; font-weight: 500;">推理高手</div>
                                </div>
                                <div style="text-align: center; padding: 12px;">
                                    <div style="font-size: 32px; margin-bottom: 8px;">🎨</div>
                                    <div style="font-size: 12px; color: #333; font-weight: 500;">绘画达人</div>
                                </div>
                                <div style="text-align: center; padding: 12px;">
                                    <div style="font-size: 32px; margin-bottom: 8px;">💬</div>
                                    <div style="font-size: 12px; color: #333; font-weight: 500;">真心话王</div>
                                </div>
                                <div style="text-align: center; padding: 12px;">
                                    <div style="font-size: 32px; margin-bottom: 8px; opacity: 0.3;">🏅</div>
                                    <div style="font-size: 12px; color: #999;">连胜达人</div>
                                </div>
                                <div style="text-align: center; padding: 12px;">
                                    <div style="font-size: 32px; margin-bottom: 8px; opacity: 0.3;">👑</div>
                                    <div style="font-size: 12px; color: #999;">推理之王</div>
                                </div>
                                <div style="text-align: center; padding: 12px;">
                                    <div style="font-size: 32px; margin-bottom: 8px; opacity: 0.3;">🌟</div>
                                    <div style="font-size: 12px; color: #999;">全能玩家</div>
                                </div>
                            </div>
                        </div>

                        <div class="card fade-in">
                            <h4 style="color: #333; margin-bottom: 16px;">⚙️ 设置</h4>
                            <div style="display: flex; flex-direction: column; gap: 16px;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span style="color: #333; font-size: 14px;">音效开关</span>
                                    <div style="width: 44px; height: 24px; background: #0052d9; border-radius: 12px; position: relative; cursor: pointer;">
                                        <div style="width: 20px; height: 20px; background: white; border-radius: 50%; position: absolute; top: 2px; right: 2px; transition: all 0.3s ease;"></div>
                                    </div>
                                </div>
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span style="color: #333; font-size: 14px;">震动反馈</span>
                                    <div style="width: 44px; height: 24px; background: #e0e0e0; border-radius: 12px; position: relative; cursor: pointer;">
                                        <div style="width: 20px; height: 20px; background: white; border-radius: 50%; position: absolute; top: 2px; left: 2px; transition: all 0.3s ease;"></div>
                                    </div>
                                </div>
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span style="color: #333; font-size: 14px;">推送通知</span>
                                    <div style="width: 44px; height: 24px; background: #0052d9; border-radius: 12px; position: relative; cursor: pointer;">
                                        <div style="width: 20px; height: 20px; background: white; border-radius: 50%; position: absolute; top: 2px; right: 2px; transition: all 0.3s ease;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card fade-in">
                            <div style="display: flex; flex-direction: column; gap: 12px;">
                                <button style="background: transparent; color: #666; border: none; padding: 12px; text-align: left; font-size: 14px; cursor: pointer;">📋 游戏规则</button>
                                <button style="background: transparent; color: #666; border: none; padding: 12px; text-align: left; font-size: 14px; cursor: pointer;">❓ 帮助与反馈</button>
                                <button style="background: transparent; color: #666; border: none; padding: 12px; text-align: left; font-size: 14px; cursor: pointer;">📄 隐私政策</button>
                                <button style="background: transparent; color: #d32f2f; border: none; padding: 12px; text-align: left; font-size: 14px; cursor: pointer;">🚪 退出登录</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="phone-label">个人中心</div>
        </div>
    </div>

    <script>
        // 增强的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 投票选项点击效果
            const voteOptions = document.querySelectorAll('.vote-option');
            voteOptions.forEach(option => {
                option.addEventListener('click', function() {
                    voteOptions.forEach(opt => {
                        opt.classList.remove('selected');
                        opt.style.transform = '';
                        opt.style.boxShadow = '';
                    });
                    this.classList.add('selected');
                    this.style.transform = 'scale(1.02)';
                    this.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)';
                });
            });

            // 按钮点击效果增强
            const buttons = document.querySelectorAll('.btn-primary, .btn-secondary, .action-btn');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    this.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.2)';

                    setTimeout(() => {
                        this.style.transform = '';
                        this.style.boxShadow = '';
                    }, 150);

                    // 添加涟漪效果
                    const ripple = document.createElement('div');
                    ripple.style.position = 'absolute';
                    ripple.style.borderRadius = '50%';
                    ripple.style.background = 'rgba(255, 255, 255, 0.6)';
                    ripple.style.transform = 'scale(0)';
                    ripple.style.animation = 'ripple 0.6s linear';
                    ripple.style.left = '50%';
                    ripple.style.top = '50%';
                    ripple.style.width = '20px';
                    ripple.style.height = '20px';
                    ripple.style.marginLeft = '-10px';
                    ripple.style.marginTop = '-10px';

                    this.style.position = 'relative';
                    this.style.overflow = 'hidden';
                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // 聊天标签切换
            const chatTabs = document.querySelectorAll('.chat-tab');
            chatTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    chatTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');

                    // 模拟切换内容
                    const chatMessages = document.querySelector('.chat-messages');
                    if (chatMessages) {
                        chatMessages.style.opacity = '0.5';
                        setTimeout(() => {
                            chatMessages.style.opacity = '1';
                        }, 200);
                    }
                });
            });

            // 语音按钮效果
            const voiceBtns = document.querySelectorAll('.voice-btn');
            voiceBtns.forEach(btn => {
                btn.addEventListener('mousedown', function() {
                    this.classList.add('recording');
                    this.innerHTML = '🔴';
                });

                btn.addEventListener('mouseup', function() {
                    this.classList.remove('recording');
                    this.innerHTML = '🎤';
                });

                btn.addEventListener('mouseleave', function() {
                    this.classList.remove('recording');
                    this.innerHTML = '🎤';
                });
            });

            // 聊天输入框回车发送
            const chatInputs = document.querySelectorAll('.chat-input input, .voice-controls input');
            chatInputs.forEach(input => {
                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        const message = this.value.trim();
                        if (message) {
                            // 模拟发送消息
                            console.log('发送消息:', message);
                            this.value = '';

                            // 添加发送动画
                            this.style.background = 'rgba(0, 82, 217, 0.1)';
                            setTimeout(() => {
                                this.style.background = '';
                            }, 300);
                        }
                    }
                });
            });

            // 开关切换效果
            const toggles = document.querySelectorAll('[style*="width: 44px"]');
            toggles.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    const isOn = this.style.background.includes('#0052d9');
                    const circle = this.querySelector('div');

                    if (isOn) {
                        this.style.background = '#e0e0e0';
                        circle.style.left = '2px';
                        circle.style.right = 'auto';
                    } else {
                        this.style.background = '#0052d9';
                        circle.style.right = '2px';
                        circle.style.left = 'auto';
                    }

                    // 添加切换动画
                    this.style.transform = 'scale(1.1)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 200);
                });
            });

            // 玩家卡片悬停效果
            const playerSlots = document.querySelectorAll('.player-slot');
            playerSlots.forEach(slot => {
                slot.addEventListener('mouseenter', function() {
                    if (this.classList.contains('occupied')) {
                        this.style.transform = 'translateY(-5px) scale(1.05)';
                        this.style.boxShadow = '0 12px 30px rgba(0, 0, 0, 0.2)';
                    }
                });

                slot.addEventListener('mouseleave', function() {
                    this.style.transform = '';
                    this.style.boxShadow = '';
                });
            });

            // 添加涟漪动画CSS
            const style = document.createElement('style');
            style.textContent = `
                @keyframes ripple {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }

                .message {
                    transition: all 0.3s ease;
                }

                .message:hover {
                    transform: translateX(5px);
                }

                .card {
                    transition: all 0.3s ease;
                }

                .card:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                }
            `;
            document.head.appendChild(style);

            // 自动滚动效果
            const containers = document.querySelectorAll('.container');
            containers.forEach(container => {
                let isScrolling = false;

                container.addEventListener('wheel', function(e) {
                    if (!isScrolling) {
                        isScrolling = true;
                        setTimeout(() => {
                            isScrolling = false;
                        }, 100);
                    }
                });
            });

            // 真心话页面字符计数
            const textarea = document.querySelector('textarea');
            const charCount = document.getElementById('charCount');
            if (textarea && charCount) {
                textarea.addEventListener('input', function() {
                    const count = this.value.length;
                    charCount.textContent = `${count}/500字`;

                    if (count > 500) {
                        charCount.style.color = '#ff4d4f';
                        this.style.borderColor = '#ff4d4f';
                    } else if (count > 400) {
                        charCount.style.color = '#faad14';
                        this.style.borderColor = '#faad14';
                    } else {
                        charCount.style.color = '#666';
                        this.style.borderColor = '#d9d9d9';
                    }
                });
            }

            // 坚持机制选择效果
            const persistenceChoices = document.querySelectorAll('.persistence-choice');
            persistenceChoices.forEach(choice => {
                choice.addEventListener('click', function() {
                    persistenceChoices.forEach(c => c.classList.remove('selected'));
                    this.classList.add('selected');

                    // 添加选择动画
                    this.style.transform = 'scale(1.05)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 200);
                });
            });
        });
    </script>
</body>
</html>