<!--房间大厅页面-->
<view class="lobby-container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <t-icon name="chevron-left" size="48rpx" color="white" bind:tap="goBack" />
      <view class="room-title">{{roomInfo.roomId}}房间</view>
      <t-icon name="more" size="48rpx" color="white" bind:tap="showRoomMenu" />
    </view>
  </view>

  <!-- 房间信息卡片 -->
  <view class="room-info-section">
    <view class="room-info-card">
      <view class="game-mode-info">
        <view class="mode-title">{{roomInfo.gameMode}}</view>
        <view class="mode-details">
          <t-tag theme="primary" size="small">⏱ {{roomInfo.timeLimit}}s</t-tag>
          <t-tag theme="warning" size="small">{{roomInfo.rounds}}局</t-tag>
        </view>
      </view>
    </view>
  </view>

  <!-- 玩家网格 -->
  <view class="players-section">
    <view class="players-grid">
      <view 
        class="player-slot {{item.status}}" 
        wx:for="{{playerSlots}}" 
        wx:key="id"
        bind:tap="onPlayerSlotTap"
        data-slot-id="{{item.id}}"
      >
        <view class="player-status-indicator" wx:if="{{item.status === 'occupied'}}"></view>
        
        <view class="player-avatar" wx:if="{{item.status === 'occupied'}}">
          <t-avatar 
            size="large" 
            image="{{item.player.avatar}}"
            alt="{{item.player.nickname}}"
          >
            {{item.player.nickname.charAt(0)}}
          </t-avatar>
        </view>
        
        <view class="empty-slot" wx:else>
          <t-icon name="add" size="48rpx" color="#ccc" />
        </view>
        
        <view class="player-info" wx:if="{{item.status === 'occupied'}}">
          <view class="player-name">{{item.player.nickname}}</view>
          <t-tag 
            theme="warning" 
            size="small" 
            wx:if="{{item.player.isHost}}"
          >
            房主
          </t-tag>
        </view>
        
        <view class="empty-info" wx:else>
          <view class="empty-text">等待加入</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 游戏阶段导航 -->
  <view class="game-phases-section" wx:if="{{gameStarted}}">
    <view class="phases-title">
      <t-icon name="play-circle" size="32rpx" color="#667eea" />
      <text>游戏阶段</text>
    </view>

    <view class="phases-grid">
      <view class="phase-card" bind:tap="goToRoleAssignment">
        <view class="phase-icon">
          <t-icon name="user-avatar" size="48rpx" color="#667eea" />
        </view>
        <view class="phase-info">
          <view class="phase-name">角色分配</view>
          <view class="phase-desc">查看你的角色</view>
        </view>
        <view class="phase-status completed" wx:if="{{phaseStatus.roleAssignment}}">
          <t-icon name="check-circle" size="24rpx" color="#52c41a" />
        </view>
      </view>

      <view class="phase-card" bind:tap="goToPrivateClues">
        <view class="phase-icon">
          <t-icon name="search" size="48rpx" color="#52c41a" />
        </view>
        <view class="phase-info">
          <view class="phase-name">私人线索</view>
          <view class="phase-desc">查看线索信息</view>
        </view>
        <view class="phase-status completed" wx:if="{{phaseStatus.privateClues}}">
          <t-icon name="check-circle" size="24rpx" color="#52c41a" />
        </view>
      </view>

      <view class="phase-card" bind:tap="goToDiscussion">
        <view class="phase-icon">
          <t-icon name="chat" size="48rpx" color="#ffc107" />
        </view>
        <view class="phase-info">
          <view class="phase-name">讨论阶段</view>
          <view class="phase-desc">与其他玩家讨论</view>
        </view>
        <view class="phase-status active" wx:if="{{currentPhase === 'discussion'}}">
          <t-icon name="time" size="24rpx" color="#ffc107" />
        </view>
        <view class="phase-status completed" wx:if="{{phaseStatus.discussion}}">
          <t-icon name="check-circle" size="24rpx" color="#52c41a" />
        </view>
      </view>

      <view class="phase-card" bind:tap="goToVoting">
        <view class="phase-icon">
          <t-icon name="thumb-up" size="48rpx" color="#ff6b6b" />
        </view>
        <view class="phase-info">
          <view class="phase-name">投票阶段</view>
          <view class="phase-desc">投票淘汰玩家</view>
        </view>
        <view class="phase-status active" wx:if="{{currentPhase === 'voting'}}">
          <t-icon name="time" size="24rpx" color="#ffc107" />
        </view>
        <view class="phase-status completed" wx:if="{{phaseStatus.voting}}">
          <t-icon name="check-circle" size="24rpx" color="#52c41a" />
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作区 -->
  <view class="lobby-actions">
    <t-button
      theme="primary"
      size="large"
      bind:tap="{{gameStarted ? 'continueGame' : 'startGame'}}"
      disabled="{{!canStart && !gameStarted && !testMode.singlePlayer}}"
      class="{{gameStarted ? 'continue-btn' : 'start-btn'}}"
      wx:if="{{isHost || gameStarted}}"
    >
      {{gameStarted ? '继续游戏' : testMode.singlePlayer ? '开始测试游戏' : '开始游戏'}}
    </t-button>

    <t-button
      theme="primary"
      size="large"
      bind:tap="onReadyToggle"
      disabled="{{!canReady}}"
      class="ready-btn"
      wx:if="{{!isHost && !gameStarted}}"
    >
      {{isReady ? '取消准备' : '准备'}}
    </t-button>

    <t-button
      theme="default"
      size="large"
      bind:tap="inviteFriends"
      class="invite-btn"
      wx:if="{{!gameStarted}}"
    >
      邀请
    </t-button>

    <t-button
      theme="default"
      size="large"
      bind:tap="showGameMenu"
      class="menu-btn"
      wx:if="{{gameStarted}}"
    >
      菜单
    </t-button>
  </view>

  <!-- 房间状态信息 -->
  <view class="room-status">
    <view class="status-item">
      <t-icon name="user" size="32rpx" color="#666" />
      <text>在线: {{onlineCount}}人</text>
    </view>
    <view class="status-item">
      <t-icon name="time" size="32rpx" color="#666" />
      <text>等待时间: {{waitingTime}}</text>
    </view>
  </view>
</view>

<!-- 房间菜单弹窗 -->
<t-popup 
  visible="{{showMenu}}" 
  placement="top"
  bind:visible-change="onMenuVisibleChange"
>
  <view class="room-menu">
    <t-cell-group>
      <t-cell title="房间设置" left-icon="setting" bind:click="openRoomSettings" />
      <t-cell title="🧪 测试功能" left-icon="tools" bind:click="openTestPanel" />
      <t-cell title="分享房间" left-icon="share" bind:click="shareRoom" />
      <t-cell title="举报房间" left-icon="error-circle" bind:click="reportRoom" />
      <t-cell title="退出房间" left-icon="logout" bind:click="leaveRoom" />
    </t-cell-group>
  </view>
</t-popup>

<!-- 邀请好友弹窗 -->
<t-dialog 
  id="invite-dialog"
  title="邀请好友"
  content="分享房间号或二维码邀请好友加入"
  confirm-btn="分享"
  cancel-btn="取消"
  bind:confirm="shareRoomCode"
/>

<!-- 测试功能面板 -->
<t-popup
  visible="{{showTestPanel}}"
  placement="bottom"
  bind:visible-change="onTestPanelVisibleChange"
>
  <view class="test-panel">
    <view class="test-panel-header">
      <text class="test-panel-title">🧪 测试功能面板</text>
      <t-icon name="close" size="32rpx" color="#666" bind:tap="closeTestPanel" />
    </view>

    <view class="test-options">
      <!-- 单人测试模式 -->
      <view class="test-option-card">
        <view class="option-header">
          <view class="option-icon">👤</view>
          <view class="option-info">
            <text class="option-title">单人测试模式</text>
            <text class="option-desc">允许单人开始游戏，自动添加AI玩家</text>
          </view>
          <t-switch
            value="{{testMode.singlePlayer}}"
            bind:change="onSinglePlayerModeChange"
            size="large"
          />
        </view>
        <view class="option-details" wx:if="{{testMode.singlePlayer}}">
          <text class="detail-text">✅ 已开启单人测试模式</text>
          <text class="detail-text">• 可以单人开始游戏</text>
          <text class="detail-text">• 自动添加AI测试玩家</text>
          <text class="detail-text">• 跳过准备等待阶段</text>
        </view>
      </view>

      <!-- 快速开始 -->
      <view class="test-option-card">
        <view class="option-header">
          <view class="option-icon">⚡</view>
          <view class="option-info">
            <text class="option-title">快速开始模式</text>
            <text class="option-desc">跳过等待，立即开始游戏</text>
          </view>
          <t-switch
            value="{{testMode.quickStart}}"
            bind:change="onQuickStartModeChange"
            size="large"
          />
        </view>
      </view>

      <!-- AI剧本生成 -->
      <view class="test-option-card">
        <view class="option-header">
          <view class="option-icon">🤖</view>
          <view class="option-info">
            <text class="option-title">AI剧本生成</text>
            <text class="option-desc">使用AI自动生成游戏剧本</text>
          </view>
          <t-switch
            value="{{testMode.aiScript}}"
            bind:change="onAIScriptModeChange"
            size="large"
          />
        </view>
      </view>
    </view>

    <view class="test-actions">
      <button class="test-action-btn secondary" bind:tap="resetTestMode">
        重置设置
      </button>
      <button
        class="test-action-btn primary"
        bind:tap="startTestGame"
        disabled="{{!testMode.singlePlayer}}"
      >
        开始测试游戏
      </button>
    </view>
  </view>
</t-popup>

<!-- 提示组件 -->
<t-toast id="t-toast" />
<t-dialog id="t-dialog" />
