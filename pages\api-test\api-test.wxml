<!-- pages/api-test/api-test.wxml -->
<view class="container">
  <view class="header">
    <text class="title">API连接测试</text>
    <text class="subtitle">测试Moonshot AI API连接状态</text>
  </view>

  <!-- API状态卡片 -->
  <view class="status-card">
    <view class="status-header">
      <text class="status-title">当前状态</text>
    </view>
    
    <view class="status-items">
      <view class="status-item">
        <text class="status-label">API密钥:</text>
        <view class="status-value {{apiKeyStatus === '已配置' ? 'success' : 'error'}}">
          {{apiKeyStatus}}
        </view>
      </view>
      
      <view class="status-item">
        <text class="status-label">连接状态:</text>
        <view class="status-value {{connectionStatus === 'success' ? 'success' : connectionStatus === 'failed' ? 'error' : 'unknown'}}">
          {{connectionStatus === 'success' ? '连接正常' : connectionStatus === 'failed' ? '连接失败' : '未测试'}}
        </view>
      </view>
    </view>
  </view>

  <!-- 测试按钮 -->
  <view class="test-buttons">
    <button 
      class="test-btn primary" 
      bindtap="testSimpleApi" 
      disabled="{{isLoading}}"
    >
      {{isLoading ? '测试中...' : '测试AI服务'}}
    </button>
    
    <button 
      class="test-btn secondary" 
      bindtap="testNetworkConnection" 
      disabled="{{isLoading}}"
    >
      {{isLoading ? '测试中...' : '测试网络连接'}}
    </button>
    
    <button 
      class="test-btn settings" 
      bindtap="goToApiSettings"
    >
      API设置
    </button>
  </view>

  <!-- 测试结果 -->
  <view class="result-section" wx:if="{{testResult}}">
    <view class="result-header">
      <text class="result-title">测试结果</text>
      <view class="result-actions">
        <button class="action-btn" bindtap="copyResult">复制</button>
        <button class="action-btn" bindtap="clearResult">清除</button>
      </view>
    </view>
    
    <view class="result-content">
      <text class="result-text">{{testResult}}</text>
    </view>
  </view>

  <!-- 使用说明 -->
  <view class="instructions">
    <view class="instruction-title">使用说明</view>
    <view class="instruction-list">
      <view class="instruction-item">
        <text class="instruction-number">1</text>
        <text class="instruction-text">确保已在API设置中配置正确的密钥</text>
      </view>
      <view class="instruction-item">
        <text class="instruction-number">2</text>
        <text class="instruction-text">点击"测试AI服务"验证完整功能</text>
      </view>
      <view class="instruction-item">
        <text class="instruction-number">3</text>
        <text class="instruction-text">如果失败，可以测试网络连接排查问题</text>
      </view>
      <view class="instruction-item">
        <text class="instruction-number">4</text>
        <text class="instruction-text">查看详细错误信息进行问题定位</text>
      </view>
    </view>
  </view>

  <!-- 加载指示器 -->
  <view class="loading-overlay" wx:if="{{isLoading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在测试连接...</text>
    </view>
  </view>
</view>
