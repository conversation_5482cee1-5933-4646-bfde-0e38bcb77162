<!--线索收集测试页面-->
<view class="container">
  <view class="header">
    <text class="title">🔍 线索收集测试</text>
    <text class="subtitle">测试AI线索生成和收集功能</text>
  </view>

  <view class="test-buttons">
    <view 
      class="test-button"
      wx:for="{{testButtons}}" 
      wx:key="id"
      bind:tap="handleButtonTap"
      data-button="{{item}}"
    >
      <view class="button-content">
        <text class="button-name">{{item.name}}</text>
        <text class="button-desc">{{item.desc}}</text>
      </view>
      <text class="button-arrow">→</text>
    </view>
  </view>

  <view class="info-section">
    <text class="info-title">📋 测试说明</text>
    <view class="info-list">
      <text class="info-item">• 直接测试：快速跳转到线索收集页面</text>
      <text class="info-item">• 带角色测试：设置完整角色信息后测试</text>
      <text class="info-item">• AI生成测试：单独测试AI线索生成功能</text>
      <text class="info-item">• 测试页面包含真实的AI生成线索</text>
    </view>
  </view>
</view>
