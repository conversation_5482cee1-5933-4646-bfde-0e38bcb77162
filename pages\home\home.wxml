<!--首页 - 地球星空背景-->
<view class="home-container" style="position: relative; left: 0rpx; top: -25rpx">
  <!-- 星星装饰 -->
  <view class="stars">
    <view class="star star-1">✦</view>
    <view class="star star-2">✧</view>
    <view class="star star-3">✦</view>
    <view class="star star-4">✧</view>
    <view class="star star-5">✦</view>
  </view>

  <!-- 极简主标题区域 -->
  <view class="hero-section" style="position: relative; left: 0rpx; top: -97rpx">
    <view class="app-logo">
      <text class="logo-text">AI推理大师</text>
    </view>
    <text class="app-subtitle">智能剧本 · 沉浸推理</text>
  </view>

  <!-- 极简操作按钮 -->
  <view class="main-actions">
    <button class="minimal-btn primary-btn" bind:tap="createRoom" style="position: relative; left: -1rpx; top: -1rpx">
      创建房间
    </button>
    <button class="minimal-btn secondary-btn" bind:tap="joinRoom">
      加入游戏
    </button>
    <button class="minimal-btn test-btn" bind:tap="quickSinglePlayerTest">
      🚀 单人测试
    </button>
    <button class="minimal-btn test-btn" bind:tap="testCluesCollection">
      🔍 线索测试
    </button>
  </view>

  <!-- 快速加入 -->
  <view class="quick-join-section">
    <input
      class="minimal-input"
      placeholder="输入房间号"
      value="{{roomIdInput}}"
      bindinput="onRoomIdChange"
      maxlength="6"
      type="number"
    />
    <button
      class="minimal-btn join-btn {{!roomIdInput ? 'disabled' : ''}}"
      bind:tap="quickJoinRoom"
      disabled="{{!roomIdInput}}"
    >
      加入
    </button>
  </view>

  <!-- 热门房间 -->
  <view class="rooms-section" wx:if="{{roomList.length > 0}}">
    <text class="section-title">热门房间</text>
    <view class="rooms-list">
      <view wx:for="{{roomList}}" wx:key="id" class="room-card" bind:tap="joinRoomById" data-room-id="{{item.id}}">
        <view class="room-info">
          <text class="room-name">{{item.name}}</text>
          <text class="room-players">{{item.currentPlayers}}/{{item.maxPlayers}}</text>
        </view>
        <view class="room-status {{item.status === 'playing' ? 'playing' : 'waiting'}}">
          {{item.status === 'playing' ? '进行中' : '等待中'}}
        </view>
      </view>
    </view>
  </view>

  <!-- 开发者测试区域 -->
  <view class="dev-test-section" wx:if="{{showDevTest}}">
    <text class="section-title">开发测试</text>
    <view class="test-buttons">
      <button class="test-btn" bind:tap="quickCreateRoom">快速创建</button>
      <button class="test-btn" bind:tap="testRoleAssignment">角色分配</button>
      <button class="test-btn" bind:tap="testRoleAssignmentFunction">🎭 角色分配测试</button>
      <button class="test-btn" bind:tap="simpleRoleTest">🧪 简单角色测试</button>
      <button class="test-btn" bind:tap="testRoomReady">🏠 房间准备流程</button>
      <button class="test-btn special" bind:tap="quickSinglePlayerTest">🚀 单人测试 (含AI剧情生成)</button>
      <button class="test-btn warning" bind:tap="testCompleteFlow">🔬 完整流程测试</button>
      <button class="test-btn" bind:tap="testPrivateClues">私人线索</button>
      <button class="test-btn" bind:tap="testDiscussion">讨论阶段</button>
      <button class="test-btn" bind:tap="testVoting">投票页面</button>
      <!-- AI剧情生成已整合到单人测试模式中 -->
      <button class="test-btn" bind:tap="testRoomFunction">🏠 房间功能测试</button>
      <button class="test-btn" bind:tap="testReadyGameFunction">🧪 测试准备开始游戏</button>
      <button class="test-btn" bind:tap="manualTestGuide">📋 手动测试指南</button>
      <button class="test-btn special" bind:tap="testPersistenceDemo">坚持机制演示</button>
      <button class="test-btn warning" bind:tap="testNetworkDiagnostic">🔍 网络诊断</button>
      <button class="test-btn danger" bind:tap="toggleDevTest">隐藏测试</button>
    </view>
  </view>

  <!-- 显示测试区按钮 -->
  <button class="dev-toggle-btn" wx:if="{{!showDevTest}}" bind:tap="toggleDevTest">
    开发测试
  </button>
</view>

<!-- 加载状态 -->
<view wx:if="{{loading}}" class="loading">
  <text>加载中...</text>
</view>
