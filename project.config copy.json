{"compileType": "miniprogram", "libVersion": "trial", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "condition": false, "swc": false, "disableSWC": true, "disableUseStrict": false, "useCompilerPlugins": false}, "condition": {}, "editorSetting": {"tabIndent": "auto", "tabSize": 2}, "appid": "wxa9e14950d650cc0c", "simulatorPluginLibVersion": {}}